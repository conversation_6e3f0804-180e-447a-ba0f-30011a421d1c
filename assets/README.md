# Assets Directory

This directory contains all visual assets for the Glidic Flutter application.

## Structure

```
assets/
├── images/          # PNG, JPG, WebP images
├── icons/           # SVG icons and vector graphics
└── fonts/           # Custom font files (TTF, OTF)
```

## Guidelines

### Images (`assets/images/`)
- Use for raster images like photos, illustrations, backgrounds
- Supported formats: PNG, JPG, WebP
- Consider providing multiple resolutions (1x, 2x, 3x) for different screen densities
- Organize in subfolders by feature or category when needed

### Icons (`assets/icons/`)
- Use for vector graphics and icons
- Preferred format: SVG for scalability
- Use semantic naming (e.g., `home_icon.svg`, `user_profile.svg`)
- Keep icons simple and consistent in style

### Fonts (`assets/fonts/`)
- Custom font files for the application
- Supported formats: TTF, OTF
- Include all required font weights and styles

## Usage

All asset paths are defined as constants in `lib/core/common/constants/path_constants.dart`.
Use these constants instead of hardcoding paths in your widgets.

Example:
```dart
import 'package:flutter_svg/flutter_svg.dart';
import 'package:glidic_app/core/common/constants/path_constants.dart';

SvgPicture.asset(PathConstants.homeIcon)
```
