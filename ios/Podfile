# Uncomment this line to define a global platform for your project
platform :ios, '16.2'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    # Set minimum deployment target for Live Activities support
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.2'
      # Remove ONLY_ACTIVE_ARCH for device builds
      # config.build_settings['ONLY_ACTIVE_ARCH'] = 'YES'
      # Fix C++ linking issues
      config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'gnu++17'
      config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'

      # Add C++ standard library linking
      other_ldflags = config.build_settings['OTHER_LDFLAGS'] || []
      other_ldflags = [other_ldflags] if other_ldflags.is_a?(String)
      other_ldflags << '-lc++'
      config.build_settings['OTHER_LDFLAGS'] = other_ldflags
    end
  end

  # Fix main app target linking issues
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      if target.name == 'Runner'
        target.build_configurations.each do |config|
          # Add C++ standard library linking to main app
          other_ldflags = config.build_settings['OTHER_LDFLAGS'] || []
          other_ldflags = [other_ldflags] if other_ldflags.is_a?(String)
          other_ldflags << '-lc++'
          other_ldflags << '-lstdc++'
          config.build_settings['OTHER_LDFLAGS'] = other_ldflags

          # Ensure C++ standard library is linked
          config.build_settings['CLANG_CXX_LIBRARY'] = 'libc++'
          config.build_settings['CLANG_CXX_LANGUAGE_STANDARD'] = 'gnu++17'
        end
      end
    end
  end
end
