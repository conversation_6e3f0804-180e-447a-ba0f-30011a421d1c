PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audio_waveforms (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_calendar (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_app_group_directory (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - live_activities (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preference_app_group (1.0.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.50.3):
    - sqlite3/common (= 3.50.3)
  - sqlite3/common (3.50.3)
  - sqlite3/dbstatvtab (3.50.3):
    - sqlite3/common
  - sqlite3/fts5 (3.50.3):
    - sqlite3/common
  - sqlite3/math (3.50.3):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.50.3):
    - sqlite3/common
  - sqlite3/rtree (3.50.3):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.50.3)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree

DEPENDENCIES:
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_group_directory (from `.symlinks/plugins/flutter_app_group_directory/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - live_activities (from `.symlinks/plugins/live_activities/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preference_app_group (from `.symlinks/plugins/shared_preference_app_group/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - sqlite3

EXTERNAL SOURCES:
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  Flutter:
    :path: Flutter
  flutter_app_group_directory:
    :path: ".symlinks/plugins/flutter_app_group_directory/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  live_activities:
    :path: ".symlinks/plugins/live_activities/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preference_app_group:
    :path: ".symlinks/plugins/shared_preference_app_group/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audio_waveforms: a6dde7fe7c0ea05f06ffbdb0f7c1b2b2ba6cedcf
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_calendar: b55b2c5406cfba45c95a59f9059156daee1f74ed
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_group_directory: 55b5362007d1c0cb45dc1dd1e94f67d615f45a6b
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  google_sign_in_ios: b48bb9af78576358a168361173155596c845f0b9
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  live_activities: 4dfa736d0736e1c77866a2f9c056a76513cc9e7b
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preference_app_group: 7422922a188e05cf680a656e18e2786dcb5c58b3
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqlite3: 83105acd294c9137c026e2da1931c30b4588ab81
  sqlite3_flutter_libs: ce0522d143cee6ef5e16587acfce8f476316e005

PODFILE CHECKSUM: 0090085901770f3dc9577754cca0514363081b94

COCOAPODS: 1.16.2
