#!/bin/bash

# <PERSON><PERSON>t to load environment variables from .env files and make them available to iOS build
# This script reads the appropriate .env file based on the build configuration and exports
# the Google OAuth configuration variables for use in .xcconfig files

set -e

# Get the project root directory (parent of ios directory)
PROJECT_ROOT="$(cd "$(dirname "$0")/../.." && pwd)"

# Always regenerate the config file to ensure it's up to date
# We'll determine the environment from multiple sources with fallbacks

FLAVOR="${FLAVOR:-}"
CONFIGURATION="${CONFIGURATION:-}"
PRODUCT_BUNDLE_IDENTIFIER="${PRODUCT_BUNDLE_IDENTIFIER:-}"

# Debug output - log to file for debugging
echo "$(date): Script executed with FLAVOR=$FLAVOR, CONFIGURATION=$CONFIGURATION, BUNDLE_ID=$PRODUCT_BUNDLE_IDENTIFIER" >> "${PROJECT_ROOT}/ios/build_debug.log"

# Determine environment based on available information with multiple fallback strategies
ENVIRONMENT=""

# Strategy 1: Use FLAVOR if available
if [ -n "$FLAVOR" ]; then
    ENVIRONMENT="$FLAVOR"
    echo "$(date): Using FLAVOR: $ENVIRONMENT" >> "${PROJECT_ROOT}/ios/build_debug.log"
# Strategy 2: Use CONFIGURATION
elif [ -n "$CONFIGURATION" ]; then
    case "${CONFIGURATION}" in
        *"Development"* | *"Debug"*)
            ENVIRONMENT="development"
            ;;
        *"Staging"*)
            ENVIRONMENT="staging"
            ;;
        *"Production"* | *"Release"*)
            ENVIRONMENT="production"
            ;;
        *)
            ENVIRONMENT="development"
            ;;
    esac
    echo "$(date): Using CONFIGURATION: $CONFIGURATION -> $ENVIRONMENT" >> "${PROJECT_ROOT}/ios/build_debug.log"
# Strategy 3: Use bundle identifier
elif [ -n "$PRODUCT_BUNDLE_IDENTIFIER" ]; then
    case "${PRODUCT_BUNDLE_IDENTIFIER}" in
        *".dev")
            ENVIRONMENT="development"
            ;;
        *".staging")
            ENVIRONMENT="staging"
            ;;
        *)
            ENVIRONMENT="production"
            ;;
    esac
    echo "$(date): Using BUNDLE_ID: $PRODUCT_BUNDLE_IDENTIFIER -> $ENVIRONMENT" >> "${PROJECT_ROOT}/ios/build_debug.log"
# Strategy 4: Default fallback
else
    ENVIRONMENT="development"
    echo "$(date): Using default: $ENVIRONMENT" >> "${PROJECT_ROOT}/ios/build_debug.log"
fi

echo "Using environment: $ENVIRONMENT"

case "${ENVIRONMENT}" in
    "development")
        ENV_FILE="${PROJECT_ROOT}/.env.development"
        ;;
    "staging")
        ENV_FILE="${PROJECT_ROOT}/.env.staging"
        ;;
    "production")
        ENV_FILE="${PROJECT_ROOT}/.env.production"
        ;;
    *)
        ENV_FILE="${PROJECT_ROOT}/.env.development"
        echo "Warning: Unknown ENVIRONMENT '${ENVIRONMENT}', defaulting to development"
        ;;
esac

# Check if the environment file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Error: Environment file $ENV_FILE not found"
    exit 1
fi

# Function to extract value from .env file
get_env_value() {
    local key="$1"
    local file="$2"
    
    # Extract the value, handling comments and whitespace
    grep "^${key}=" "$file" | head -n1 | cut -d'=' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//'
}

# Extract Google OAuth configuration from the environment file
GOOGLE_OAUTH_IOS_CLIENT_ID=$(get_env_value "GOOGLE_OAUTH_IOS_CLIENT_ID" "$ENV_FILE")

# Validate that we got the required values
if [ -z "$GOOGLE_OAUTH_IOS_CLIENT_ID" ]; then
    echo "Error: GOOGLE_OAUTH_IOS_CLIENT_ID not found in $ENV_FILE"
    exit 1
fi

# Generate the URL scheme from the client ID (reverse the domain)
GOOGLE_OAUTH_IOS_URL_SCHEME="com.googleusercontent.apps.$(echo "$GOOGLE_OAUTH_IOS_CLIENT_ID" | cut -d'.' -f1)"

# Export the variables for use in .xcconfig files
echo "GOOGLE_OAUTH_IOS_CLIENT_ID=$GOOGLE_OAUTH_IOS_CLIENT_ID"
echo "GOOGLE_OAUTH_IOS_URL_SCHEME=$GOOGLE_OAUTH_IOS_URL_SCHEME"

# Also write to a temporary file that can be included by .xcconfig
TEMP_CONFIG_FILE="${PROJECT_ROOT}/ios/Flutter/GoogleOAuth.xcconfig"
cat > "$TEMP_CONFIG_FILE" << EOF
// Auto-generated Google OAuth configuration from $ENV_FILE
// Do not edit this file manually - it will be overwritten

GOOGLE_OAUTH_IOS_CLIENT_ID = $GOOGLE_OAUTH_IOS_CLIENT_ID
GOOGLE_OAUTH_IOS_URL_SCHEME = $GOOGLE_OAUTH_IOS_URL_SCHEME
EOF

echo "Generated Google OAuth configuration in $TEMP_CONFIG_FILE"
