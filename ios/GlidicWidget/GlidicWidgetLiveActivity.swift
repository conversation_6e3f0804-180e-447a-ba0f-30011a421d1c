import ActivityKit
import SwiftUI
import WidgetKit

/// Data structure for Live Activity that matches Flutter RecordingLiveActivityModel
/// This structure must be named exactly "LiveActivitiesAppAttributes" as required by the plugin
/// All parameter names and types must match exactly with Flutter model
struct LiveActivitiesAppAttributes: ActivityAttributes, Identifiable {
    public typealias LiveDeliveryData = ContentState

    /// Content state structure that matches Flutter RecordingLiveActivityModel.toMap() output
    public struct ContentState: Codable, Hashable {
        /* For preview only Core recording data
        var durationMs: Int = 0
        var title: String?
        var status: String?
        var amplitude: Double?
        var isRecording: Bool?
        var isPaused: Bool?
        var timestamp: Int?
        */
        var waveformAmplitudes: [Double]?
    }

    var id = UUID()
}

/// Extension for prefixed keys as required by ActivityKit documentation
extension LiveActivitiesAppAttributes {
    func prefixedKey(_ key: String) -> String {
        return "\(id)_\(key)"
    }
}

// Create shared UserDefaults with app group for data exchange
// Use optional to handle potential initialization failures in Live Activity context
let sharedDefault: UserDefaults? = {
    // Try to create UserDefaults with app group
    let userDefaults = UserDefaults(suiteName: "group.jp.co.iotbank.glidici.shared")
    
    // If app group UserDefaults fails, log and return nil to use fallback
    if userDefaults == nil {
        print("Warning: App group UserDefaults initialization failed, using fallback")
    }
    
    return userDefaults
}()

@available(iOSApplicationExtension 16.1, *)
struct GlidicWidgetLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: LiveActivitiesAppAttributes.self) { context in
            // Lock screen/banner UI with safe data handling
            RecordingLockScreenView(context: context)
                .activityBackgroundTint(Color.clear)
                .activitySystemActionForegroundColor(Color.red)
                .containerBackground(for: .widget) {
                    Color.black.opacity(0.8)
                }
        } dynamicIsland: { context in
            // Dynamic Island UI
            DynamicIsland {
                // Expanded view regions
                DynamicIslandExpandedRegion(.leading) {
                    HStack(spacing: 12) {
                        WaveformView(context: context, config: .expanded)
                            .frame(height: 40)
                            .frame(width: .infinity)
                    }.padding(.leading, 22)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    HStack(spacing: 12) {
                        Text(getRecordingTime(context: context))
                            .font(.system(size: 18, weight: .medium, design: .monospaced))
                            .foregroundColor(.red)

                        RecordButton(isRecording: getRecordingStatus(context: context))
                            .frame(width: 40, height: 40)
                    }
                    .padding(.trailing, 22)
                }
                
                DynamicIslandExpandedRegion(.bottom) {
                    Spacer()
                }
            } compactLeading: {
                HStack(spacing: 12) {
                    WaveformView(context: context, config: .compact)
                        .frame(width: 30, height: 20)
                        .padding(.leading, 4)
                }
            } compactTrailing: {
                Text(getRecordingTime(context: context))
                    .font(.system(size: 14, weight: .medium, design: .monospaced))
                    .foregroundColor(.red)
                    .padding(.trailing, 4)
            } minimal: {
                WaveformView(context: context, config: .minimal)
                    .frame(width: 30, height: 16)
            }
            .keylineTint(.red)
            .contentMargins(.horizontal, 0, for: .expanded)
        }
    }
}

/// View for Lock Screen presentation of the Live Activity
/// Matches Figma design with top section (profile, timer, controls) and bottom section (waveform, record button, status)
struct RecordingLockScreenView: View {
    let context: ActivityViewContext<LiveActivitiesAppAttributes>

    var body: some View {
        VStack(spacing: 16) {
            // Top section: Profile image, name, timer, and control buttons
            HStack(spacing: 16) {
                // Profile image (left)
                ProfileImageView()
                    .frame(width: 44, height: 44)
                
                // Name and timer section
                VStack(alignment: .leading, spacing: 6) {
                    // Green timer (top section)
                    Text(getRecordingTime(context: context))
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(Color(red: 0, green: 1, blue: 0.24)) // #00FF3D
                    
                    Text(getUserName(context: context))
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .lineLimit(1)
                }
                
                Spacer()
                
                // Control buttons (right)
                HStack(spacing: 12) {
                    // Earphone/microphone icon
                    HeadphoneImageView()
                        .frame(width: 44, height: 44)
                    
                    // Cancel/End button
                    EndCallButtonView()
                        .frame(width: 44, height: 44)
                }
            }
            
            // Bottom section: Waveform, record button, and status
            HStack(spacing: 15) {
                // Waveform visualization (left)
                WaveformView(context: context, config: .expanded)
                    .frame(height: 30)
                    .frame(maxWidth: .infinity, alignment: .leading)
                Spacer()
                // Central record button
                RecordButton(isRecording: true)
                    .frame(width: 44, height: 44)
                Spacer()
                // Status and timer (right)
                HStack(alignment: .bottom, spacing: 2) {
                    Text("録音中") // "Recording" in Japanese
                        .font(.system(size: 11, weight: .bold))
                        .foregroundColor(.red)
                    
                    Text(getRecordingTime(context: context))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.red)
                        .monospacedDigit()
                }
            }
        }
        .padding(.horizontal, 17)
        .padding(.vertical, 16)
        .background(
            Rectangle()
                .fill(Color.black)
        )
        .containerBackground(for: .widget) {
            Color.clear
        }
    }
}

/// Profile image view for the top section
struct ProfileImageView: View {
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.gray.opacity(0.3))

            // Placeholder profile image - in production this could be dynamic
            Image(systemName: "person.fill")
                .font(.system(size: 24))
                .foregroundColor(.white)
        }
    }
}

struct HeadphoneImageView: View {
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.white)
            
            Image(systemName: "headphones")
                .font(.system(size: 24))
                .foregroundColor(.gray)
        }
    }
}

struct EndCallButtonView: View {
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.red)
            
            Image(systemName: "phone.down.fill")
                .font(.system(size: 24))
                .foregroundColor(.white)
        }
    }
}
    

/// Configuration for waveform display properties
struct WaveformConfig {
    let barWidth: CGFloat
    let barSpacing: CGFloat
    let minBarHeight: CGFloat
    let maxBarHeight: CGFloat
    let barCornerRadius: CGFloat
    let maxBars: Int
    let placeHolderBarCount: Int
    let scaleFactor: Double
    let amplitudeSensitivity: Double
    
    // Predefined configurations with improved parameters
    static let expanded = WaveformConfig(
        barWidth: 2.5,
        barSpacing: 1.5,
        minBarHeight: 2.0,
        maxBarHeight: 30.0,
        barCornerRadius: 1.0,
        maxBars: 25,
        placeHolderBarCount: 3,
        scaleFactor: 1.5,
        amplitudeSensitivity: 0.8
    )
    
    static let compact = WaveformConfig(
        barWidth: 2.0,
        barSpacing: 1,
        minBarHeight: 1.5,
        maxBarHeight: 20.0,
        barCornerRadius: 1.0,
        maxBars: 12,
        placeHolderBarCount: 1,
        scaleFactor: 1.2,
        amplitudeSensitivity: 0.7
    )
    
    static let minimal = WaveformConfig(
        barWidth: 2.0,
        barSpacing: 1.0,
        minBarHeight: 1.0,
        maxBarHeight: 16.0,
        barCornerRadius: 1.0,
        maxBars: 6,
        placeHolderBarCount: 0,
        scaleFactor: 1,
        amplitudeSensitivity: 0.6
    )
}

/// Unified waveform view that can be configured for different display contexts
struct WaveformView: View {
    let context: ActivityViewContext<LiveActivitiesAppAttributes>?
    let config: WaveformConfig
    let staticData: [Double]? // Optional data for preview mode
    
    init(context: ActivityViewContext<LiveActivitiesAppAttributes>?, config: WaveformConfig, staticData: [Double]? = nil) {
        self.context = context
        self.config = config
        self.staticData = staticData
    }

    var body: some View {
        HStack(spacing: config.barSpacing) {
            // Check if we have real waveform data from context
            if let waveformData = getWaveformData() {
                // Use real waveform data, limited to maxBars
                let displayData = Array(waveformData.prefix(config.maxBars))
                ForEach(0..<displayData.count, id: \.self) { index in
                    RoundedRectangle(cornerRadius: config.barCornerRadius)
                        .fill(Color.red)
                        .frame(width: config.barWidth)
                        .frame(height: barHeightFromAmplitude(displayData[index]))
                        .transition(.scale)
                }

                // Add dimmed bars for remaining space if we have fewer bars than max
                if displayData.count < config.maxBars {
                    ForEach(displayData.count..<config.maxBars, id: \.self) { index in
                        RoundedRectangle(cornerRadius: config.barCornerRadius)
                            .fill(Color.gray.opacity(0.4))
                            .frame(width: config.barWidth)
                            .frame(height: config.minBarHeight)
                    }
                }
            } else {
                // Fallback to animated waveform when no data available
                ForEach(0..<config.maxBars, id: \.self) { index in
                    let isActive = index < config.maxBars - config.placeHolderBarCount
                    RoundedRectangle(cornerRadius: config.barCornerRadius)
                        .fill(isActive ? Color.red : Color.gray.opacity(0.4))
                        .frame(width: config.barWidth)
                        .frame(height: config.minBarHeight)
                }
            }
        }
    }

    /// Retrieves waveform amplitude data from multiple sources with fallbacks
    private func getWaveformData() -> [Double]? {
        // First check for static data (for previews)
        if let staticData = staticData, !staticData.isEmpty {
            return staticData
        }
        
        guard let context = context else { return nil }
        
        // Try to get waveform data from ContentState first
        if let amplitudes = context.state.waveformAmplitudes, !amplitudes.isEmpty {
            return amplitudes
        }

        // Otherwise try from UserDefaults - waveform data might be stored as JSON
        let waveformDataString = safelyReadFromUserDefaults(
            context: context,
            key: "waveformAmplitudes",
            defaultValue: ""
        )

        if !waveformDataString.isEmpty,
           let data = waveformDataString.data(using: .utf8) {
            do {
                return try JSONDecoder().decode([Double].self, from: data)
            } catch {
                print("Failed to decode waveform data: \(error)")
            }
        }

        return nil
    }

    /// Converts amplitude (0.0-1.0) to visual bar height
    private func barHeightFromAmplitude(_ amplitude: Double) -> CGFloat {
        // Ensure amplitude is in valid range (0.0-1.0)
        let noiseFloor = 0.0;
        let normalizedAmplitude = max(0.0, min(1.0, amplitude - noiseFloor))
        
        let scaledAmplitude = normalizedAmplitude * config.scaleFactor
        let sensitiveAmplitude = pow(scaledAmplitude, config.amplitudeSensitivity)
        let heightRange = config.maxBarHeight - config.minBarHeight
        let visualAmplitude = pow(sensitiveAmplitude, 0.7)
        let calculatedHeight = config.minBarHeight + (heightRange * CGFloat(visualAmplitude))
        return min(max(calculatedHeight, config.minBarHeight), config.maxBarHeight)
    }
}

/// Visual representation of recording button state matching Figma design
struct RecordButton: View {
    let isRecording: Bool
    @State private var pulseScale: CGFloat = 1.0

    var body: some View {
        ZStack {
            // Animated pulse effect when recording (subtle)
            if isRecording {
                Circle()
                    .fill(Color.red.opacity(0.2))
                    .scaleEffect(pulseScale)
                    .opacity((2.0 - pulseScale) / 1.0)
                    .animation(
                        Animation.easeInOut(duration: 0.1)
                            .repeatForever(autoreverses: false),
                        value: pulseScale
                    )
                    .onAppear {
                        pulseScale = 1.3
                    }
            }

            // White border circle (2.5px stroke as per Figma)
            Circle()
                .stroke(Color.white, lineWidth: 2.5)
                .background(Circle().fill(Color.clear))

            // Inner red square for recording state (matches Figma design)
            if isRecording {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.red)
                    .frame(width: 22, height: 22) // Larger size to match Figma proportions
            } else {
                // Circle for stopped state
                Circle()
                    .fill(Color.red)
                    .frame(width: 18, height: 18)
            }
        }
    }
}

/// Safely read from UserDefaults with prewarming protection and fallback handling
func safelyReadFromUserDefaults(context: ActivityViewContext<LiveActivitiesAppAttributes>, key: String, defaultValue: String) -> String {
    // Get properly prefixed key according to documentation
    let prefixedKey = context.attributes.prefixedKey(key)
    
    // Try to read from shared UserDefaults first
    if let sharedDefaults = sharedDefault {
        let value = sharedDefaults.string(forKey: prefixedKey)
        if let value = value, !value.isEmpty {
            return value
        }
    }
    
    // Fallback to standard UserDefaults if app group fails
    let standardDefaults = UserDefaults.standard
    let value = standardDefaults.string(forKey: prefixedKey)
    if let value = value, !value.isEmpty {
        return value
    }
    
    // Return default value if all else fails
    return defaultValue
}
/// Format and retrieve recording time from multiple sources
func getRecordingTime(context: ActivityViewContext<LiveActivitiesAppAttributes>) -> String {
    // Get recording time with proper key prefixing
    return safelyReadFromUserDefaults(context: context, key: "recordingTime", defaultValue: "00:00")
}

/// Determine if recording is active from multiple sources
func getRecordingStatus(context: ActivityViewContext<LiveActivitiesAppAttributes>) -> Bool {
    // Get recording status with proper key prefixing
    let statusString = safelyReadFromUserDefaults(context: context, key: "isRecording", defaultValue: "true")
    return statusString.lowercased() == "true"
}

func getTitle(context: ActivityViewContext<LiveActivitiesAppAttributes>) -> String {
    // Get recording title with proper key prefixing
    return safelyReadFromUserDefaults(context: context, key: "title", defaultValue: "Recording")
}

func getUserName(context: ActivityViewContext<LiveActivitiesAppAttributes>) -> String {
    // Get user name with proper key prefixing
    return safelyReadFromUserDefaults(context: context, key: "userName", defaultValue: "Glidic")
}

/*
// MARK: - Widget Previews

#if DEBUG
@available(iOSApplicationExtension 16.1, *)
struct GlidicWidgetLiveActivity_Previews: PreviewProvider {
    static let activityAttributes = LiveActivitiesAppAttributes(id: UUID(), title: "Voice Recording")
    
    // Recording state for active previews
    static let recordingState = LiveActivitiesAppAttributes.ContentState(
        durationMs: 135000,  // 2:15
        title: "Voice Recording",
        status: "recording",
        amplitude: 0.7,
        isRecording: true,
        isPaused: false,
        timestamp: Int(Date().timeIntervalSince1970 * 1000),
        waveformAmplitudes: LiveActivitiesAppAttributes.ContentState.sampleWaveform
    )
    
    // Paused state for paused previews
    static let pausedState = LiveActivitiesAppAttributes.ContentState(
        durationMs: 87000,  // 1:27
        title: "Voice Recording",
        status: "paused",
        amplitude: 0.3,
        isRecording: false,
        isPaused: true,
        timestamp: Int(Date().timeIntervalSince1970 * 1000),
        waveformAmplitudes: LiveActivitiesAppAttributes.ContentState.highAmplitudeWaveform
    )
    
    // High activity state with more dramatic waveform
    static let highActivityState = LiveActivitiesAppAttributes.ContentState(
        durationMs: 248000,  // 4:08
        title: "Important Recording",
        status: "recording",
        amplitude: 0.9,
        isRecording: true,
        isPaused: false,
        timestamp: Int(Date().timeIntervalSince1970 * 1000),
        waveformAmplitudes: LiveActivitiesAppAttributes.ContentState.sampleWaveform
    )
    
    static var previews: some View {
        Group {
            // MARK: - Dynamic Island Previews
            
            // Dynamic Island: Compact Mode
            activityAttributes
                .previewContext(recordingState, viewKind: ActivityPreviewViewKind.dynamicIsland(.compact))
                .previewDisplayName("Dynamic Island: Compact")
            
            // Dynamic Island: Expanded Mode
            activityAttributes
                .previewContext(highActivityState, viewKind: ActivityPreviewViewKind.dynamicIsland(.expanded))
                .previewDisplayName("Dynamic Island: Expanded")
            
            // Dynamic Island: Minimal Mode
            activityAttributes
                .previewContext(recordingState, viewKind: ActivityPreviewViewKind.dynamicIsland(.minimal))
                .previewDisplayName("Dynamic Island: Minimal")
            
            // MARK: - Lock Screen Previews
            
            // Lock Screen recording state
            activityAttributes
                .previewContext(recordingState, viewKind: ActivityPreviewViewKind.content)
                .previewDisplayName("Lock Screen: Recording")
            
            // Lock Screen paused state
            activityAttributes
                .previewContext(pausedState, viewKind: ActivityPreviewViewKind.content)
                .previewDisplayName("Lock Screen: Paused")
            
            // MARK: - Widget Previews
            
            // Widget: Normal Recording State
            GlidicWidgetLiveActivityView(
                waveformAmplitudes: LiveActivitiesAppAttributes.ContentState.sampleWaveform,
                recordingTime: "02:15",
                isRecording: true
            )
            .previewContext(WidgetPreviewContext(family: .accessoryRectangular))
            .previewDisplayName("Widget: Recording")
            .containerBackground(for: .widget) {
                Color.clear
            }
            
            // Widget: High Activity Recording
            GlidicWidgetLiveActivityView(
                waveformAmplitudes: LiveActivitiesAppAttributes.ContentState.highAmplitudeWaveform,
                recordingTime: "04:08",
                isRecording: true
            )
            .previewContext(WidgetPreviewContext(family: .accessoryRectangular))
            .previewDisplayName("Widget: High Activity")
            .containerBackground(for: .widget) {
                Color.clear
            }
            
            // Widget: Paused State
            GlidicWidgetLiveActivityView(
                waveformAmplitudes: LiveActivitiesAppAttributes.ContentState.lowAmplitudeWaveform,
                recordingTime: "01:27",
                isRecording: false
            )
            .previewContext(WidgetPreviewContext(family: .accessoryRectangular))
            .previewDisplayName("Widget: Paused")
            .containerBackground(for: .widget) {
                Color.clear
            }
        }
    }
}

/// Standalone view for Live Activity preview that mirrors the actual implementation
struct GlidicWidgetLiveActivityView: View {
    let waveformAmplitudes: [Double]?
    let recordingTime: String
    let isRecording: Bool

    var body: some View {
        VStack(spacing: 16) {
            // Top section: Profile image, name, timer, and control buttons
            HStack(spacing: 16) {
                // Profile image (left)
                ProfileImageView()
                    .frame(width: 44, height: 44)

                // Name and timer section
                VStack(alignment: .leading, spacing: 6) {
                    // Recording title/name
                    Text("Voice Recording")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .lineLimit(1)

                    // Green timer (top section)
                    Text(recordingTime)
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(Color(red: 0, green: 1, blue: 0.24)) // #00FF3D
                }

                Spacer()

                // Control buttons (right)
                HStack(spacing: 12) {
                    // Earphone/microphone icon
                    ZStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 50, height: 50)

                        Image(systemName: "headphones")
                            .font(.system(size: 24))
                            .foregroundColor(.gray)
                    }

                    // Cancel/End button
                    ZStack {
                        Circle()
                            .fill(Color.red)
                            .frame(width: 50, height: 50)

                        Image(systemName: "phone.down.fill")
                            .font(.system(size: 22))
                            .foregroundColor(.white)
                    }
                }
            }

            // Bottom section: Waveform, record button, and status
            HStack(spacing: 24) {
                // Waveform visualization (left)
                WaveformPreviewView(amplitudes: waveformAmplitudes)
                    .frame(height: 27)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // Central record button
                RecordButton(isRecording: isRecording)
                    .frame(width: 44, height: 44)

                // Status and timer (right)
                VStack(alignment: .trailing, spacing: 2) {
                    Text("録音中") // "Recording" in Japanese
                        .font(.system(size: 11, weight: .bold))
                        .foregroundColor(.red)

                    Text(recordingTime)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.red)
                        .monospacedDigit()
                }
            }
        }
        .padding(.horizontal, 17)
        .padding(.vertical, 16)
        .background(
            Rectangle()
                .fill(Color.black)
        )
    }
}

/// Simplified waveform view for previews using the unified WaveformView
struct WaveformPreviewView: View {
    let amplitudes: [Double]?
    
    // Configuration for preview - larger for better visibility in previews
    private let previewConfig = WaveformConfig(
        barWidth: 3.0,
        barSpacing: 2.0,
        minBarHeight: 3.0,
        maxBarHeight: 27.0,
        barCornerRadius: 2.0,
        maxBars: 25,
        placeHolderBarCount: 0,
        scaleFactor: 1.1,
        amplitudeSensitivity: 0.8
    )

    var body: some View {
        WaveformView(context: nil, config: previewConfig, staticData: amplitudes)
    }
}

// MARK: - Mock Data for Previews

extension LiveActivitiesAppAttributes.LiveDeliveryData {
    /// Sample waveform data for previews
    static let sampleWaveform: [Double] = [
        0.2, 0.5, 0.8, 0.3, 0.9, 0.1, 0.6, 0.4, 0.7, 0.2,
        0.8, 0.3, 0.5, 0.9, 0.1, 0.4, 0.7, 0.6, 0.2, 0.8,
        0.5, 0.3, 0.9, 0.4, 0.6, 0.2, 0.8, 0.7, 0.1, 0.5,
    ]
    
    /// High amplitude waveform for testing
    static let highAmplitudeWaveform: [Double] = [
        0.7, 0.9, 0.8, 0.6, 1.0, 0.5, 0.8, 0.7, 0.9, 0.6,
        0.1, 0.4, 0.3, 0.2, 0.5, 0.6, 0.8, 0.9, 0.7, 1.0,
        0.6, 0.8, 0.5, 0.4, 0.3, 0.2, 0.1, 0.5, 0.6, 0.7,
    ]
    
    /// Low amplitude waveform for testing
    static let lowAmplitudeWaveform: [Double] = [
        0.1, 0.2, 0.3, 0.1, 0.4, 0.2, 0.3, 0.1, 0.2, 0.3,
        0.1, 0.4, 0.2, 0.3, 0.1, 0.2, 0.3, 0.1, 0.4, 0.2,
        0.3, 0.1, 0.2, 0.3, 0.1, 0.4, 0.2, 0.3, 0.1, 0.2,
    ]
}
#endif
*/
