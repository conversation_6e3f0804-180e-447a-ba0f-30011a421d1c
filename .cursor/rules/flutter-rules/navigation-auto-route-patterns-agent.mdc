---
description: This rule governs navigation patterns using AutoRoute in the Glidic Flutter App. It should be applied when: (1) Setting up new routes and navigation structure, (2) Implementing type-safe navigation between screens, (3) Adding route guards and authentication checks, (4) Creating nested navigation and tab structures, (5) Implementing deep linking and route parameters, (6) Setting up route transitions and animations.
globs: 
alwaysApply: false
---

# Navigation Patterns with AutoRoute

## Critical Rules

- Use **AutoRoute** for declarative, type-safe navigation
- Define routes in `app_router.dart` with proper route structure
- Use `context.go()` for navigation instead of manual Navigator calls
- Implement route guards for authentication and authorization
- Create navigation extensions for commonly used routes
- Use proper route naming conventions with RouteNames constants
- Handle route parameters with type-safe classes
- Implement proper route transitions and loading states

## Router Configuration

### App Router Setup
```dart
@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    // Authentication Routes
    AutoRoute(
      page: LoginRoute.page,
      path: '/login',
      initial: true,
    ),
    
    // Main App Routes (Protected)
    AutoRoute(
      page: HomeWrapperRoute.page,
      path: '/home',
      guards: [AuthGuard],
      children: [
        AutoRoute(
          page: HomeRoute.page,
          path: '/dashboard',
        ),
        AutoRoute(
          page: RecordingRoute.page,
          path: '/recording',
        ),
        AutoRoute(
          page: CalendarRoute.page,
          path: '/calendar',
        ),
      ],
    ),
    
    // Feature-specific Routes
    AutoRoute(
      page: RecordingListRoute.page,
      path: '/recordings',
      guards: [AuthGuard],
    ),
    
    // Parameterized Routes
    AutoRoute(
      page: RecordingDetailRoute.page,
      path: '/recording/:recordingId',
      guards: [AuthGuard],
    ),
    
    // Fallback Route
    AutoRoute(
      page: NotFoundRoute.page,
      path: '*',
    ),
  ];
}
```

### Route Names Constants
```dart
class RouteNames {
  static const String login = '/login';
  static const String home = '/home';
  static const String dashboard = '/home/<USER>';
  static const String recording = '/home/<USER>';
  static const String calendar = '/home/<USER>';
  static const String recordingList = '/recordings';
  static const String recordingDetail = '/recording/:recordingId';
  static const String notFound = '*';
}
```

## Page Classes Definition

### AutoRoute Pages
```dart
// Login Page
@RoutePage()
class LoginPage extends StatelessWidget {
  const LoginPage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return const LoginScreen();
  }
}

// Home Wrapper with Navigation
@RoutePage()
class HomeWrapperPage extends StatelessWidget {
  const HomeWrapperPage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return AutoTabsScaffold(
      routes: const [
        HomeRoute(),
        RecordingRoute(),
        CalendarRoute(),
      ],
      bottomNavigationBuilder: (context, tabsRouter) {
        return BottomNavigationBar(
          currentIndex: tabsRouter.activeIndex,
          onTap: tabsRouter.setActiveIndex,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.mic),
              label: 'Recording',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.calendar_today),
              label: 'Calendar',
            ),
          ],
        );
      },
    );
  }
}

// Parameterized Page
@RoutePage()
class RecordingDetailPage extends StatelessWidget {
  const RecordingDetailPage({
    super.key,
    @pathParam required this.recordingId,
  });
  
  final String recordingId;
  
  @override
  Widget build(BuildContext context) {
    return RecordingDetailScreen(recordingId: recordingId);
  }
}
```

## Route Guards

### Authentication Guard
```dart
class AuthGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    // Check if user is authenticated
    if (sl<AuthRepository>().isAuthenticated()) {
      resolver.next();
    } else {
      // Redirect to login
      router.pushAndClearStack(const LoginRoute());
    }
  }
}

class AdminGuard extends AutoRouteGuard {
  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) async {
    final user = await sl<AuthRepository>().getCurrentUser();
    
    if (user?.role == UserRole.admin) {
      resolver.next();
    } else {
      // Redirect to unauthorized page
      router.pushAndClearStack(const UnauthorizedRoute());
    }
  }
}
```

## Navigation Extensions

### Convenient Navigation Methods
```dart
extension AppNavigationExtension on BuildContext {
  // Authentication Navigation
  void goToLogin() => go(RouteNames.login);
  void goToHome() => go(RouteNames.home);
  
  // Feature Navigation
  void goToRecording() => go(RouteNames.recording);
  void goToCalendar() => go(RouteNames.calendar);
  void goToRecordingList() => go(RouteNames.recordingList);
  
  // Parameterized Navigation
  void goToRecordingDetail(String recordingId) {
    go('/recording/$recordingId');
  }
  
  // Stack Navigation
  void pushRecordingDetail(String recordingId) {
    push('/recording/$recordingId');
  }
  
  // Navigation with Parameters
  void goToCalendarEvent({
    required String eventId,
    DateTime? selectedDate,
  }) {
    final uri = Uri(
      path: '/calendar/event/$eventId',
      queryParameters: selectedDate != null 
          ? {'date': selectedDate.toIso8601String()}
          : null,
    );
    go(uri.toString());
  }
  
  // Authentication-aware Navigation
  void goToProtectedRoute(String route) async {
    final isAuthenticated = await sl<AuthRepository>().isAuthenticated();
    if (isAuthenticated) {
      go(route);
    } else {
      goToLogin();
    }
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct Route Definition
  @AutoRouterConfig()
  class AppRouter extends _$AppRouter {
    @override
    List<AutoRoute> get routes => [
      AutoRoute(
        page: LoginRoute.page,
        path: '/login',
        initial: true,
      ),
      AutoRoute(
        page: HomeRoute.page,
        path: '/home',
        guards: [AuthGuard],
      ),
      AutoRoute(
        page: RecordingDetailRoute.page,
        path: '/recording/:recordingId',
        guards: [AuthGuard],
      ),
    ];
  }
  
  // ✅ Correct Page Implementation
  @RoutePage()
  class LoginPage extends StatelessWidget {
    const LoginPage({super.key});
    
    @override
    Widget build(BuildContext context) {
      return BlocProvider(
        create: (context) => sl<LoginCubit>(),
        child: const LoginScreen(),
      );
    }
  }
  
  // ✅ Correct Navigation Usage
  class LoginCubit extends Cubit<LoginState> {
    Future<void> login(String email, String password) async {
      try {
        final user = await loginUseCase.execute(LoginParams(email, password));
        emit(LoginSuccess(user: user));
        
        // Navigate to home after successful login
        if (context.mounted) {
          context.goToHome();
        }
      } catch (e) {
        emit(LoginError(failure: e as AppFailure));
      }
    }
  }
  
  // ✅ Correct Parameterized Navigation
  class RecordingListScreen extends StatelessWidget {
    void _onRecordingTap(RecordItem recording) {
      context.goToRecordingDetail(recording.id);
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Manual Navigator usage
  class BadNavigation {
    void navigateToHome() {
      Navigator.of(context).pushReplacementNamed('/home'); // ❌ Use context.go()
    }
    
    void navigateToDetail(String id) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => RecordingDetailScreen(id: id), // ❌ Use AutoRoute
        ),
      );
    }
  }
  
  // ❌ Wrong - Hardcoded route strings
  class BadRouting {
    void goToRecording() {
      context.go('/recording'); // ❌ Use RouteNames constants
    }
  }
  
  // ❌ Wrong - No route guards for protected routes
  @AutoRouterConfig()
  class BadRouter extends _$BadRouter {
    @override
    List<AutoRoute> get routes => [
      AutoRoute(
        page: AdminPanelRoute.page,
        path: '/admin', // ❌ No guards for admin route
      ),
    ];
  }
  
  // ❌ Wrong - Not using BlocProvider in page
  @RoutePage()
  class BadPage extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return LoginScreen(); // ❌ No BlocProvider, no dependency injection
    }
  }
  ```
</example>

## Nested Navigation

### Tab-Based Navigation
```dart
@RoutePage()
class MainTabPage extends StatelessWidget {
  const MainTabPage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return AutoTabsScaffold(
      routes: const [
        HomeTab(),
        RecordingTab(),
        CalendarTab(),
        ProfileTab(),
      ],
      bottomNavigationBuilder: (context, tabsRouter) {
        return BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: tabsRouter.activeIndex,
          onTap: (index) {
            tabsRouter.setActiveIndex(index);
            
            // Track navigation analytics
            AnalyticsService.trackTabNavigation(
              tab: _getTabName(index),
            );
          },
          items: [
            const BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'Home',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.mic),
              label: 'Recording',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.calendar_today),
              label: 'Calendar',
            ),
            const BottomNavigationBarItem(
              icon: Icon(Icons.person),
              label: 'Profile',
            ),
          ],
        );
      },
    );
  }
  
  String _getTabName(int index) {
    switch (index) {
      case 0: return 'home';
      case 1: return 'recording';
      case 2: return 'calendar';
      case 3: return 'profile';
      default: return 'unknown';
    }
  }
}
```

## Deep Linking

### URL Parameter Handling
```dart
@RoutePage()
class CalendarEventPage extends StatelessWidget {
  const CalendarEventPage({
    super.key,
    @pathParam required this.eventId,
    @queryParam this.date,
    @queryParam this.view,
  });
  
  final String eventId;
  final String? date;
  final String? view;
  
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<CalendarEventCubit>()
        ..loadEvent(
          eventId: eventId,
          selectedDate: date != null ? DateTime.parse(date!) : null,
          view: CalendarView.fromString(view),
        ),
      child: const CalendarEventScreen(),
    );
  }
}
```

## Route Transitions

### Custom Transitions
```dart
@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    // Slide transition for recording screens
    AutoRoute(
      page: RecordingRoute.page,
      path: '/recording',
      transitionsBuilder: TransitionsBuilders.slideLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
    
    // Fade transition for modal screens
    AutoRoute(
      page: SettingsRoute.page,
      path: '/settings',
      transitionsBuilder: TransitionsBuilders.fadeIn,
      transitionDuration: const Duration(milliseconds: 200),
    ),
    
    // Custom transition
    AutoRoute(
      page: RecordingDetailRoute.page,
      path: '/recording/:recordingId',
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: animation.drive(
            Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                .chain(CurveTween(curve: Curves.easeInOut)),
          ),
          child: child,
        );
      },
    ),
  ];
}
```

## Navigation State Management

### Navigation-Aware Cubits
```dart
class AppNavigationCubit extends Cubit<AppNavigationState> {
  AppNavigationCubit() : super(const AppNavigationState.initial());
  
  void navigateToFeature(AppFeature feature) {
    emit(AppNavigationState.navigating(feature));
    
    switch (feature) {
      case AppFeature.recording:
        // Check permissions before navigation
        _checkRecordingPermissions().then((hasPermission) {
          if (hasPermission) {
            emit(AppNavigationState.completed(feature));
          } else {
            emit(const AppNavigationState.error('Recording permission required'));
          }
        });
        break;
        
      case AppFeature.calendar:
        emit(AppNavigationState.completed(feature));
        break;
    }
  }
  
  Future<bool> _checkRecordingPermissions() async {
    // Permission checking logic
    return await Permission.microphone.isGranted;
  }
}
```

## Error Handling in Navigation

### Navigation Error Recovery
```dart
extension SafeNavigationExtension on BuildContext {
  Future<void> safeGoTo(String route) async {
    try {
      go(route);
    } catch (e) {
      // Log navigation error
      ErrorLogger.logException(
        e,
        context: 'Navigation to $route',
        additionalData: {'current_route': GoRouterState.of(this).fullPath},
      );
      
      // Fallback to safe route
      go(RouteNames.home);
    }
  }
  
  Future<void> safeGoToProtected(String route) async {
    try {
      final isAuthenticated = await sl<AuthRepository>().isAuthenticated();
      if (isAuthenticated) {
        go(route);
      } else {
        goToLogin();
      }
    } catch (e) {
      // Handle authentication check error
      ErrorLogger.logException(e, context: 'Protected navigation check');
      goToLogin();
    }
  }
}
```