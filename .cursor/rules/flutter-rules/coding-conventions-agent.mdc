---
description: This rule governs coding conventions in the Glidic Flutter App. It should be applied when: (1) Writing new code with proper naming conventions, (2) Reviewing code for consistency and standards, (3) Implementing new features that need to follow established patterns, (4) Ensuring proper code organization and documentation, (5) Setting up constants usage and avoiding hardcoded values, (6) Implementing consistent error messages and validation.
globs: 
alwaysApply: false
---

# Coding Conventions

## Critical Rules

- Use **descriptive, explicit variable names** over short, ambiguous ones
- Follow **Dart naming conventions**: `camelCase` for variables/methods, `PascalCase` for classes
- **NO hardcoded values** - all constants must be defined in appropriate `constants/` files
- Use **English** for all code, comments, and documentation
- Implement **comprehensive error handling** with proper logging
- Follow **consistent file and folder naming** patterns
- Use **meaningful prefixes** for different types of constants
- Ensure **proper documentation** for all public APIs and complex logic

## Naming Conventions

### Variables and Methods
```dart
// ✅ Correct - Descriptive names
String userDisplayName;
bool isEmailVerified;
DateTime recordingStartTime;
List<RecordItem> filteredRecordings;

Future<void> validateUserInput() async { }
void updateRecordingProgress(double progress) { }

// ❌ Wrong - Ambiguous names
String name; // Too generic
bool flag; // Unclear purpose
DateTime dt; // Abbreviated
List<RecordItem> items; // Not specific enough

void validate() { } // What is being validated?
void update(double val) { } // Update what? Val is what?
```

### Classes and Types
```dart
// ✅ Correct - Clear, descriptive class names
class UserAuthenticationManager { }
class RecordingSessionController { }
class CalendarEventValidator { }
enum RecordingStatus { recording, paused, stopped }

// ❌ Wrong - Vague or abbreviated names
class Manager { } // Manager of what?
class Controller { } // Controls what?
class Validator { } // Validates what?
enum Status { } // Status of what?
```

### Files and Folders
```dart
// ✅ Correct - Feature-based, descriptive naming
login_screen.dart
user_authentication_cubit.dart
recording_session_model.dart
calendar_event_repository.dart

// ❌ Wrong - Generic or unclear names
screen.dart
cubit.dart
model.dart
repository.dart
```

## Constants Usage Rules

### Mandatory Constants Organization

All hardcoded values MUST be defined in appropriate constants files:

#### Colors → `color_constants.dart`
```dart
// ✅ Correct - Using constants
Container(
  color: ColorConstants.primaryColor,
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(Dimensions.radiusMd),
  ),
)

// ❌ Wrong - Hardcoded values
Container(
  color: Color(0xFF8BAABD), // ❌ Use ColorConstants.primaryColor
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(8.0), // ❌ Use Dimensions.radiusMd
  ),
)
```

#### Dimensions → `dimensions.dart`
```dart
// ✅ Correct - Using dimension constants
Padding(
  padding: EdgeInsets.all(Dimensions.defaultPadding),
  child: Icon(
    Icons.home,
    size: Dimensions.iconMd,
  ),
)

// ❌ Wrong - Hardcoded dimensions
Padding(
  padding: EdgeInsets.all(16.0), // ❌ Use Dimensions.defaultPadding
  child: Icon(
    Icons.home,
    size: 24.0, // ❌ Use Dimensions.iconMd
  ),
)
```

#### Text Styles → `text_style_constants.dart`
```dart
// ✅ Correct - Using text style constants
Text(
  'Welcome User',
  style: TextStyleConstants.title,
)

// ❌ Wrong - Hardcoded text styles
Text(
  'Welcome User',
  style: TextStyle( // ❌ Use TextStyleConstants.title
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: Color(0xFF212121),
  ),
)
```

#### User-Facing Strings → Localization Required
```dart
// ✅ Correct - Using localization
final l10n = AppLocalizations.of(context)!;
Text(l10n.auth_button_login)
ValidationFailure(l10n.validation_email_required)

// ❌ Wrong - Hardcoded user-facing strings
Text('Sign In') // ❌ Use l10n.auth_button_login
ValidationFailure('Email is required') // ❌ Use l10n.validation_email_required

// ✅ Technical constants (non-user-facing) can use constants
class TechnicalConstants {
  static const String apiEndpointLogin = '/auth/login';
  static const String storageKeyUser = 'user_data';
  static const String logTagAuth = 'Auth';
}
```

## Constants Organization Pattern

### Current Constants Files Structure
```dart
// lib/core/common/constants/color_constants.dart
class ColorConstants {
  ColorConstants._();
  
  static const Color primaryColor = Color(0xFF8BAABD);
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color textPrimary = Color(0xFF212121);
  // ... more colors
}

// lib/core/common/constants/dimensions.dart
class Dimensions {
  const Dimensions._();
  
  static const double defaultPadding = 16.0;
  static const double buttonHeight = 48.0;
  static const double radiusMd = 8.0;
  // ... more dimensions
}

// lib/core/common/constants/network_constants.dart
class NetworkConstants {
  NetworkConstants._();
  
  static String get baseUrl => EnvConfig.baseUrl;
  static const Duration connectTimeout = Duration(seconds: 30);
  static const String authLogin = '/auth/login';
  // ... more network constants
}

// lib/core/common/constants/database_constants.dart
class DatabaseConstants {
  DatabaseConstants._();
  
  static const String databaseName = 'app_database.db';
  static const String usersTable = 'users';
  static const String recordsTable = 'records';
  // ... more database constants
}

// lib/core/common/constants/storage_constants.dart
class StorageConstants {
  StorageConstants._();
  
  static const String accessTokenKey = 'access_token';
  static const String userDataKey = 'user_data';
  // ... more storage constants
}

// lib/core/common/constants/error_codes.dart
class ErrorCodes {
  ErrorCodes._();
  
  static const String noConnection = 'NO_CONNECTION';
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  // ... more error codes
}
```

## Examples

<example>
  ```dart
  // ✅ Correct - Comprehensive example with proper conventions
  class LoginCubit extends Cubit<LoginState> {
    LoginCubit({required this.loginUseCase}) : super(const LoginInitial());
    
    final LoginUseCase loginUseCase;
    
    /// Validates user email format according to business rules
    String? validateEmailAddress(String? emailInput) {
      if (emailInput == null || emailInput.isEmpty) {
        return AuthConstants.errorEmailRequired;
      }
      
      final emailRegex = RegExp(AuthConstants.emailRegexPattern);
      if (!emailRegex.hasMatch(emailInput)) {
        return AuthConstants.errorEmailInvalid;
      }
      
      return null;
    }
    
    /// Validates password strength according to security requirements
    String? validatePasswordStrength(String? passwordInput) {
      if (passwordInput == null || passwordInput.isEmpty) {
        return AuthConstants.errorPasswordRequired;
      }
      
      if (passwordInput.length < AuthConstants.minPasswordLength) {
        return AuthConstants.errorPasswordTooShort;
      }
      
      return null;
    }
    
    /// Performs user authentication with comprehensive error handling
    Future<void> authenticateUser({
      required String emailAddress,
      required String userPassword,
    }) async {
      emit(const LoginLoading());
      
      try {
        final authenticatedUser = await loginUseCase.execute(
          LoginParams(email: emailAddress, password: userPassword),
        );
        
        emit(LoginSuccess(user: authenticatedUser));
        
        // Log successful authentication
        Logger.info(
          'User authentication successful',
          data: {'userId': authenticatedUser.id},
        );
      } catch (authenticationError) {
        final errorMessage = authenticationError is AppFailure
            ? authenticationError.message
            : AuthConstants.errorUnknownFailure;
            
        emit(LoginError(failure: authenticationError as AppFailure));
        
        // Log authentication failure
        Logger.error(
          'User authentication failed',
          error: authenticationError,
          data: {'email': emailAddress},
        );
      }
    }
  }
  
  // ✅ Correct - UI implementation with constants
  class LoginScreen extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return Scaffold(
        backgroundColor: ColorConstants.backgroundColor,
        body: Padding(
          padding: EdgeInsets.all(Dimensions.defaultPadding),
          child: Column(
            children: [
              Text(
                AuthConstants.screenTitleLogin,
                style: TextStyleConstants.title,
              ),
              SizedBox(height: Dimensions.gapLg),
              PrimaryTextField(
                labelText: AuthConstants.labelEmail,
                hintText: AuthConstants.hintEmail,
                validator: (value) => context.read<LoginCubit>().validateEmailAddress(value),
              ),
              SizedBox(height: Dimensions.gapMd),
              PrimaryButton(
                text: AuthConstants.buttonTextLogin,
                onPressed: () => _handleLoginButtonPress(context),
              ),
            ],
          ),
        ),
      );
    }
    
    void _handleLoginButtonPress(BuildContext context) {
      final loginCubit = context.read<LoginCubit>();
      final emailController = _emailController;
      final passwordController = _passwordController;
      
      loginCubit.authenticateUser(
        emailAddress: emailController.text.trim(),
        userPassword: passwordController.text,
      );
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Multiple convention violations
  class login extends Cubit<state> { // ❌ Wrong naming convention
    login({required this.uc}) : super(init()); // ❌ Abbreviated names
    
    final uc; // ❌ No type, unclear name
    
    // ❌ Wrong - No documentation, generic name
    String? validate(String? val) {
      if (val == null || val.isEmpty) {
        return 'Email is required'; // ❌ Hardcoded string
      }
      return null;
    }
    
    // ❌ Wrong - Generic method name, no error handling
    Future<void> login(String e, String p) async {
      emit(loading()); // ❌ Wrong naming
      
      final user = await uc.execute(params(e, p)); // ❌ No try-catch
      emit(success(user)); // ❌ Wrong naming
    }
  }
  
  // ❌ Wrong - UI with hardcoded values
  class bad extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return Scaffold(
        backgroundColor: Color(0xFFFFFFFF), // ❌ Hardcoded color
        body: Padding(
          padding: EdgeInsets.all(16.0), // ❌ Hardcoded padding
          child: Column(
            children: [
              Text(
                'Login', // ❌ Hardcoded string
                style: TextStyle( // ❌ Hardcoded style
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                ),
              ),
              SizedBox(height: 10.0), // ❌ Hardcoded dimension
              TextField( // ❌ Not using custom components
                decoration: InputDecoration(
                  labelText: 'Email', // ❌ Hardcoded string
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
  ```
</example>

## Documentation Standards

### Keep Documentation Concise
```dart
/// Validates email format and returns error message if invalid
String? validateEmailAddress(String? emailInput) {
  // Implementation
}

/// Validates password strength (min 6 characters)
String? validatePassword(String? password) {
  // Implementation  
}

/// Handles user authentication with login/logout operations
class LoginCubit extends Cubit<LoginState> {
  // Implementation
}
```

## Import Organization

```dart
// 1. Dart SDK imports
import 'dart:async';
import 'dart:convert';

// 2. Flutter framework imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. Third-party package imports
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// 4. App imports - Core first
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/failures.dart';

// 5. App imports - Feature imports
import 'package:glidic_app/features/auth/domain/usecases/login_usecase.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_state.dart';
```

## Error Message Standards

### Use Localization for All Error Messages
```dart
// ✅ Correct - Using localized error messages
String? validateEmail(String? email, AppLocalizations l10n) {
  if (email == null || email.isEmpty) {
    return l10n.validation_email_required;
  }
  
  if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email)) {
    return l10n.validation_email_invalid;
  }
  
  return null;
}

// ✅ Error codes (technical) can use constants
class ErrorCodes {
  static const String noConnection = 'NO_CONNECTION';
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  static const String serverError = 'SERVER_ERROR';
}

// ❌ Wrong - Hardcoded user-facing error messages
String? validateEmailBad(String? email) {
  if (email == null || email.isEmpty) {
    return 'Email is required'; // ❌ Use l10n.validation_email_required
  }
  return null;
}
```