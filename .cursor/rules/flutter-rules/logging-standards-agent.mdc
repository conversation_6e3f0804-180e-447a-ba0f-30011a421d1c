---
description: This rule governs logging standards and patterns in the Glidic Flutter App. It should be applied when: (1) Adding logging to new features and components, (2) Implementing error logging and debugging information, (3) Setting up performance monitoring and analytics, (4) Creating structured logging for troubleshooting, (5) Adding user action tracking and audit trails, (6) Implementing development vs production logging strategies.
globs: 
alwaysApply: false
---

# Logging Standards

## Critical Rules

- Use **dart:developer log()** function for consistent logging
- **NO sensitive data** in logs (passwords, tokens, personal information)
- Use **meaningful log messages** with proper context
- Include **name parameter** for categorization and filtering
- Use **error and stackTrace parameters** for error logging
- Implement **different logging strategies** for development vs production
- Follow **consistent naming** patterns for log categories
- Use **structured data** when additional context is needed

## Current Logging Implementation

### Using dart:developer log() Function
```dart
import 'dart:developer' as developer;

// ✅ Correct - Current codebase logging pattern
void logSimpleMessage() {
  developer.log('Operation completed successfully');
}

// ✅ Correct - Logging with name for categorization
void logWithCategory() {
  developer.log(
    'User login attempt',
    name: 'Auth',
  );
}

// ✅ Correct - Error logging with error and stackTrace
void logError(Object error, StackTrace stackTrace) {
  developer.log(
    'Login failed',
    name: 'Auth',
    error: error,
    stackTrace: stackTrace,
  );
}

// ✅ Correct - Current error handler pattern in codebase
class ErrorLogger {
  static void logFailure(
    AppFailure failure, {
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    if (kDebugMode) {
      developer.log(
        'Error: ${failure.message}',
        name: 'ErrorHandler',
        error: failure,
        stackTrace: StackTrace.current,
      );
    }
    
    // Send to external services in production
    _logToService({
      'failure_type': failure.runtimeType.toString(),
      'message': failure.message,
      'context': context,
      ...?additionalData,
    });
  }
}
```

## Logging Categories and Tags

### Logging Categories (Name Parameter)
```dart
// ✅ Use consistent names for log categorization
void logAuthentication() {
  developer.log('User login started', name: 'Auth');
  developer.log('Password validation failed', name: 'Auth');
  developer.log('Token refresh completed', name: 'Auth');
}

void logRecording() {
  developer.log('Recording session started', name: 'Recording');
  developer.log('Audio file saved', name: 'Recording');
  developer.log('Upload to server initiated', name: 'Recording');
}

void logDatabase() {
  developer.log('User data inserted successfully', name: 'Database');
  developer.log('Query executed in 45ms', name: 'Database');
}

void logNetwork() {
  developer.log('API request to /auth/login', name: 'Network');
  developer.log('Response received: 200 OK', name: 'Network');
}

void logNavigation() {
  developer.log('Navigated to HomeScreen', name: 'Navigation');
  developer.log('Route pushed: /recording/123', name: 'Navigation');
}
```

## Use Case Logging Patterns

### Authentication Logging
```dart
class LoginUseCase {
  const LoginUseCase(this._repository);
  
  final AuthRepository _repository;
  
  Future<UserEntity> execute(LoginParams params) async {
    final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
    
    developer.log(
      'Login attempt started for ${_sanitizeEmail(params.email)}',
      name: 'Auth',
    );
    
    try {
      final user = await _repository.login(params);
      
      developer.log(
        'Login successful for user ${user.id}',
        name: 'Auth',
      );
      
      return user;
    } catch (e, stackTrace) {
      developer.log(
        'Login failed for ${_sanitizeEmail(params.email)}',
        name: 'Auth',
        error: e,
        stackTrace: stackTrace,
      );
      
      rethrow;
    }
  }
  
  String _sanitizeEmail(String email) {
    // Mask email for privacy: j***@example.com
    final parts = email.split('@');
    if (parts.length != 2) return '<EMAIL>';
    
    final username = parts[0];
    final domain = parts[1];
    final maskedUsername = username.length > 1 
        ? '${username[0]}${'*' * (username.length - 1)}'
        : '*';
        
    return '$maskedUsername@$domain';
  }
}
```

### Repository Logging
```dart
class RecordingRepositoryImpl implements RecordingRepository {
  @override
  Future<RecordItem> startRecording(RecordingParams params) async {
    developer.log(
      'Starting recording operation with quality: ${params.quality.name}',
      name: 'Recording',
    );
    
    try {
      // Check permissions
      developer.log('Checking recording permissions', name: 'Recording');
      
      final hasPermission = await _checkRecordingPermissions();
      if (!hasPermission) {
        throw const RecordingPermissionFailure('Recording permission denied');
      }
      
      // Start recording
      final recording = await localDataSource.startRecording(params);
      
      developer.log(
        'Recording started successfully with ID: ${recording.id}',
        name: 'Recording',
      );
      
      return recording.toEntity();
    } catch (e, stackTrace) {
      developer.log(
        'Failed to start recording',
        name: 'Recording',
        error: e,
        stackTrace: stackTrace,
      );
      
      throw ExceptionHandler.handle(e, stackTrace);
    }
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct - Cubit logging with dart:developer
  
  class RecordingCubit extends Cubit<RecordingState> {
    RecordingCubit({
      required this.startRecordingUseCase,
      required this.stopRecordingUseCase,
    }) : super(const RecordingInitial()) {
      developer.log('RecordingCubit initialized', name: 'Recording');
    }
    
    final StartRecordingUseCase startRecordingUseCase;
    final StopRecordingUseCase stopRecordingUseCase;
    
    Future<void> startRecording() async {
      developer.log('User initiated recording start', name: 'Recording');
      emit(const RecordingLoading());
      
      try {
        final recording = await startRecordingUseCase.execute(RecordingParams());
        
        emit(RecordingInProgress(recording: recording));
        
        developer.log(
          'Recording started successfully with ID: ${recording.id}',
          name: 'Recording',
        );
      } catch (e, stackTrace) {
        developer.log(
          'Recording start failed',
          name: 'Recording',
          error: e,
          stackTrace: stackTrace,
        );
        
        emit(RecordingError(
          failure: e is AppFailure ? e : UnknownFailure('Recording failed'),
        ));
      }
    }
    
    Future<void> stopRecording() async {
      if (state is! RecordingInProgress) {
        developer.log(
          'Attempted to stop recording when not in progress',
          name: 'Recording',
        );
        return;
      }
      
      final currentState = state as RecordingInProgress;
      
      try {
        final stoppedRecording = await stopRecordingUseCase.execute(
          currentState.recording.id,
        );
        
        emit(RecordingCompleted(recording: stoppedRecording));
        
        developer.log(
          'Recording completed - Duration: ${stoppedRecording.duration.inSeconds}s',
          name: 'Recording',
        );
      } catch (e, stackTrace) {
        developer.log(
          'Recording stop failed',
          name: 'Recording',
          error: e,
          stackTrace: stackTrace,
        );
        
        emit(RecordingError(
          failure: e is AppFailure ? e : UnknownFailure('Stop recording failed'),
        ));
      }
    }
  }
  
  // ✅ Correct - Network logging
  class AuthApiService {
    Future<UserModel> login(LoginRequest request) async {
      developer.log('API request started: POST /auth/login', name: 'Network');
      
      final stopwatch = Stopwatch()..start();
      
      try {
        final response = await dio.post<Map<String, dynamic>>(
          '/auth/login',
          data: request.toJson(),
        );
        
        stopwatch.stop();
        
        developer.log(
          'API request successful - ${response.statusCode} in ${stopwatch.elapsedMilliseconds}ms',
          name: 'Network',
        );
        
        return UserModel.fromJson(response.data!);
      } catch (e, stackTrace) {
        stopwatch.stop();
        
        developer.log(
          'API request failed after ${stopwatch.elapsedMilliseconds}ms',
          name: 'Network',
          error: e,
          stackTrace: stackTrace,
        );
        
        rethrow;
      }
    }
  }
  
  // ✅ Correct - Navigation logging
  void logNavigation() {
    developer.log('Navigated to HomeScreen', name: 'Navigation');
    developer.log('User tapped recording button', name: 'UI');
    developer.log('Database query completed in 45ms', name: 'Database');
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Poor logging practices
  
  class BadLoggingCubit extends Cubit<State> {
    Future<void> login(String email, String password) async {
      print('Starting login'); // ❌ Use developer.log() instead of print
      
      try {
        final user = await loginUseCase.execute(LoginParams(
          email: email,
          password: password,
        ));
        
        developer.log('Login success: ${user.email} ${user.password}'); // ❌ Logging sensitive data
        emit(LoginSuccess(user: user));
      } catch (e) {
        developer.log('Error: $e'); // ❌ No name parameter, no context
        emit(LoginError(e.toString()));
      }
    }
  }
  
  // ❌ Wrong - No logging context
  class BadApiService {
    Future<User> getUser(String id) async {
      try {
        final response = await dio.get('/user/$id');
        return User.fromJson(response.data);
      } catch (e) {
        developer.log('API error'); // ❌ No name, no error parameter, no context
        throw e;
      }
    }
  }
  
  // ❌ Wrong - Logging sensitive information
  class VeryBadLogging {
    void logUserActivity(User user, String action) {
      developer.log(
        'User activity: ${user.email}, password: ${user.password}', // ❌ NEVER log passwords
        name: 'UserActivity',
      );
    }
  }
  ```
</example>

## Performance Monitoring

### Database Query Logging
```dart
class UsersDao {
  Future<List<UserTableData>> getAllUsers() async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final users = await select(usersTable).get();
      
      stopwatch.stop();
      developer.log(
        'Database query completed - ${users.length} users in ${stopwatch.elapsedMilliseconds}ms',
        name: 'Database',
      );
      
      return users;
    } catch (e, stackTrace) {
      stopwatch.stop();
      developer.log(
        'Database query failed after ${stopwatch.elapsedMilliseconds}ms',
        name: 'Database',
        error: e,
        stackTrace: stackTrace,
      );
      
      rethrow;
    }
  }
}
```

### Navigation Logging
```dart
extension LoggedNavigationExtension on BuildContext {
  void loggedGoTo(String route) {
    developer.log(
      'Navigation: ${GoRouterState.of(this).fullPath} -> $route',
      name: 'Navigation',
    );
    
    go(route);
  }
}
```

## Production vs Development Logging

### Environment-Specific Configuration
```dart
class LoggingConfig {
  static void configure() {
    // dart:developer log() automatically handles debug vs production
    // In production builds, logs are automatically filtered
    
    if (kDebugMode) {
      // Development - verbose logging
      developer.log('App started in debug mode', name: 'App');
    } else {
      // Production - minimal logging through external services only
      _initProductionLogging();
    }
  }
  
  static void _initProductionLogging() {
    // Setup external logging services (Firebase Crashlytics, Sentry, etc.)
  }
}
```

## Data Sanitization Patterns

### Email Sanitization for Logs
```dart
String sanitizeEmail(String email) {
  final parts = email.split('@');
  if (parts.length != 2) return '<EMAIL>';
  
  final username = parts[0];
  final domain = parts[1];
  final maskedUsername = username.length > 1 
      ? '${username[0]}${'*' * (username.length - 1)}'
      : '*';
      
  return '$maskedUsername@$domain';
}

// Usage in logging
void logUserActivity(String email, String action) {
  developer.log(
    'User ${sanitizeEmail(email)} performed: $action',
    name: 'UserActivity',
  );
}
```

### Safe Data Logging
```dart
void logUserData(Map<String, dynamic> userData) {
  final safeData = <String, dynamic>{};
  
  for (final entry in userData.entries) {
    final key = entry.key.toLowerCase();
    
    if (_isSensitiveKey(key)) {
      safeData[entry.key] = '***REDACTED***';
    } else {
      safeData[entry.key] = entry.value;
    }
  }
  
  developer.log('User data: $safeData', name: 'UserData');
}

bool _isSensitiveKey(String key) {
  const sensitiveKeys = [
    'password', 'token', 'secret', 'key', 'auth',
    'credential', 'card', 'ssn', 'social', 'pin',
  ];
  
  return sensitiveKeys.any((sensitive) => key.contains(sensitive));
}
```