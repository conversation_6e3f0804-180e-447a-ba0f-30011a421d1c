---
description: This rule governs BLoC/Cubit state management patterns in the Glidic Flutter App. It should be applied when: (1) Creating new Cubit classes for screen state management, (2) Implementing MVVM pattern with View-ViewModel-State structure, (3) Setting up state classes with proper inheritance, (4) Adding exception-based error handling to Cubits, (5) Creating screen-specific state management, (6) Reviewing state management consistency across features.
globs: 
alwaysApply: false
---

# BLoC/Cubit State Management Patterns

## Critical Rules

- Use **Cubit** (not BLoC) for state management following MVVM pattern
- Each screen has dedicated Cubit (ViewModel), State classes, and View
- State classes extend `Equatable` for value equality and performance
- Use exception-based error handling with `AppFailure` hierarchy
- Implement immutable state objects with proper `copyWith()` methods
- Follow naming convention: `ScreenNameCubit`, `ScreenNameState`
- Cubits should be registered as factory in dependency injection
- Use `ErrorHandlingMixin` for consistent error handling across Cubits

## MVVM Structure Pattern

Each screen follows this exact structure:
```
presentation/screens/screen_name/
├── screen_name_screen.dart    # View (UI Component)
├── screen_name_cubit.dart     # ViewModel (Business Logic)
├── screen_name_state.dart     # State (UI State Representation)
└── widgets/                   # Screen-specific widgets
```

## State Class Implementation

### Base State Pattern
```dart
abstract class ScreenNameState extends Equatable {
  const ScreenNameState();
  
  @override
  List<Object?> get props => [];
}

class ScreenNameInitial extends ScreenNameState {
  const ScreenNameInitial();
}

class ScreenNameLoading extends ScreenNameState {
  const ScreenNameLoading();
}

class ScreenNameSuccess extends ScreenNameState {
  const ScreenNameSuccess({required this.data});
  
  final DataType data;
  
  @override
  List<Object?> get props => [data];
}

class ScreenNameError extends ScreenNameState {
  const ScreenNameError({required this.failure});
  
  final AppFailure failure;
  
  @override
  List<Object?> get props => [failure];
}
```

## Cubit Implementation Pattern

### Exception-Based Error Handling
```dart
class LoginCubit extends Cubit<LoginState> {
  LoginCubit({required this.loginUseCase}) : super(const LoginInitial());
  
  final LoginUseCase loginUseCase;
  
  Future<void> login({
    required String email,
    required String password,
  }) async {
    emit(const LoginLoading());
    
    try {
      final user = await loginUseCase.execute(
        LoginParams(email: email, password: password),
      );
      emit(LoginSuccess(user: user));
    } catch (e) {
      // Exception-based error handling
      if (e is AppFailure) {
        emit(LoginError(failure: e));
      } else {
        emit(LoginError(
          failure: UnknownFailure('Login failed: ${e.toString()}'),
        ));
      }
    }
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct Cubit implementation
  class HomeCubit extends Cubit<HomeState> {
    HomeCubit({
      required this.getDashboardDataUseCase,
      required this.logoutUseCase,
    }) : super(const HomeInitial());
    
    final GetDashboardDataUseCase getDashboardDataUseCase;
    final LogoutUseCase logoutUseCase;
    
    /// Load dashboard data with proper error handling
    Future<void> loadDashboardData() async {
      emit(const HomeLoading());
      
      try {
        final data = await getDashboardDataUseCase.execute();
        emit(HomeLoaded(dashboardData: data));
      } catch (e) {
        if (e is AppFailure) {
          emit(HomeError(failure: e));
        } else {
          emit(const HomeError(
            failure: UnknownFailure('Failed to load dashboard data'),
          ));
        }
      }
    }
  }
  
  // ✅ Correct State implementation
  abstract class HomeState extends Equatable {
    const HomeState();
    
    @override
    List<Object?> get props => [];
  }
  
  class HomeInitial extends HomeState {
    const HomeInitial();
  }
  
  class HomeLoading extends HomeState {
    const HomeLoading();
  }
  
  class HomeLoaded extends HomeState {
    const HomeLoaded({required this.dashboardData});
    
    final DashboardData dashboardData;
    
    @override
    List<Object?> get props => [dashboardData];
  }
  
  class HomeError extends HomeState {
    const HomeError({required this.failure});
    
    final AppFailure failure;
    
    @override
    List<Object?> get props => [failure];
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Using BLoC instead of Cubit
  class LoginBloc extends Bloc<LoginEvent, LoginState> { } // ❌
  
  // ❌ Wrong - Mutable state
  class LoginState {
    bool isLoading; // ❌ Should be immutable
    String? error;  // ❌ Should use AppFailure
  }
  
  // ❌ Wrong - Either pattern instead of exceptions
  Future<void> login() async {
    final result = await loginUseCase.execute();
    result.fold(
      (failure) => emit(LoginError(failure)), // ❌ Old pattern
      (user) => emit(LoginSuccess(user)),
    );
  }
  
  // ❌ Wrong - Not extending Equatable
  class LoginState { } // ❌ Should extend Equatable
  ```
</example>

## Screen Integration Pattern

### View (Screen) Implementation
```dart
class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});
  
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<LoginCubit>(),
      child: BlocConsumer<LoginCubit, LoginState>(
        listener: (context, state) {
          if (state is LoginSuccess) {
            context.go('/home');
          } else if (state is LoginError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.failure.message)),
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            body: Column(
              children: [
                // UI components
                if (state is LoginLoading)
                  const CircularProgressIndicator(),
                ElevatedButton(
                  onPressed: state is LoginLoading ? null : () {
                    context.read<LoginCubit>().login(
                      email: emailController.text,
                      password: passwordController.text,
                    );
                  },
                  child: const Text('Login'),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
```

## Validation State Pattern

For forms with validation:
```dart
class LoginFormValidation extends LoginState {
  const LoginFormValidation({
    this.emailError,
    this.passwordError,
    required this.isFormValid,
  });
  
  final String? emailError;
  final String? passwordError;
  final bool isFormValid;
  
  @override
  List<Object?> get props => [emailError, passwordError, isFormValid];
}

// In Cubit
void validateForm({required String email, required String password}) {
  final emailError = validateEmail(email);
  final passwordError = validatePassword(password);
  final isFormValid = emailError == null && passwordError == null;
  
  emit(LoginFormValidation(
    emailError: emailError,
    passwordError: passwordError,
    isFormValid: isFormValid,
  ));
}
```

## Error Handling Mixin Usage

For advanced error handling, use the provided mixin:
```dart
class RecordingCubit extends Cubit<RecordingState> with ErrorHandlingMixin {
  RecordingCubit({required this.startRecordingUseCase}) 
      : super(const RecordingInitial());
  
  Future<void> startRecording() async {
    await executeWithErrorHandling<void>(
      () => startRecordingUseCase.execute(),
      onSuccess: (_) => emit(const RecordingStarted()),
      onLoading: () => emit(const RecordingLoading()),
      errorContext: 'Starting recording',
      canRetry: true,
      retryAction: startRecording,
    );
  }
  
  @override
  void emitErrorState(
    AppFailure failure, {
    bool canRetry = true,
    VoidCallback? retryAction,
  }) {
    emit(RecordingError(failure: failure, canRetry: canRetry));
  }
}
```

## Dependency Injection Pattern

Register Cubits as factories in feature DI:
```dart
class AuthDI {
  static Future<void> init() async {
    // Use Cases
    sl.registerFactory(() => LoginUseCase(sl()));
    
    // Cubits - Always use factory registration
    sl.registerFactory(() => LoginCubit(loginUseCase: sl()));
  }
}
```

## Complex State Management

For complex screens with multiple data sources:
```dart
class DashboardLoadedState extends HomeState {
  const DashboardLoadedState({
    required this.userStats,
    required this.recentRecordings,
    required this.upcomingEvents,
    this.isRefreshing = false,
  });
  
  final UserStats userStats;
  final List<RecordItem> recentRecordings;
  final List<CalendarEventEntity> upcomingEvents;
  final bool isRefreshing;
  
  DashboardLoadedState copyWith({
    UserStats? userStats,
    List<RecordItem>? recentRecordings,
    List<CalendarEventEntity>? upcomingEvents,
    bool? isRefreshing,
  }) {
    return DashboardLoadedState(
      userStats: userStats ?? this.userStats,
      recentRecordings: recentRecordings ?? this.recentRecordings,
      upcomingEvents: upcomingEvents ?? this.upcomingEvents,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
  
  @override
  List<Object?> get props => [userStats, recentRecordings, upcomingEvents, isRefreshing];
}
```

## Testing Cubits

```dart
void main() {
  group('LoginCubit', () {
    late LoginCubit loginCubit;
    late MockLoginUseCase mockLoginUseCase;
    
    setUp(() {
      mockLoginUseCase = MockLoginUseCase();
      loginCubit = LoginCubit(loginUseCase: mockLoginUseCase);
    });
    
    test('should emit [LoginLoading, LoginSuccess] when login is successful', () async {
      // Arrange
      when(() => mockLoginUseCase.execute(any()))
          .thenAnswer((_) async => mockUserEntity);
      
      // Act
      await loginCubit.login(email: '<EMAIL>', password: 'password');
      
      // Assert
      expect(
        loginCubit.stream,
        emitsInOrder([
          const LoginLoading(),
          LoginSuccess(user: mockUserEntity),
        ]),
      );
    });
  });
}
```