---
description: This rule governs Repository pattern implementation in the Glidic Flutter App following Clean Architecture. It should be applied when: (1) Creating new repository interfaces in domain layer, (2) Implementing concrete repositories in data layer, (3) Setting up multiple data sources coordination, (4) Implementing caching strategies, (5) Adding network connectivity checks, (6) Converting between Models and Entities, (7) Implementing consistent error handling across repositories.
globs: 
alwaysApply: false
---

# Repository Pattern Implementation

## Critical Rules

- Repository **interfaces** go in `domain/repositories/` (abstract classes)
- Repository **implementations** go in `data/repositories/` (concrete classes)
- Repository implementations coordinate between multiple data sources
- Always implement network connectivity checks before remote operations
- Handle Model ↔ Entity conversions within repository implementations
- Use exception-based error handling with `AppFailure` hierarchy
- Implement proper caching strategies for offline-first functionality
- Repository implementations should be registered as singletons in DI

## Architecture Pattern

```
Domain Layer (Interfaces)          Data Layer (Implementations)
┌─────────────────────────┐        ┌─────────────────────────┐
│  AuthRepository         │◄───────┤  AuthRepositoryImpl     │
│  (Abstract Interface)   │        │  (Concrete Class)       │
└─────────────────────────┘        └─────────────────────────┘
                                               │
                                   ┌───────────┴───────────┐
                                   ▼                       ▼
                          RemoteDataSource         LocalDataSource
                            (API calls)           (Database/Cache)
```

## Repository Interface Pattern

### Domain Layer Interface
```dart
abstract class AuthRepository {
  /// Login user with credentials
  /// Throws [AuthFailure] on authentication errors
  /// Throws [NetworkFailure] on network errors
  Future<UserEntity> login(LoginParams params);
  
  /// Logout current user
  Future<void> logout();
  
  /// Get current authenticated user
  /// Returns null if no user is logged in
  Future<UserEntity?> getCurrentUser();
  
  /// Check if user is authenticated
  Future<bool> isAuthenticated();
}
```

## Repository Implementation Pattern

### Data Layer Implementation
```dart
class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });
  
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  final NetworkInfo networkInfo;
  
  @override
  Future<UserEntity> login(LoginParams params) async {
    try {
      // Always try remote first for authentication
      final userModel = await remoteDataSource.login(params);
      
      // Cache successful login locally
      await localDataSource.saveUser(userModel);
      await localDataSource.saveAuthToken(userModel.token);
      
      // Convert Model to Entity for domain layer
      return userModel.toEntity();
    } catch (e) {
      // Handle and convert exceptions to domain failures
      throw ExceptionHandler.handle(e);
    }
  }
  
  @override
  Future<UserEntity?> getCurrentUser() async {
    try {
      // Try local cache first (offline-first approach)
      final localUser = await localDataSource.getUser();
      if (localUser != null) {
        return localUser.toEntity();
      }
      
      // Check network connectivity before remote call
      if (await networkInfo.isConnected) {
        final remoteUser = await remoteDataSource.getCurrentUser();
        await localDataSource.saveUser(remoteUser);
        return remoteUser.toEntity();
      }
      
      return null;
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct Repository Interface (Domain Layer)
  abstract class RecordingRepository {
    /// Start recording session
    Future<RecordItem> startRecording(RecordingParams params);
    
    /// Stop current recording
    Future<RecordItem> stopRecording(String sessionId);
    
    /// Get all recordings with pagination
    Future<List<RecordItem>> getRecordings({
      int page = 1,
      int limit = 20,
      RecordingFilter? filter,
    });
    
    /// Get recording by ID (try local first, then remote)
    Future<RecordItem?> getRecording(String id);
    
    /// Upload recording to remote storage
    Future<RecordItem> uploadRecording(String localPath);
    
    /// Delete recording (both local and remote)
    Future<void> deleteRecording(String id);
  }
  
  // ✅ Correct Repository Implementation (Data Layer)
  class RecordingRepositoryImpl implements RecordingRepository {
    RecordingRepositoryImpl({
      required this.remoteDataSource,
      required this.localDataSource,
      required this.fileStorageDataSource,
      required this.networkInfo,
    });
    
    final RecordingRemoteDataSource remoteDataSource;
    final RecordingLocalDataSource localDataSource;
    final FileStorageDataSource fileStorageDataSource;
    final NetworkInfo networkInfo;
    
    @override
    Future<List<RecordItem>> getRecordings({
      int page = 1,
      int limit = 20,
      RecordingFilter? filter,
    }) async {
      try {
        // Offline-first: Get local recordings
        final localRecordings = await localDataSource.getRecordings(
          offset: (page - 1) * limit,
          limit: limit,
          filter: filter,
        );
        
        // Try to sync with remote if connected
        if (await networkInfo.isConnected) {
          try {
            final remoteRecordings = await remoteDataSource.getRecordings(
              page: page,
              limit: limit,
              filter: filter,
            );
            
            // Merge and update local cache
            await _syncRecordings(remoteRecordings);
            
            // Return fresh data from local cache
            return await localDataSource.getRecordings(
              offset: (page - 1) * limit,
              limit: limit,
              filter: filter,
            ).then((models) => models.map((m) => m.toEntity()).toList());
          } catch (e) {
            // Network error - continue with local data
            log('Failed to sync recordings: $e');
          }
        }
        
        // Return local recordings as entities
        return localRecordings.map((model) => model.toEntity()).toList();
      } catch (e) {
        throw ExceptionHandler.handle(e);
      }
    }
    
    @override
    Future<RecordItem> uploadRecording(String localPath) async {
      if (!await networkInfo.isConnected) {
        throw const NetworkFailure('No internet connection');
      }
      
      try {
        // Upload file first
        final remoteUrl = await fileStorageDataSource.uploadFile(localPath);
        
        // Create recording record on server
        final recordingModel = await remoteDataSource.createRecording(
          CreateRecordingRequest(
            localPath: localPath,
            remoteUrl: remoteUrl,
          ),
        );
        
        // Update local record with remote info
        await localDataSource.updateRecording(recordingModel);
        
        return recordingModel.toEntity();
      } catch (e) {
        throw ExceptionHandler.handle(e);
      }
    }
    
    /// Sync remote recordings with local cache
    Future<void> _syncRecordings(List<RecordingSessionModel> remoteRecordings) async {
      for (final remoteRecording in remoteRecordings) {
        final localRecording = await localDataSource.getRecording(remoteRecording.id);
        
        if (localRecording == null) {
          // New recording from remote
          await localDataSource.saveRecording(remoteRecording);
        } else if (remoteRecording.updatedAt.isAfter(localRecording.updatedAt)) {
          // Remote is newer - update local
          await localDataSource.updateRecording(remoteRecording);
        }
      }
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Repository doing UI logic
  class BadAuthRepository implements AuthRepository {
    Future<UserEntity> login(LoginParams params) async {
      final user = await remoteDataSource.login(params);
      
      // ❌ Repository should not handle UI concerns
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Login successful')),
      );
      
      return user.toEntity();
    }
  }
  
  // ❌ Wrong - No error handling
  class BadRecordingRepository implements RecordingRepository {
    Future<List<RecordItem>> getRecordings() async {
      // ❌ No try-catch, no network check, no error handling
      final recordings = await remoteDataSource.getRecordings();
      return recordings.map((r) => r.toEntity()).toList();
    }
  }
  
  // ❌ Wrong - Direct Entity usage instead of Model conversion
  class BadRepositoryImpl implements AuthRepository {
    Future<UserEntity> login(LoginParams params) async {
      // ❌ Should return Model and convert to Entity
      return await remoteDataSource.login(params); // Returns Entity directly
    }
  }
  
  // ❌ Wrong - Either pattern instead of exceptions
  class OldPatternRepository implements AuthRepository {
    Future<Either<Failure, UserEntity>> login(LoginParams params) async {
      try {
        final user = await remoteDataSource.login(params);
        return Right(user.toEntity());
      } catch (e) {
        return Left(AuthFailure('Login failed'));
      }
    }
  }
  ```
</example>

## Multi-DataSource Coordination

### Complex Repository with Multiple Sources
```dart
class CalendarEventRepositoryImpl implements CalendarEventRepository {
  CalendarEventRepositoryImpl({
    required this.localCalendarDataSource,
    required this.googleCalendarDataSource,
    required this.remoteDataSource,
    required this.networkInfo,
  });
  
  final LocalCalendarDataSource localCalendarDataSource;
  final GoogleCalendarDataSource googleCalendarDataSource;
  final CalendarRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  
  @override
  Future<List<CalendarEventEntity>> getEvents({
    required DateTimeRange dateRange,
    CalendarSource source = CalendarSource.all,
  }) async {
    try {
      final List<CalendarEventModel> allEvents = [];
      
      // Get local calendar events
      if (source == CalendarSource.all || source == CalendarSource.local) {
        final localEvents = await localCalendarDataSource.getEvents(dateRange);
        allEvents.addAll(localEvents);
      }
      
      // Get Google Calendar events if authenticated and connected
      if ((source == CalendarSource.all || source == CalendarSource.google) && 
          await networkInfo.isConnected) {
        try {
          final googleEvents = await googleCalendarDataSource.getEvents(dateRange);
          allEvents.addAll(googleEvents);
        } catch (e) {
          log('Failed to fetch Google Calendar events: $e');
        }
      }
      
      // Sync with remote backend if connected
      if (await networkInfo.isConnected) {
        try {
          final remoteEvents = await remoteDataSource.getEvents(dateRange);
          allEvents.addAll(remoteEvents);
        } catch (e) {
          log('Failed to fetch remote events: $e');
        }
      }
      
      // Remove duplicates and convert to entities
      final uniqueEvents = _removeDuplicateEvents(allEvents);
      return uniqueEvents.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }
  
  List<CalendarEventModel> _removeDuplicateEvents(List<CalendarEventModel> events) {
    final Map<String, CalendarEventModel> uniqueEventsMap = {};
    
    for (final event in events) {
      final key = '${event.title}_${event.startTime}_${event.endTime}';
      if (!uniqueEventsMap.containsKey(key)) {
        uniqueEventsMap[key] = event;
      }
    }
    
    return uniqueEventsMap.values.toList();
  }
}
```

## Caching Strategies

### Offline-First with TTL
```dart
class HomeRepositoryImpl implements HomeRepository {
  @override
  Future<DashboardData> getDashboardData({bool forceRefresh = false}) async {
    try {
      // Check cache first unless force refresh
      if (!forceRefresh) {
        final cachedData = await localDataSource.getCachedDashboardData();
        if (cachedData != null && !_isCacheExpired(cachedData.cachedAt)) {
          return cachedData.toEntity();
        }
      }
      
      // Fetch fresh data if connected
      if (await networkInfo.isConnected) {
        final freshData = await remoteDataSource.getDashboardData();
        
        // Update cache
        await localDataSource.saveDashboardData(freshData);
        
        return freshData.toEntity();
      }
      
      // Return stale cache if no network
      final staleData = await localDataSource.getCachedDashboardData();
      if (staleData != null) {
        return staleData.toEntity();
      }
      
      throw const NetworkFailure('No data available offline');
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }
  
  bool _isCacheExpired(DateTime cachedAt) {
    const cacheValidDuration = Duration(minutes: 5);
    return DateTime.now().difference(cachedAt) > cacheValidDuration;
  }
}
```

## Error Handling Patterns

### Consistent Exception Handling
```dart
class RepositoryImpl implements Repository {
  Future<Entity> someOperation() async {
    try {
      // Perform operation
      final result = await dataSource.operation();
      return result.toEntity();
    } on DioException catch (e) {
      // Network-specific error handling
      throw NetworkFailure.fromDioException(e);
    } on DatabaseException catch (e) {
      // Database-specific error handling
      throw DatabaseFailure(e.message);
    } catch (e) {
      // Generic error handling
      throw ExceptionHandler.handle(e);
    }
  }
}
```

## Testing Repositories

### Mock DataSource Pattern
```dart
void main() {
  group('AuthRepositoryImpl', () {
    late AuthRepositoryImpl repository;
    late MockAuthRemoteDataSource mockRemoteDataSource;
    late MockAuthLocalDataSource mockLocalDataSource;
    late MockNetworkInfo mockNetworkInfo;
    
    setUp(() {
      mockRemoteDataSource = MockAuthRemoteDataSource();
      mockLocalDataSource = MockAuthLocalDataSource();
      mockNetworkInfo = MockNetworkInfo();
      
      repository = AuthRepositoryImpl(
        remoteDataSource: mockRemoteDataSource,
        localDataSource: mockLocalDataSource,
        networkInfo: mockNetworkInfo,
      );
    });
    
    test('should return UserEntity when login is successful', () async {
      // Arrange
      when(() => mockRemoteDataSource.login(any()))
          .thenAnswer((_) async => mockUserModel);
      when(() => mockLocalDataSource.saveUser(any()))
          .thenAnswer((_) async {});
      
      // Act
      final result = await repository.login(mockLoginParams);
      
      // Assert
      expect(result, isA<UserEntity>());
      verify(() => mockLocalDataSource.saveUser(mockUserModel));
    });
  });
}
```