---
description: 
globs: **/*.dart
alwaysApply: false
---

# Constants Usage Enforcement

## Critical Rules

- **NO hardcoded values** are allowed in code
- All values must be defined in appropriate constants files in `lib/core/common/constants/`
- Use the barrel export file `constants.dart` for importing all constants
- Follow established constants naming patterns with descriptive prefixes
- Group related constants logically within constant classes
- Constants classes must have private constructors to prevent instantiation

## Constants Categories and Usage

### Colors → `ColorConstants`
```dart
// ✅ Correct - Using color constants from codebase
Container(
  color: ColorConstants.primaryColor, // Color(0xFF8BAABD)
  child: Text(
    'Title',
    style: TextStyle(color: ColorConstants.textPrimary), // Color(0xFF212121)
  ),
)

// Available colors in codebase:
// - ColorConstants.primaryColor = Color(0xFF8BAABD)
// - ColorConstants.backgroundColor = Color(0xFFFFFFFF)
// - ColorConstants.textPrimary = Color(0xFF212121)
// - ColorConstants.textSecondary = Color(0xFF757575)
// - ColorConstants.successColor = Color(0xFF4CAF50)
// - ColorConstants.errorColor = Color(0xFFFF0000)

// ❌ Wrong - Hardcoded colors
Container(
  color: Color(0xFF8BAABD), // ❌ MUST use ColorConstants.primaryColor
  child: Text(
    'Title',
    style: TextStyle(color: Color(0xFF212121)), // ❌ MUST use ColorConstants.textPrimary
  ),
)
```

### Dimensions → `Dimensions`
```dart
// ✅ Correct - Using dimension constants from codebase
Padding(
  padding: EdgeInsets.all(Dimensions.defaultPadding), // 16.0
  child: Container(
    height: Dimensions.buttonHeight, // 48.0
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(Dimensions.radiusMd), // 8.0
    ),
  ),
)

// Available dimensions in codebase:
// - Dimensions.defaultPadding = 16.0
// - Dimensions.buttonHeight = 48.0
// - Dimensions.radiusMd = 8.0
// - Dimensions.radiusLg = 10.0
// - Dimensions.gapMd = 8.0
// - Dimensions.gapLg = 10.0
// - Dimensions.iconMd = 24.0

// ❌ Wrong - Hardcoded dimensions
Padding(
  padding: EdgeInsets.all(16.0), // ❌ MUST use Dimensions.defaultPadding
  child: Container(
    height: 48.0, // ❌ MUST use Dimensions.buttonHeight
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8.0), // ❌ MUST use Dimensions.radiusMd
    ),
  ),
)
```

### Text Styles → `TextStyleConstants`
```dart
// ✅ Correct - Using text style constants
Text(
  'Welcome',
  style: TextStyleConstants.title,
)

Text(
  'Description',
  style: TextStyleConstants.body,
)

// ❌ Wrong - Hardcoded text styles
Text(
  'Welcome',
  style: TextStyle( // ❌ MUST use TextStyleConstants.title
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: Color(0xFF212121),
  ),
)
```

### Asset Paths → `PathConstants`
```dart
// ✅ Correct - Using path constants
SvgPicture.asset(PathConstants.homeIcon)
Image.asset(PathConstants.logoImage)

// ❌ Wrong - Hardcoded paths
SvgPicture.asset('assets/icons/home.svg') // ❌ MUST use PathConstants.homeIcon
Image.asset('assets/images/logo.svg') // ❌ MUST use PathConstants.logoImage
```

### Storage Keys → `StorageConstants`
```dart
// ✅ Correct - Using storage constants
await secureStorage.write(
  key: StorageConstants.accessTokenKey,
  value: token,
)

// ❌ Wrong - Hardcoded storage keys
await secureStorage.write(
  key: 'access_token', // ❌ MUST use StorageConstants.accessTokenKey
  value: token,
)
```

### Network Values → `NetworkConstants`
```dart
// ✅ Correct - Using network constants from codebase
dio.options.connectTimeout = NetworkConstants.connectTimeout; // Duration(seconds: 30)
dio.options.baseUrl = NetworkConstants.baseUrl; // From EnvConfig
final loginEndpoint = NetworkConstants.authLogin; // '/auth/login'

// Available network constants in codebase:
// - NetworkConstants.baseUrl (from EnvConfig)
// - NetworkConstants.connectTimeout = Duration(seconds: 30)
// - NetworkConstants.authLogin = '/auth/login'
// - NetworkConstants.authRegister = '/auth/register'
// - NetworkConstants.homeDashboard = '/dashboard'

// ❌ Wrong - Hardcoded network values
dio.options.connectTimeout = Duration(seconds: 30); // ❌ MUST use NetworkConstants.connectTimeout
dio.options.baseUrl = 'https://api.example.com'; // ❌ MUST use NetworkConstants.baseUrl
```

## Constants File Organization

### Feature-Specific Constants
```dart
// lib/features/auth/data/constants/auth_constants.dart
class AuthConstants {
  AuthConstants._(); // Private constructor
  
  // UI Labels
  static const String labelEmail = 'Email Address';
  static const String labelPassword = 'Password';
  static const String buttonTextLogin = 'Sign In';
  static const String buttonTextSignUp = 'Create Account';
  
  // Error Messages
  static const String errorEmailRequired = 'Email is required';
  static const String errorPasswordRequired = 'Password is required';
  static const String errorInvalidCredentials = 'Invalid email or password';
  
  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const String emailRegexPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  // API Endpoints
  static const String apiLogin = '/auth/login';
  static const String apiLogout = '/auth/logout';
  static const String apiRefreshToken = '/auth/refresh';
}
```

### Database Constants
```dart
// ✅ Correct - Using database constants from codebase
class UsersDao {
  Future<void> createUser(UserModel user) {
    return database.into(DatabaseConstants.usersTable).insert(user); // 'users'
  }
}

// Available database constants in codebase:
// - DatabaseConstants.databaseName = 'app_database.db'
// - DatabaseConstants.usersTable = 'users'
// - DatabaseConstants.recordsTable = 'records'
// - DatabaseConstants.tokensTable = 'tokens'
// - DatabaseConstants.userIdColumn = 'id'
// - DatabaseConstants.userEmailColumn = 'email'

// ❌ Wrong - Hardcoded table names
class UsersDao {
  Future<void> createUser(UserModel user) {
    return database.into('users').insert(user); // ❌ MUST use DatabaseConstants.usersTable
  }
}
```

## String Constants Patterns

### User-Facing Strings MUST Use Localization
```dart
// ✅ Correct - Using localization for user-facing strings
final l10n = AppLocalizations.of(context)!;
Text(l10n.auth_screen_title_login)
ElevatedButton(
  onPressed: onSave,
  child: Text(l10n.button_save),
)

// ❌ Wrong - Hardcoded user-facing strings
Text('Sign In') // ❌ MUST use l10n.auth_screen_title_login
Text('Save') // ❌ MUST use l10n.button_save
```

### Technical Constants (Non-User-Facing)
```dart
// ✅ Technical constants from codebase are OK
class ErrorCodes {
  static const String noConnection = 'NO_CONNECTION';
  static const String invalidCredentials = 'INVALID_CREDENTIALS';
  static const String tokenExpired = 'TOKEN_EXPIRED';
}

class Constants {
  static const String appGroupId = 'group.jp.co.iotbank.glidici.shared';
  static const String defaultUploadFileName = 'audio_file.m4a';
  static const int maxUploadFileSize = 100 * 1024 * 1024;
}

// ✅ Storage keys are technical constants
class StorageConstants {
  static const String accessTokenKey = 'access_token';
  static const String userDataKey = 'user_data';
}
```

## Time and Duration Constants
```dart
// ✅ Correct - Time constants from codebase
class NetworkConstants {
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
}

class Constants {
  static const int uploadWorkerInterval = 1;
  static const int liveActivityUpdateInterval = 100;
  static const int millisecondsPerSecond = 1000;
}

// Usage with existing constants
dio.options.connectTimeout = NetworkConstants.connectTimeout;
Timer.periodic(
  Duration(seconds: Constants.uploadWorkerInterval),
  (timer) => uploadWorker(),
);

// ❌ Wrong - Hardcoded durations
dio.options.connectTimeout = Duration(seconds: 30); // ❌ Use NetworkConstants.connectTimeout
Timer.periodic(Duration(seconds: 1), (timer) => uploadWorker()); // ❌ Use Constants.uploadWorkerInterval
```

## Import Pattern
```dart
// Always import through the barrel file
import 'package:glidic_app/core/common/constants/constants.dart';

// Access constants directly
final color = ColorConstants.primaryColor;
final padding = Dimensions.defaultPadding;
final title = TextStyleConstants.title;
final icon = PathConstants.homeIcon;
```

## Constants Validation Rules

### Required Constant Categories
1. **Colors** - All Color() instantiations must use ColorConstants
2. **Dimensions** - All hardcoded double values must use Dimensions
3. **Text Styles** - All TextStyle() instantiations must use TextStyleConstants
4. **Asset Paths** - All asset path strings must use PathConstants
5. **Storage Keys** - All storage key strings must use StorageConstants
6. **API Endpoints** - All endpoint strings must use NetworkConstants or feature constants
7. **Duration Values** - All Duration() instantiations must use TimeConstants
8. **Status Messages** - All user-facing strings must use feature constants

### Pattern Enforcement
- Search for: `Color(0x` → Must use `ColorConstants.*`
- Search for: `EdgeInsets.all(` with literal numbers → Must use `Dimensions.*`
- Search for: `TextStyle(` → Must use `TextStyleConstants.*`
- Search for: `'assets/` → Must use `PathConstants.*`
- Search for: Duration literals → Must use `TimeConstants.*`
- Search for: String literals in UI → Must use feature constants