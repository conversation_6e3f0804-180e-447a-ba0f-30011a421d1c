---
description: This rule governs error handling patterns in the Glidic Flutter App using exception-based approach with AppFailure hierarchy. It should be applied when: (1) Implementing error handling in use cases, repositories, and Cubits, (2) Creating custom failure types for specific domains, (3) Setting up exception handling in data sources, (4) Converting exceptions to domain failures, (5) Implementing user-friendly error messages, (6) Adding retry mechanisms and error recovery.
globs: 
alwaysApply: false
---

# Error Handling Patterns

## Critical Rules

- Use **exception-based error handling** with `AppFailure` hierarchy
- **Use Cases** throw `AppFailure` on error, return success value directly
- **Repositories** convert data source exceptions to domain failures
- **Cubits** catch `AppFailure` and emit error states with user-friendly messages
- Create specific failure types for different error scenarios
- Use `ExceptionHandler.handle()` for converting generic exceptions
- Implement retry mechanisms for recoverable failures
- Log errors with context using `ErrorLogger`

## AppFailure Hierarchy

### Base Failure Class
```dart
abstract class AppFailure extends Equatable implements Exception {
  const AppFailure({
    required this.message,
    this.canRetry = false,
    this.isCritical = false,
  });
  
  final String message;
  final bool canRetry;
  final bool isCritical;
  
  @override
  List<Object?> get props => [message, canRetry, isCritical];
}
```

### Specific Failure Types
```dart
class NetworkFailure extends AppFailure {
  const NetworkFailure(String message, {bool canRetry = true}) 
      : super(message: message, canRetry: canRetry);
}

class AuthFailure extends AppFailure {
  const AuthFailure(String message, {bool canRetry = false}) 
      : super(message: message, canRetry: canRetry);
}

class ValidationFailure extends AppFailure {
  const ValidationFailure(String message) 
      : super(message: message, canRetry: false);
}

class DatabaseFailure extends AppFailure {
  const DatabaseFailure(String message, {bool canRetry = true}) 
      : super(message: message, canRetry: canRetry);
}

class UnknownFailure extends AppFailure {
  const UnknownFailure(String message) 
      : super(message: message, canRetry: false, isCritical: true);
}
```

## Use Case Error Handling

### Exception-Based Pattern
```dart
class LoginUseCase {
  const LoginUseCase(this._repository);
  
  final AuthRepository _repository;
  
  /// Throws AppFailure on error, returns success value directly
  Future<UserEntity> execute(LoginParams params) async {
    // Validation
    if (params.email.isEmpty) {
      throw const ValidationFailure('Email is required');
    }
    
    if (params.password.length < 6) {
      throw const ValidationFailure('Password must be at least 6 characters');
    }
    
    try {
      return await _repository.login(params);
    } catch (e) {
      // Re-throw AppFailure as-is
      if (e is AppFailure) rethrow;
      
      // Convert unknown exceptions
      throw ExceptionHandler.handle(e);
    }
  }
}
```

## Repository Error Handling

### Data Source Exception Conversion
```dart
class AuthRepositoryImpl implements AuthRepository {
  @override
  Future<UserEntity> login(LoginParams params) async {
    try {
      final userModel = await remoteDataSource.login(params);
      await localDataSource.saveUser(userModel);
      return userModel.toEntity();
    } on DioException catch (e, stackTrace) {
      // Convert Dio exceptions to domain failures
      throw _handleDioException(e);
    } on DatabaseException catch (e) {
      throw DatabaseFailure('Failed to save user data: ${e.message}');
    } catch (e, stackTrace) {
      // Log unknown errors
      ErrorLogger.logException(
        e,
        stackTrace: stackTrace,
        context: 'AuthRepository.login',
        additionalData: {'email': params.email},
      );
      
      throw ExceptionHandler.handle(e, stackTrace);
    }
  }
  
  AppFailure _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
        return const NetworkFailure(
          'Connection timeout. Please check your internet connection.',
          canRetry: true,
        );
      
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        switch (statusCode) {
          case 401:
            return const AuthFailure('Invalid email or password');
          case 403:
            return const AuthFailure('Account is disabled');
          case 429:
            return const NetworkFailure(
              'Too many login attempts. Please try again later.',
              canRetry: true,
            );
          default:
            return NetworkFailure(
              'Server error (${statusCode}). Please try again.',
              canRetry: true,
            );
        }
      
      case DioExceptionType.cancel:
        return const NetworkFailure('Request was cancelled');
      
      default:
        return const NetworkFailure(
          'Network error. Please check your connection.',
          canRetry: true,
        );
    }
  }
}
```

## Cubit Error Handling

### State-Based Error Management
```dart
class LoginCubit extends Cubit<LoginState> {
  LoginCubit({required this.loginUseCase}) : super(const LoginInitial());
  
  final LoginUseCase loginUseCase;
  
  Future<void> login({
    required String email,
    required String password,
  }) async {
    emit(const LoginLoading());
    
    try {
      final user = await loginUseCase.execute(
        LoginParams(email: email, password: password),
      );
      emit(LoginSuccess(user: user));
    } catch (e) {
      // Handle AppFailure with user-friendly messages
      if (e is AppFailure) {
        emit(LoginError(
          failure: e,
          canRetry: e.canRetry,
        ));
      } else {
        // Fallback for unexpected errors
        emit(const LoginError(
          failure: UnknownFailure('An unexpected error occurred'),
          canRetry: false,
        ));
      }
    }
  }
  
  /// Retry last failed operation
  void retryLogin() {
    if (state is LoginError) {
      final errorState = state as LoginError;
      if (errorState.canRetry) {
        // Re-execute with cached parameters
        login(email: _lastEmail, password: _lastPassword);
      }
    }
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct Use Case with exception handling
  class StartRecordingUseCase {
    const StartRecordingUseCase(this._repository);
    
    final RecordingRepository _repository;
    
    Future<RecordItem> execute(RecordingParams params) async {
      // Validate permissions
      if (!await _hasRecordingPermission()) {
        throw const ValidationFailure('Recording permission is required');
      }
      
      // Validate storage space
      if (!await _hasEnoughStorageSpace()) {
        throw const ValidationFailure('Insufficient storage space');
      }
      
      try {
        return await _repository.startRecording(params);
      } catch (e) {
        if (e is AppFailure) rethrow;
        throw ExceptionHandler.handle(e);
      }
    }
  }
  
  // ✅ Correct Cubit with retry mechanism
  class RecordingCubit extends Cubit<RecordingState> {
    Future<void> startRecording(RecordingParams params) async {
      emit(const RecordingLoading());
      
      try {
        final recording = await startRecordingUseCase.execute(params);
        emit(RecordingStarted(recording: recording));
      } catch (e) {
        if (e is AppFailure) {
          emit(RecordingError(
            failure: e,
            canRetry: e.canRetry,
            retryAction: () => startRecording(params),
          ));
        } else {
          emit(const RecordingError(
            failure: UnknownFailure('Failed to start recording'),
            canRetry: false,
          ));
        }
      }
    }
  }
  
  // ✅ Correct UI error handling
  class RecordingScreen extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return BlocConsumer<RecordingCubit, RecordingState>(
        listener: (context, state) {
          if (state is RecordingError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.failure.message),
                action: state.canRetry
                    ? SnackBarAction(
                        label: 'Retry',
                        onPressed: state.retryAction,
                      )
                    : null,
              ),
            );
          }
        },
        builder: (context, state) {
          // UI implementation
        },
      );
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Either pattern (old approach)
  class OldLoginUseCase {
    Future<Either<Failure, UserEntity>> execute(LoginParams params) async {
      try {
        final user = await repository.login(params);
        return Right(user);
      } catch (e) {
        return Left(AuthFailure('Login failed'));
      }
    }
  }
  
  // ❌ Wrong - Generic Exception instead of AppFailure
  class BadUseCase {
    Future<UserEntity> execute() async {
      if (someCondition) {
        throw Exception('Something went wrong'); // ❌ Use AppFailure
      }
      return await repository.getData();
    }
  }
  
  // ❌ Wrong - No error handling in Cubit
  class BadCubit extends Cubit<State> {
    Future<void> loadData() async {
      emit(Loading());
      final data = await useCase.execute(); // ❌ No try-catch
      emit(Loaded(data));
    }
  }
  
  // ❌ Wrong - Swallowing exceptions
  class BadRepository implements Repository {
    Future<Entity> getData() async {
      try {
        return await dataSource.getData();
      } catch (e) {
        return Entity.empty(); // ❌ Don't swallow errors
      }
    }
  }
  ```
</example>

## Error Logging

### Comprehensive Error Logging
```dart
class ErrorLogger {
  static void logFailure(
    AppFailure failure, {
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    final logData = {
      'type': 'AppFailure',
      'failure_type': failure.runtimeType.toString(),
      'message': failure.message,
      'can_retry': failure.canRetry,
      'is_critical': failure.isCritical,
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };
    
    if (failure.isCritical) {
      log('CRITICAL FAILURE: ${jsonEncode(logData)}', level: 1000);
    } else {
      log('FAILURE: ${jsonEncode(logData)}', level: 800);
    }
  }
  
  static void logException(
    dynamic exception, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    final logData = {
      'type': 'Exception',
      'exception_type': exception.runtimeType.toString(),
      'message': exception.toString(),
      'context': context,
      'stack_trace': stackTrace?.toString(),
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };
    
    log('EXCEPTION: ${jsonEncode(logData)}', level: 1000);
  }
}
```

## ErrorHandlingMixin for Cubits

### Advanced Error Handling
```dart
mixin ErrorHandlingMixin<T> on Cubit<T> {
  /// Execute use case with automatic error handling
  Future<void> executeWithErrorHandling<R>(
    Future<R> Function() useCase, {
    required void Function(R data) onSuccess,
    void Function()? onLoading,
    String? errorContext,
    bool canRetry = true,
    VoidCallback? retryAction,
  }) async {
    try {
      onLoading?.call();
      
      final result = await useCase();
      onSuccess(result);
    } catch (e, stackTrace) {
      final failure = e is AppFailure 
          ? e 
          : ExceptionHandler.handle(e, stackTrace);
          
      ErrorLogger.logFailure(
        failure,
        context: errorContext,
        additionalData: {
          'cubit_type': runtimeType.toString(),
          'current_state': state.runtimeType.toString(),
        },
      );
      
      emitErrorState(failure, canRetry: canRetry, retryAction: retryAction);
    }
  }
  
  /// Abstract method to emit error state
  void emitErrorState(
    AppFailure failure, {
    bool canRetry = true,
    VoidCallback? retryAction,
  });
}
```

## Network Error Handling

### Dio Exception Conversion
```dart
class NetworkFailureFactory {
  static AppFailure fromDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return const NetworkFailure(
          'Connection timeout. Please check your internet connection.',
          canRetry: true,
        );
        
      case DioExceptionType.receiveTimeout:
        return const NetworkFailure(
          'Server response timeout. Please try again.',
          canRetry: true,
        );
        
      case DioExceptionType.badResponse:
        return _handleBadResponse(e.response!);
        
      case DioExceptionType.cancel:
        return const NetworkFailure('Request was cancelled');
        
      default:
        return const NetworkFailure(
          'Network error occurred. Please try again.',
          canRetry: true,
        );
    }
  }
  
  static AppFailure _handleBadResponse(Response response) {
    final statusCode = response.statusCode!;
    
    // Try to extract error message from response
    String message = 'Server error ($statusCode)';
    try {
      final data = response.data;
      if (data is Map<String, dynamic> && data.containsKey('message')) {
        message = data['message'] as String;
      }
    } catch (_) {
      // Use default message if parsing fails
    }
    
    switch (statusCode) {
      case 400:
        return ValidationFailure(message);
      case 401:
        return AuthFailure(message);
      case 403:
        return AuthFailure(message);
      case 404:
        return NetworkFailure('Resource not found');
      case 429:
        return const NetworkFailure(
          'Too many requests. Please try again later.',
          canRetry: true,
        );
      case 500:
      case 502:
      case 503:
        return NetworkFailure(
          'Server error. Please try again later.',
          canRetry: true,
        );
      default:
        return NetworkFailure(message, canRetry: true);
    }
  }
}
```