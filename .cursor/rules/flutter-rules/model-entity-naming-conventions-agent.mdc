---
description: This rule governs Model vs Entity naming conventions and implementation patterns in the Glidic Flutter App. It should be applied when: (1) Creating data layer models with JSON serialization, (2) Creating domain layer entities with business logic, (3) Implementing conversion methods between Model and Entity, (4) Setting up database table conversion patterns, (5) Reviewing naming consistency across layers, (6) Adding new business objects to features.
globs: 
alwaysApply: false
---

# Model vs Entity Naming Conventions

## Critical Rules

- **Model classes** MUST use "Model" suffix and be located in `data/models/`
- **Entity classes** SHOULD use "Entity" suffix or clean names in `domain/entities/`
- Models handle JSON serialization/deserialization with external data sources
- Entities contain business logic and are framework-independent
- Models typically extend their corresponding entities for data consistency
- Conversion methods between Model and Entity MUST be implemented
- Database table data conversion patterns MUST follow established conventions

## Naming Conventions

### Model Classes (Data Layer)
```dart
// ✅ Correct - Use "Model" suffix
UserModel
ProductModel
OrderModel
DashboardDataModel
AuthResponseModel
RegisterRequestModel
NotificationModel
CalendarEventModel
```

### Entity Classes (Domain Layer)
```dart
// ✅ Correct - Use "Entity" suffix or clean names
UserEntity
User (type alias)
ProductEntity
Product
CalendarEventEntity
DashboardData
NotificationEntity
RecordItem
```

## Structure Requirements

### Model Implementation (Data Layer)
- Location: `lib/features/*/data/models/`
- Purpose: Handle raw data from APIs/Database
- Extends corresponding entity for consistency
- Includes JSON serialization with `@JsonSerializable()`
- Maps API field names using `@JsonKey(name: 'api_field')`

### Entity Implementation (Domain Layer)
- Location: `lib/features/*/domain/entities/`
- Purpose: Business logic and UI optimization
- Extends `Equatable` for value equality
- Contains computed properties and business methods
- Framework-independent (no external dependencies)
- Immutable with `copyWith()` methods

## Examples

<example>
  ```dart
  // ✅ Correct Model (Data Layer)
  @JsonSerializable()
  class UserModel extends UserEntity {
    @JsonKey(name: 'profile_picture')
    @override
    final String? profilePicture;
    
    @JsonKey(name: 'is_email_verified')
    @override
    final bool isEmailVerified;
    
    const UserModel({
      required super.id,
      required super.email,
      required super.name,
      this.profilePicture,
      this.isEmailVerified = false,
    }) : super(
      profilePicture: profilePicture,
      isEmailVerified: isEmailVerified,
    );
    
    // JSON serialization
    factory UserModel.fromJson(Map<String, dynamic> json) =>
        _$UserModelFromJson(json);
    Map<String, dynamic> toJson() => _$UserModelToJson(this);
    
    // Entity conversion
    UserEntity toEntity() => UserEntity(/* ... */);
  }
  
  // ✅ Correct Entity (Domain Layer)
  class UserEntity extends Equatable {
    const UserEntity({
      required this.id,
      required this.email,
      required this.name,
      this.profilePicture,
      this.isEmailVerified = false,
    });
    
    final String id;
    final String email;
    final String name;
    final String? profilePicture;
    final bool isEmailVerified;
    
    // Business logic methods
    String get displayName => name.isEmpty ? email.split('@').first : name;
    bool get hasProfilePicture => profilePicture != null;
    bool get isVerified => isEmailVerified;
    
    UserEntity copyWith({/* ... */}) => UserEntity(/* ... */);
    
    @override
    List<Object?> get props => [id, email, name, profilePicture, isEmailVerified];
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Missing "Model" suffix in data layer
  class User extends UserEntity { } // Should be UserModel
  
  // ❌ Wrong - JSON logic in Entity
  class UserEntity extends Equatable {
    factory UserEntity.fromJson(Map<String, dynamic> json) { } // ❌
  }
  
  // ❌ Wrong - Business logic in Model
  class UserModel extends UserEntity {
    String get displayName => /* ... */; // ❌ Should be in Entity
  }
  
  // ❌ Wrong - External dependencies in Entity
  class UserEntity {
    @JsonKey(name: 'email') // ❌ No JSON annotations in Entity
    final String email;
  }
  ```
</example>

## Conversion Patterns

### Extension Methods (Recommended)
```dart
extension UserModelExtension on UserModel {
  UserEntity toEntity() {
    return UserEntity(
      id: id,
      email: email,
      name: name,
      profilePicture: profilePicture,
      isEmailVerified: isEmailVerified,
    );
  }
}

extension UserEntityExtension on UserEntity {
  UserModel toModel() {
    return UserModel(
      id: id,
      email: email,
      name: name,
      profilePicture: profilePicture,
      isEmailVerified: isEmailVerified,
    );
  }
}
```

### Repository Usage Pattern
```dart
class AuthRepositoryImpl implements AuthRepository {
  @override
  Future<UserEntity> login(String email, String password) async {
    try {
      final userModel = await remoteDataSource.login(email, password);
      await localDataSource.saveUser(userModel);
      return userModel.toEntity(); // Model → Entity conversion
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }
}
```

## Database Integration Patterns

### Database Table → Entity Conversion
```dart
@DriftAccessor(tables: [UsersTable])
class UsersDao extends DatabaseAccessor<AppDatabase> with _$UsersDaoMixin {
  // Database → Entity conversion
  UserEntity tableDataToEntity(UserTableData data) {
    return UserEntity(
      id: data.id,
      email: data.email,
      name: data.name,
      profilePicture: data.profilePicture,
      isEmailVerified: data.isEmailVerified,
    );
  }
  
  // Entity → Database conversion with error handling
  UsersTableCompanion entityToCompanion(UserEntity entity) {
    return UsersTableCompanion(
      id: Value(entity.id),
      email: Value(entity.email),
      name: Value(entity.name),
      profilePicture: Value(entity.profilePicture),
      isEmailVerified: Value(entity.isEmailVerified),
      updatedAt: Value(DateTime.now()),
    );
  }
}
```

## Complex Data Type Handling

### JSON Data Conversion with Error Handling
```dart
RecordItem tableDataToEntity(RecordsTableData data) {
  // Handle complex JSON data with graceful fallback
  List<double>? waveformData;
  if (data.waveformData != null) {
    try {
      final decoded = jsonDecode(data.waveformData!);
      waveformData = decoded.map((e) => (e as num).toDouble()).toList();
    } catch (e) {
      log('Error decoding waveform data: $e');
      waveformData = null; // Graceful fallback
    }
  }
  
  return RecordItem(
    id: data.id,
    duration: Duration(milliseconds: data.durationMs),
    waveformData: waveformData,
    status: UploadStatus.fromString(data.status),
  );
}
```

## Domain Examples by Feature

| Feature       | Entity Name           | Model Name              | Key Characteristics              |
| ------------- | --------------------- | ----------------------- | -------------------------------- |
| **Auth**      | `UserEntity`          | `UserModel`             | Profile, verification status     |
| **Calendar**  | `CalendarEventEntity` | `CalendarEventModel`    | Duration calculation, validation |
| **Recording** | `RecordItem`          | `RecordingSessionModel` | Waveform data, playback state    |
| **Home**      | `DashboardData`       | `DashboardDataModel`    | Statistics, recent activities    |

## Testing Guidelines

### Entity Testing (Unit Tests)
- Test business logic methods
- Test computed properties
- Test validation rules
- Test copyWith functionality

### Model Testing (Integration Tests)
- Test JSON serialization/deserialization
- Test conversion to/from entities
- Test API field mapping
- Test error handling in complex data types