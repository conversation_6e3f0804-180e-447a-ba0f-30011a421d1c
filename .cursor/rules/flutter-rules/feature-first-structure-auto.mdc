---
description: 
globs: lib/features/**/*.dart
alwaysApply: false
---

# Feature-First Structure Guidelines

## Critical Rules

- Each feature MUST be completely self-contained in its own folder
- Features can only depend on Core layer, never on other features
- Follow Clean Architecture layers: domain/data/presentation/di
- Use MVVM pattern for presentation layer organization
- Screen-specific widgets go in screen folders, feature widgets at feature level
- Each feature must have its own dependency injection configuration

## Folder Structure Requirements

```
lib/features/{feature_name}/
├── domain/
│   ├── entities/          # Business objects extending Equatable
│   ├── repositories/      # Abstract repository interfaces
│   └── usecases/         # Business logic use cases
├── data/
│   ├── models/           # DTOs with JSON serialization (*Model suffix)
│   ├── datasources/      # API and local data sources
│   └── repositories/     # Repository implementations
├── presentation/
│   ├── screens/          # MVVM screens with dedicated folders
│   │   └── {screen_name}/
│   │       ├── {screen_name}_screen.dart   # View
│   │       ├── {screen_name}_cubit.dart    # ViewModel
│   │       ├── {screen_name}_state.dart    # State
│   │       └── widgets/                    # Screen-specific widgets
│   └── widgets/          # Feature-level shared widgets
└── di/
    └── {feature_name}_di.dart # Dependency injection configuration
```

## Import Rules

```dart
// ✅ Allowed - Feature depending on Core
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/core/data/datasources/network/network_info.dart';

// ❌ Forbidden - Feature depending on another feature
import 'package:glidic_app/features/auth/domain/entities/user_entity.dart';
// ❌ Use shared entities in Core if needed across features

// ✅ Allowed - Internal feature imports
import 'package:glidic_app/features/auth/domain/repositories/auth_repository.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_cubit.dart';
```

## Screen Organization

Each screen must follow this structure:
- **View** (`*_screen.dart`): UI component with BlocProvider and BlocConsumer
- **ViewModel** (`*_cubit.dart`): Business logic and state management
- **State** (`*_state.dart`): Immutable state classes extending Equatable
- **Widgets** folder: Screen-specific reusable components

## Dependency Injection Pattern

Each feature must have a DI configuration:
```dart
class FeatureDI {
  static Future<void> init() async {
    // Data Sources
    sl.registerLazySingleton<FeatureRemoteDataSource>(() => ...);
    
    // Repositories
    sl.registerLazySingleton<FeatureRepository>(() => ...);
    
    // Use Cases - Always use factory for use cases
    sl.registerFactory(() => FeatureUseCase(sl()));
    
    // Cubits - Always use factory for cubits
    sl.registerFactory(() => FeatureCubit(useCase: sl()));
  }
}
```

## Examples

✅ **Correct feature structure:**
```
lib/features/recording/
├── domain/
│   ├── entities/record_item.dart
│   ├── repositories/recording_repository.dart
│   └── usecases/start_recording_usecase.dart
├── data/
│   ├── models/recording_session_model.dart
│   ├── datasources/recording_remote_datasource.dart
│   └── repositories/recording_repository_impl.dart
├── presentation/
│   ├── screens/recording/
│   │   ├── recording_screen.dart
│   │   ├── recording_cubit.dart
│   │   ├── recording_state.dart
│   │   └── widgets/record_button.dart
│   └── widgets/waveform_widget.dart
└── di/recording_di.dart
```

❌ **Incorrect patterns:**
- Cross-feature imports
- Missing layer organization
- Business logic in presentation layer
- External framework dependencies in domain layer
- Shared entities duplicated across features (should be in Core)