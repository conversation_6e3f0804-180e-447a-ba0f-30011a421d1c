---
description: This rule governs multi-language localization patterns in the Glidic Flutter App. It should be applied when: (1) Adding new user-facing text strings, (2) Implementing internationalization for new features, (3) Setting up localized error messages and validation text, (4) Creating language-specific content and formatting, (5) Implementing dynamic language switching, (6) Adding new supported languages to the app.
globs: 
alwaysApply: false
---

# Multi-Language Localization Patterns

## Critical Rules

- **NO hardcoded user-facing strings** in code - all text must be localized
- Use **app_localizations.dart** generated class for accessing localized strings
- Define strings in **ARB files** (`app_en.arb`, `app_ja.arb`) with descriptive keys
- Follow **hierarchical naming** convention for localization keys
- Implement **proper context** and **descriptions** for translators
- Handle **pluralization** and **parameter interpolation** correctly
- Support **RTL languages** and **text direction** appropriately
- Use **locale-aware formatting** for dates, numbers, and currencies

## Localization File Structure

```
lib/core/
├── l10n/
│   ├── app_en.arb          # English (default) translations
│   ├── app_ja.arb          # Japanese translations
│   └── header.txt          # ARB file header template
└── generated/l10n/
    ├── app_localizations.dart      # Generated localization class
    ├── app_localizations_en.dart   # English implementation
    └── app_localizations_ja.dart   # Japanese implementation
```

## ARB File Patterns

### English ARB File (`app_en.arb`)
```json
{
  "@@locale": "en",
  "@@last_modified": "2024-01-15T10:30:00.000Z",
  
  // Authentication Screen
  "auth_screen_title_login": "Sign In",
  "@auth_screen_title_login": {
    "description": "Title displayed on the login screen"
  },
  
  "auth_screen_title_signup": "Create Account",
  "@auth_screen_title_signup": {
    "description": "Title displayed on the sign-up screen"
  },
  
  "auth_label_email": "Email Address",
  "@auth_label_email": {
    "description": "Label for email input field"
  },
  
  "auth_label_password": "Password",
  "@auth_label_password": {
    "description": "Label for password input field"
  },
  
  "auth_button_login": "Sign In",
  "@auth_button_login": {
    "description": "Text for login button"
  },
  
  "auth_button_signup": "Create Account",
  "@auth_button_signup": {
    "description": "Text for sign-up button"
  },
  
  "auth_link_forgot_password": "Forgot Password?",
  "@auth_link_forgot_password": {
    "description": "Link text for forgot password feature"
  },
  
  // Validation Messages
  "validation_email_required": "Email is required",
  "@validation_email_required": {
    "description": "Error message when email field is empty"
  },
  
  "validation_email_invalid": "Please enter a valid email address",
  "@validation_email_invalid": {
    "description": "Error message when email format is invalid"
  },
  
  "validation_password_required": "Password is required",
  "@validation_password_required": {
    "description": "Error message when password field is empty"
  },
  
  "validation_password_too_short": "Password must be at least {minLength} characters",
  "@validation_password_too_short": {
    "description": "Error message when password is too short",
    "placeholders": {
      "minLength": {
        "type": "int",
        "example": "6"
      }
    }
  },
  
  // Recording Feature
  "recording_screen_title": "Recording",
  "@recording_screen_title": {
    "description": "Title for the recording screen"
  },
  
  "recording_status_ready": "Ready to Record",
  "@recording_status_ready": {
    "description": "Status text when recording is ready to start"
  },
  
  "recording_status_recording": "Recording...",
  "@recording_status_recording": {
    "description": "Status text during active recording"
  },
  
  "recording_status_paused": "Recording Paused",
  "@recording_status_paused": {
    "description": "Status text when recording is paused"
  },
  
  "recording_duration_format": "{minutes}:{seconds}",
  "@recording_duration_format": {
    "description": "Format for displaying recording duration",
    "placeholders": {
      "minutes": {
        "type": "String",
        "example": "05"
      },
      "seconds": {
        "type": "String", 
        "example": "30"
      }
    }
  },
  
  // Pluralization Examples
  "recording_count": "{count, plural, =0{No recordings} =1{1 recording} other{{count} recordings}}",
  "@recording_count": {
    "description": "Count of recordings with proper pluralization",
    "placeholders": {
      "count": {
        "type": "int"
      }
    }
  },
  
  // Error Messages
  "error_network_connection": "Please check your internet connection",
  "@error_network_connection": {
    "description": "Error message for network connectivity issues"
  },
  
  "error_server_unavailable": "Server is temporarily unavailable. Please try again later.",
  "@error_server_unavailable": {
    "description": "Error message when server is down or unavailable"
  },
  
  "error_unknown": "An unexpected error occurred",
  "@error_unknown": {
    "description": "Generic error message for unknown errors"
  },
  
  // Date and Time Formatting
  "date_format_short": "MMM dd, yyyy",
  "@date_format_short": {
    "description": "Short date format pattern"
  },
  
  "time_format_12h": "h:mm a",
  "@time_format_12h": {
    "description": "12-hour time format pattern"
  }
}
```

### Japanese ARB File (`app_ja.arb`)
```json
{
  "@@locale": "ja",
  "@@last_modified": "2024-01-15T10:30:00.000Z",
  
  "auth_screen_title_login": "ログイン",
  "auth_screen_title_signup": "アカウント作成",
  "auth_label_email": "メールアドレス",
  "auth_label_password": "パスワード",
  "auth_button_login": "ログイン",
  "auth_button_signup": "アカウント作成",
  "auth_link_forgot_password": "パスワードを忘れた場合",
  
  "validation_email_required": "メールアドレスは必須です",
  "validation_email_invalid": "有効なメールアドレスを入力してください",
  "validation_password_required": "パスワードは必須です",
  "validation_password_too_short": "パスワードは{minLength}文字以上である必要があります",
  
  "recording_screen_title": "録音",
  "recording_status_ready": "録音準備完了",
  "recording_status_recording": "録音中...",
  "recording_status_paused": "録音一時停止中",
  "recording_duration_format": "{minutes}:{seconds}",
  
  "recording_count": "{count, plural, =0{録音なし} other{{count}件の録音}}",
  
  "error_network_connection": "インターネット接続を確認してください",
  "error_server_unavailable": "サーバーが一時的に利用できません。後でもう一度お試しください。",
  "error_unknown": "予期しないエラーが発生しました",
  
  "date_format_short": "yyyy年MM月dd日",
  "time_format_12h": "ah:mm"
}
```

## Code Usage Patterns

### Accessing Localized Strings
```dart
// ✅ Correct - Using localized strings
class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.auth_screen_title_login),
      ),
      body: Column(
        children: [
          PrimaryTextField(
            labelText: l10n.auth_label_email,
            hintText: l10n.auth_hint_email,
          ),
          PrimaryTextField(
            labelText: l10n.auth_label_password,
            obscureText: true,
          ),
          PrimaryButton(
            text: l10n.auth_button_login,
            onPressed: () => _handleLogin(context),
          ),
          TextButton(
            onPressed: () => _handleForgotPassword(context),
            child: Text(l10n.auth_link_forgot_password),
          ),
        ],
      ),
    );
  }
}

// ❌ Wrong - Hardcoded strings
class BadLoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Sign In'), // ❌ Should use l10n.auth_screen_title_login
      ),
      body: Column(
        children: [
          PrimaryTextField(
            labelText: 'Email Address', // ❌ Should use l10n.auth_label_email
          ),
          PrimaryButton(
            text: 'Sign In', // ❌ Should use l10n.auth_button_login
            onPressed: () => _handleLogin(context),
          ),
        ],
      ),
    );
  }
}
```

### Validation with Localized Messages
```dart
// ✅ Correct - Localized validation messages
class LoginCubit extends Cubit<LoginState> {
  String? validateEmail(String? email, AppLocalizations l10n) {
    if (email == null || email.isEmpty) {
      return l10n.validation_email_required;
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(email)) {
      return l10n.validation_email_invalid;
    }
    
    return null;
  }
  
  String? validatePassword(String? password, AppLocalizations l10n) {
    if (password == null || password.isEmpty) {
      return l10n.validation_password_required;
    }
    
    if (password.length < 6) {
      return l10n.validation_password_too_short(6);
    }
    
    return null;
  }
}

// Usage in widget
String? _validateEmail(String? value) {
  final l10n = AppLocalizations.of(context)!;
  return context.read<LoginCubit>().validateEmail(value, l10n);
}
```

## Examples

<example>
  ```dart
  // ✅ Correct - Comprehensive localization usage
  
  class RecordingScreen extends StatelessWidget {
    const RecordingScreen({super.key});

    @override
    Widget build(BuildContext context) {
      final l10n = AppLocalizations.of(context)!;
      
      return BlocProvider(
        create: (context) => sl<RecordingCubit>(),
        child: Scaffold(
          appBar: AppBar(
            title: Text(l10n.recording_screen_title),
            actions: [
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () => _navigateToSettings(context),
                tooltip: l10n.button_settings,
              ),
            ],
          ),
          body: BlocConsumer<RecordingCubit, RecordingState>(
            listener: (context, state) {
              if (state is RecordingError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(_getErrorMessage(state.failure, l10n)),
                    action: state.failure.canRetry
                        ? SnackBarAction(
                            label: l10n.button_retry,
                            onPressed: () => state.retryAction?.call(),
                          )
                        : null,
                  ),
                );
              }
            },
            builder: (context, state) {
              return Column(
                children: [
                  RecordingStatusWidget(
                    status: _getStatusText(state, l10n),
                    duration: state is RecordingInProgress 
                        ? state.duration 
                        : Duration.zero,
                  ),
                  Expanded(
                    child: RecordingWaveformWidget(
                      waveformData: _getWaveformData(state),
                    ),
                  ),
                  RecordingControlsWidget(
                    state: state,
                    onStart: () => context.read<RecordingCubit>().startRecording(),
                    onPause: () => context.read<RecordingCubit>().pauseRecording(),
                    onStop: () => context.read<RecordingCubit>().stopRecording(),
                  ),
                ],
              );
            },
          ),
        ),
      );
    }
    
    String _getStatusText(RecordingState state, AppLocalizations l10n) {
      if (state is RecordingInProgress) {
        return l10n.recording_status_recording;
      } else if (state is RecordingPaused) {
        return l10n.recording_status_paused;
      } else {
        return l10n.recording_status_ready;
      }
    }
    
    String _getErrorMessage(AppFailure failure, AppLocalizations l10n) {
      if (failure is NetworkFailure) {
        return l10n.error_network_connection;
      } else if (failure is ServerFailure) {
        return l10n.error_server_unavailable;
      } else {
        return l10n.error_unknown;
      }
    }
  }
  
  // Localized form validation
  class RegistrationFormWidget extends StatefulWidget {
    const RegistrationFormWidget({super.key});

    @override
    State<RegistrationFormWidget> createState() => _RegistrationFormWidgetState();
  }

  class _RegistrationFormWidgetState extends State<RegistrationFormWidget> {
    final _formKey = GlobalKey<FormState>();
    final _emailController = TextEditingController();
    final _passwordController = TextEditingController();
    final _confirmPasswordController = TextEditingController();

    @override
    Widget build(BuildContext context) {
      final l10n = AppLocalizations.of(context)!;
      
      return Form(
        key: _formKey,
        child: Column(
          children: [
            PrimaryTextField(
              controller: _emailController,
              labelText: l10n.auth_label_email,
              hintText: l10n.auth_hint_email,
              keyboardType: TextInputType.emailAddress,
              validator: (value) => _validateEmail(value, l10n),
            ),
            SizedBox(height: Dimensions.gapLg),
            
            PrimaryTextField(
              controller: _passwordController,
              labelText: l10n.auth_label_password,
              hintText: l10n.auth_hint_password,
              obscureText: true,
              validator: (value) => _validatePassword(value, l10n),
            ),
            SizedBox(height: Dimensions.gapLg),
            
            PrimaryTextField(
              controller: _confirmPasswordController,
              labelText: l10n.auth_label_confirm_password,
              obscureText: true,
              validator: (value) => _validateConfirmPassword(value, l10n),
            ),
            SizedBox(height: Dimensions.extraLargePadding),
            
            PrimaryButton(
              text: l10n.auth_button_signup,
              onPressed: () => _handleSignUp(context, l10n),
            ),
          ],
        ),
      );
    }
    
    String? _validateEmail(String? email, AppLocalizations l10n) {
      if (email == null || email.isEmpty) {
        return l10n.validation_email_required;
      }
      
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegex.hasMatch(email)) {
        return l10n.validation_email_invalid;
      }
      
      return null;
    }
    
    String? _validatePassword(String? password, AppLocalizations l10n) {
      if (password == null || password.isEmpty) {
        return l10n.validation_password_required;
      }
      
      if (password.length < 6) {
        return l10n.validation_password_too_short(6);
      }
      
      return null;
    }
    
    String? _validateConfirmPassword(String? confirmPassword, AppLocalizations l10n) {
      if (confirmPassword == null || confirmPassword.isEmpty) {
        return l10n.validation_confirm_password_required;
      }
      
      if (confirmPassword != _passwordController.text) {
        return l10n.validation_passwords_not_match;
      }
      
      return null;
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Multiple localization violations
  
  class BadRecordingScreen extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Recording'), // ❌ Hardcoded string
        ),
        body: BlocBuilder<RecordingCubit, RecordingState>(
          builder: (context, state) {
            return Column(
              children: [
                Text(
                  state is RecordingInProgress 
                      ? 'Recording...' // ❌ Hardcoded string
                      : 'Ready to Record', // ❌ Hardcoded string
                ),
                if (state is RecordingError)
                  Text(
                    'Error occurred', // ❌ Hardcoded error message
                    style: TextStyle(color: Colors.red),
                  ),
                ElevatedButton(
                  onPressed: () => context.read<RecordingCubit>().startRecording(),
                  child: Text('Start Recording'), // ❌ Hardcoded button text
                ),
              ],
            );
          },
        ),
      );
    }
  }
  
  // ❌ Wrong - Hardcoded validation messages
  class BadValidation {
    String? validateEmail(String? email) {
      if (email == null || email.isEmpty) {
        return 'Email is required'; // ❌ Should be localized
      }
      
      if (!email.contains('@')) {
        return 'Invalid email format'; // ❌ Should be localized
      }
      
      return null;
    }
  }
  ```
</example>

## Parameter Interpolation

### Simple Parameters
```dart
// ARB file
"welcome_message": "Welcome back, {userName}!",
"@welcome_message": {
  "description": "Welcome message with user name",
  "placeholders": {
    "userName": {
      "type": "String",
      "example": "John"
    }
  }
}

// Usage
Text(l10n.welcome_message('John Doe'))
```

### Number Formatting
```dart
// ARB file
"file_size": "File size: {size} MB",
"@file_size": {
  "description": "Display file size",
  "placeholders": {
    "size": {
      "type": "double",
      "format": "decimalPattern"
    }
  }
}

// Usage
Text(l10n.file_size(25.67))
```

### Date Formatting
```dart
// ARB file
"last_modified": "Last modified: {date}",
"@last_modified": {
  "description": "Last modification date",
  "placeholders": {
    "date": {
      "type": "DateTime",
      "format": "yMMMd"
    }
  }
}

// Usage
Text(l10n.last_modified(DateTime.now()))
```

## Pluralization Patterns

```dart
// ARB file
"notification_count": "{count, plural, =0{No notifications} =1{1 notification} other{{count} notifications}}",
"@notification_count": {
  "description": "Number of notifications",
  "placeholders": {
    "count": {
      "type": "int"
    }
  }
}

"time_remaining": "{minutes, plural, =0{Less than a minute} =1{1 minute} other{{minutes} minutes}} remaining",
"@time_remaining": {
  "description": "Time remaining in minutes",
  "placeholders": {
    "minutes": {
      "type": "int"
    }
  }
}

// Usage
Text(l10n.notification_count(5))  // "5 notifications"
Text(l10n.notification_count(1))  // "1 notification"
Text(l10n.notification_count(0))  // "No notifications"
```

## Localization Key Naming Convention

### Hierarchical Structure
```
feature_component_type_description

Examples:
- auth_screen_title_login
- auth_button_login
- auth_label_email
- auth_error_invalid_credentials
- recording_status_recording
- recording_button_start
- home_header_welcome
- settings_label_language
- validation_email_required
- error_network_connection
```

### Key Categories
- **screen_title_**: Screen titles and headers
- **button_**: Button text and actions
- **label_**: Form labels and field descriptions
- **hint_**: Placeholder and hint text
- **error_**: Error messages
- **validation_**: Form validation messages
- **status_**: Status and state messages
- **message_**: General messages and notifications

## RTL Language Support

```dart
// Support text direction
Widget build(BuildContext context) {
  final textDirection = Directionality.of(context);
  
  return Directionality(
    textDirection: textDirection,
    child: Row(
      children: [
        if (textDirection == TextDirection.ltr) ...[
          Icon(Icons.arrow_forward),
          SizedBox(width: Dimensions.gapSm),
          Text(l10n.button_next),
        ] else ...[
          Text(l10n.button_next),
          SizedBox(width: Dimensions.gapSm),
          Icon(Icons.arrow_back),
        ],
      ],
    ),
  );
}
```