---
description: This rule governs dependency injection patterns using GetIt service locator in the Glidic Flutter App. It should be applied when: (1) Setting up new feature dependency injection configurations, (2) Registering services, repositories, use cases, and cubits, (3) Configuring service lifetimes (singleton vs factory), (4) Adding core infrastructure dependencies, (5) Setting up network and storage services, (6) Reviewing DI container organization and patterns.
globs: 
alwaysApply: false
---

# Dependency Injection Patterns with GetIt

## Critical Rules

- Use **GetIt service locator** pattern for dependency injection
- Register dependencies in **feature-specific DI modules**
- Use `registerLazySingleton` for expensive-to-create, stateful services
- Use `registerFactory` for use cases and cubits (stateless, multi-instance)
- Core dependencies are initialized before feature dependencies
- Feature DI modules must be completely self-contained
- Follow registration order: DataSources → Repositories → UseCases → Cubits
- Network interceptors configured after all dependencies are registered

## Service Locator Setup

### Global Service Locator
```dart
/// Global service locator instance
final GetIt sl = GetIt.instance;

/// Initialize all dependencies in main()
Future<void> initializeDependencies() async {
  await _initEnvironment();
  await _initCore();
  await _initFeatures();
  await _configureNetworkInterceptors();
}
```

## Registration Patterns

### Singleton vs Factory Guidelines

#### Use `registerLazySingleton` for:
- Database instances
- Network clients (Dio)
- Storage services
- Repository implementations
- Data sources

#### Use `registerFactory` for:
- Use cases (stateless business logic)
- Cubits (screen-specific state management)
- API services (if stateless)

## Core Dependencies Pattern

### Network Configuration
```dart
Future<void> _initCore() async {
  // Dio HTTP client with proper configuration
  sl.registerLazySingleton<Dio>(() {
    final dio = Dio();
    
    dio.options = BaseOptions(
      baseUrl: NetworkConstants.baseUrl,
      connectTimeout: NetworkConstants.connectTimeout,
      receiveTimeout: NetworkConstants.receiveTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
    
    // Add interceptors
    dio.interceptors.add(ErrorInterceptor());
    dio.interceptors.add(PrettyDioLogger(/* config */));
    
    return dio;
  });
  
  // Network Info
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());
}
```

### Storage Services
```dart
Future<void> _initStorage() async {
  // Database - Singleton for app lifetime
  sl.registerLazySingleton<AppDatabase>(() => AppDatabase());
  
  // Storage Services - Singletons for consistency
  sl.registerLazySingleton<SecureStorageService>(
    () => SecureStorageServiceImpl(),
  );
  
  sl.registerLazySingleton<PreferencesService>(
    () => PreferencesServiceImpl(),
  );
}
```

## Feature DI Pattern

### Standard Feature DI Structure
```dart
class FeatureDI {
  /// Initialize Feature dependencies
  static Future<void> init() async {
    // Step 1: API Services (if needed)
    sl.registerLazySingleton<FeatureApiService>(
      () => FeatureApiService(sl<Dio>()),
    );
    
    // Step 2: Data Sources
    sl.registerLazySingleton<FeatureRemoteDataSource>(
      () => FeatureRemoteDataSourceImpl(sl()),
    );
    
    sl.registerLazySingleton<FeatureLocalDataSource>(
      () => FeatureLocalDataSourceImpl(sl()),
    );
    
    // Step 3: Repositories
    sl.registerLazySingleton<FeatureRepository>(
      () => FeatureRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
        networkInfo: sl(),
      ),
    );
    
    // Step 4: Use Cases (Factory for stateless business logic)
    sl.registerFactory(() => GetFeatureDataUseCase(sl()));
    sl.registerFactory(() => UpdateFeatureUseCase(sl()));
    sl.registerFactory(() => DeleteFeatureUseCase(sl()));
    
    // Step 5: Cubits (Factory for screen-specific instances)
    sl.registerFactory(() => FeatureCubit(
      getDataUseCase: sl(),
      updateUseCase: sl(),
    ));
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct Auth Feature DI
  class AuthDI {
    static Future<void> init() async {
      // API Service
      sl.registerLazySingleton<AuthApiService>(
        () => AuthApiService(sl()),
      );
      
      // Data Sources - Singletons for caching/state
      sl.registerLazySingleton<AuthRemoteDataSource>(
        () => AuthRemoteDataSourceImpl(sl()),
      );
      
      sl.registerLazySingleton<AuthLocalDataSource>(
        () => AuthLocalDataSourceImpl(
          secureStorage: sl(),
          preferences: sl(),
        ),
      );
      
      // Repository - Singleton for consistency
      sl.registerLazySingleton<AuthRepository>(
        () => AuthRepositoryImpl(
          remoteDataSource: sl(),
          localDataSource: sl(),
          networkInfo: sl(),
        ),
      );
      
      // Use Cases - Factory for stateless operations
      sl.registerFactory(() => LoginUseCase(sl()));
      sl.registerFactory(() => LogoutUseCase(sl()));
      sl.registerFactory(() => GetCurrentUserUseCase(sl()));
      
      // Cubits - Factory for screen-specific instances
      sl.registerFactory(() => LoginCubit(loginUseCase: sl()));
      sl.registerFactory(() => ProfileCubit(
        getCurrentUserUseCase: sl(),
        logoutUseCase: sl(),
      ));
    }
  }
  
  // ✅ Correct usage in screen
  class LoginScreen extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return BlocProvider(
        create: (context) => sl<LoginCubit>(), // Factory creates new instance
        child: // UI implementation
      );
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Inconsistent registration patterns
  class BadDI {
    static Future<void> init() async {
      // ❌ Factory for repository (should be singleton)
      sl.registerFactory<AuthRepository>(() => AuthRepositoryImpl());
      
      // ❌ Singleton for use case (should be factory)
      sl.registerLazySingleton(() => LoginUseCase(sl()));
      
      // ❌ Direct instantiation instead of dependency injection
      sl.registerLazySingleton<AuthRepository>(
        () => AuthRepositoryImpl(
          remoteDataSource: AuthRemoteDataSourceImpl(), // ❌
          localDataSource: AuthLocalDataSourceImpl(),   // ❌
        ),
      );
      
      // ❌ Missing type annotation
      sl.registerFactory(() => LoginCubit()); // ❌ No type
    }
  }
  
  // ❌ Wrong - Manual instantiation
  class LoginScreen extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return BlocProvider(
        create: (context) => LoginCubit(loginUseCase: sl()), // ❌ Manual
        child: // UI implementation
      );
    }
  }
  ```
</example>

## Complex Dependencies Pattern

### Multi-DataSource Repository
```dart
// Recording feature with multiple data sources
class RecordingDI {
  static Future<void> init() async {
    // Multiple API services
    sl.registerLazySingleton<RecordingApiService>(
      () => RecordingApiService(sl()),
    );
    
    sl.registerLazySingleton<TranscriptionApiService>(
      () => TranscriptionApiService(sl()),
    );
    
    // Multiple data sources
    sl.registerLazySingleton<RecordingRemoteDataSource>(
      () => RecordingRemoteDataSourceImpl(sl()),
    );
    
    sl.registerLazySingleton<RecordingLocalDataSource>(
      () => RecordingLocalDataSourceImpl(sl()),
    );
    
    sl.registerLazySingleton<TranscriptionDataSource>(
      () => TranscriptionDataSourceImpl(sl()),
    );
    
    // Repository with multiple dependencies
    sl.registerLazySingleton<RecordingRepository>(
      () => RecordingRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
        transcriptionDataSource: sl(),
        networkInfo: sl(),
      ),
    );
    
    // Use cases with specific dependencies
    sl.registerFactory(() => StartRecordingUseCase(sl()));
    sl.registerFactory(() => StopRecordingUseCase(sl()));
    sl.registerFactory(() => SaveRecordingUseCase(sl()));
    sl.registerFactory(() => TranscribeRecordingUseCase(sl()));
  }
}
```

## Network Interceptor Configuration

### Post-Initialization Setup
```dart
/// Configure interceptors after all dependencies are ready
Future<void> _configureNetworkInterceptors() async {
  final dio = sl<Dio>();
  
  // Add auth interceptor with dependency injection
  dio.interceptors.add(AuthInterceptor(sl<AuthLocalDataSource>()));
  
  // Add other interceptors as needed
  if (EnvConfig.isDebugMode) {
    dio.interceptors.add(DebugInterceptor());
  }
}
```

## Testing Configuration

### Mock Registration for Tests
```dart
void setupTestDependencies() {
  // Register mocks for testing
  sl.registerLazySingleton<AuthRepository>(() => MockAuthRepository());
  sl.registerFactory(() => LoginUseCase(sl()));
  sl.registerFactory(() => LoginCubit(loginUseCase: sl()));
}

void tearDownTestDependencies() {
  sl.reset();
}
```

## Feature Integration Pattern

### Main Service Locator Integration
```dart
Future<void> _initFeatures() async {
  // Initialize in dependency order
  await AuthDI.init();           // Core authentication
  await HomeDI.init();           // May depend on auth
  await CalendarEventDI.init();  // Independent feature
  await RecordingDI.init();      // Independent feature
  await DigitalTwinDI.init();    // May depend on other features
}
```

## Dependency Checking

### Runtime Validation
```dart
/// Check if a dependency is registered
bool isDependencyRegistered<T extends Object>() {
  return sl.isRegistered<T>();
}

/// Validate critical dependencies are registered
void validateCoreDependencies() {
  assert(sl.isRegistered<Dio>(), 'Dio not registered');
  assert(sl.isRegistered<AppDatabase>(), 'Database not registered');
  assert(sl.isRegistered<SecureStorageService>(), 'Storage not registered');
}
```

## Performance Considerations

- Use `registerLazySingleton` for expensive-to-create services
- Use `registerFactory` for lightweight, stateless objects
- Avoid circular dependencies by designing clear dependency graphs
- Initialize heavy services lazily to improve app startup time
- Reset dependencies properly in tests to avoid state leakage