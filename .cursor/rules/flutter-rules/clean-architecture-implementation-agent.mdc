---
description: This rule governs Clean Architecture implementation in Glidic Flutter App. It should be applied when: (1) Creating new features following the feature-first structure, (2) Implementing domain, data, and presentation layers, (3) Setting up dependency injection with GetIt, (4) Creating BLoC/Cubit state management, (5) Reviewing architecture compliance in feature modules, (6) Implementing repository pattern with multiple data sources, (7) Adding new use cases or entities.
globs: 
alwaysApply: false
---

# Clean Architecture Implementation for Flutter

## Critical Rules

- Features must follow Clean Architecture with domain/data/presentation layers
- Use feature-first folder structure with strict dependency rules (Features → Core)
- Repository pattern with abstract interfaces in domain layer
- BLoC/Cubit for state management with immutable state classes
- Dependency injection using GetIt service locator pattern
- Exception-based error handling with AppFailure hierarchy
- MVVM pattern for screen organization (View-ViewModel-State)
- Each feature must be completely independent (no cross-feature dependencies)

## Layer Structure

### Domain Layer (`domain/`)
- **Entities**: Business objects extending Equatable with business logic
- **Repositories**: Abstract interfaces for data access contracts
- **Use Cases**: Single-responsibility business logic classes

### Data Layer (`data/`)
- **Models**: DTOs with JSON serialization extending entities
- **DataSources**: Remote (API) and local (database) data sources
- **Repositories**: Concrete implementations of domain repository interfaces

### Presentation Layer (`presentation/`)
- **Screens**: MVVM pattern with screen folders containing View/ViewModel/State
- **Widgets**: Reusable UI components (feature-level and screen-specific)
- **Controllers**: Feature-specific state management

### Dependency Injection (`di/`)
- **Feature DI**: Self-contained dependency registration for the feature

## Examples

<example>
  ```dart
  // ✅ Correct feature structure
  lib/features/auth/
  ├── domain/
  │   ├── entities/user_entity.dart
  │   ├── repositories/auth_repository.dart
  │   └── usecases/login_usecase.dart
  ├── data/
  │   ├── models/user_model.dart
  │   ├── datasources/auth_remote_datasource.dart
  │   └── repositories/auth_repository_impl.dart
  ├── presentation/
  │   ├── screens/login_screen/
  │   │   ├── login_screen.dart      # View
  │   │   ├── login_cubit.dart       # ViewModel
  │   │   ├── login_state.dart       # State
  │   │   └── widgets/               # Screen-specific widgets
  │   └── widgets/                   # Feature-level widgets
  └── di/auth_di.dart
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Cross-feature dependencies
  import 'package:glidic_app/features/home/<USER>/entities/user.dart';
  // in features/auth/ - features cannot depend on other features
  
  // ❌ Wrong - Domain depending on external frameworks
  class UserEntity {
    factory UserEntity.fromJson(Map<String, dynamic> json) // ❌
  }
  
  // ❌ Wrong - Missing layer separation
  lib/features/auth/
  ├── user.dart              # ❌ No layer organization
  ├── auth_service.dart      # ❌ Mixed responsibilities
  └── login_page.dart        # ❌ No MVVM structure
  ```
</example>

## Dependency Rules

1. **Features → Core**: Features can depend on Core layer only
2. **Core Independence**: Core has zero dependencies on Features
3. **Feature Isolation**: Features cannot depend on other Features
4. **Layer Dependencies**: Presentation → Domain ← Data (Clean Architecture)
5. **Interface Segregation**: Dependencies through abstractions, not concrete implementations

## Use Case Pattern

```dart
class LoginUseCase {
  const LoginUseCase(this._repository);
  
  final AuthRepository _repository;
  
  /// Throws AppFailure on error, returns success value directly
  Future<UserEntity> execute(LoginParams params) async {
    return await _repository.login(params);
  }
}
```

## Repository Pattern

```dart
// Domain layer - Abstract interface
abstract class AuthRepository {
  Future<UserEntity> login(LoginParams params);
  Future<void> logout();
}

// Data layer - Concrete implementation
class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });
  
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  
  @override
  Future<UserEntity> login(LoginParams params) async {
    try {
      final userModel = await remoteDataSource.login(params);
      await localDataSource.saveUser(userModel);
      return userModel.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }
}
```

## MVVM Implementation

Each screen follows MVVM with dedicated folders:

```dart
// View - UI Component
class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<LoginCubit>(),
      child: // UI implementation
    );
  }
}

// ViewModel - Business Logic
class LoginCubit extends Cubit<LoginState> {
  LoginCubit({required this.loginUseCase}) : super(const LoginInitial());
  
  final LoginUseCase loginUseCase;
  
  Future<void> login(String email, String password) async {
    emit(const LoginLoading());
    try {
      final user = await loginUseCase.execute(LoginParams(email, password));
      emit(LoginSuccess(user: user));
    } catch (e) {
      emit(LoginError(failure: e as AppFailure));
    }
  }
}

// State - UI State Representation
abstract class LoginState extends Equatable {
  const LoginState();
}
```