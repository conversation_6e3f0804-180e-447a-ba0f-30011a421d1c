# Core Rules

This directory contains rules related to Cursor agent behavior and rule generation specifically. These are fundamental rules governing how the AI agent should interact with the codebase and generate new rules.

## Contents

- `general-rules-mode-always.mdc`: Core Swift & SwiftUI development guidelines that are always applied to every chat and cmd/ctrl-k context. Contains fundamental coding standards, project structure, and UI design patterns.
- `rule-generating-agent.mdc`: Guidelines for how the AI agent should create and format new rules for the YConnectGen2iOS project.
- `rule-update-cursor-modes-manual.mdc`: Manual rule for updating Cursor modes and configurations.

## Purpose

The rules in this directory help ensure consistent behavior when working with the codebase and creating new rule files. They establish the foundational patterns that all other rules build upon. The general rules provide core Swift/SwiftUI development standards that should be followed in all contexts.