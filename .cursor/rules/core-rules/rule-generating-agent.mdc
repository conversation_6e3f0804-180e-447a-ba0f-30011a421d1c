---
alwaysApply: true
---
# Rule Title

## Critical Rules

- Concise, bulleted list of actionable rules the agent MUST follow

## Examples

<example>
  {valid rule application}
</example>

<example type="invalid">
  {invalid rule application}
</example>
```

### Organizational Folders
All rules files are organized under these existing folders:
- **.cursor/rules/core-rules** - rules related to cursor agent behavior or rule generation specifically
- **.cursor/rules/flutter-rules** - Flutter development best practices, Dart coding standards, warnings cleanup
- **.cursor/rules/workflows** - development workflows, code review processes, architecture guidelines
- **.cursor/rules/tool-rules** - git operations, build tools, development tool usage
- **.cursor/rules/ui-rules** - Flutter UI guidelines, Material Design standards, widget best practices
- **.cursor/rules/global-rules** - rules that are ALWAYS applied to every chat and cmd/ctrl-k context
- **.cursor/rules/security-rules** - rules related to security practices, data protection, and secure coding
- **.cursor/rules/business-rules** - business logic, domain-specific rules, and application-specific guidelines
- **.cursor/rules/testing-rules** - testing strategies, unit tests, widget tests, integration tests for Flutter
- **.cursor/rules/my-rules** - gitignored personal rules for individual developers

**Note**: Root level rules (*.mdc files directly in .cursor/rules/) are legacy and should be migrated to appropriate folders.

## Glob Pattern Examples for Flutter Project
Common glob patterns for Glidic Flutter App:
- Dart files: **/*.dart
- Feature modules: lib/features/**/
- Core infrastructure: lib/core/**/*.dart
- Presentation layer: **/presentation/**/*.dart, **/*_screen.dart, **/*_widget.dart
- State management: **/*_cubit.dart, **/*_state.dart, **/*_bloc.dart
- Repositories: **/repositories/**/*.dart, **/*_repository.dart
- Use Cases: **/usecases/**/*.dart, **/*_usecase.dart
- Data models: **/models/**/*.dart, **/*_model.dart
- Entities: **/entities/**/*.dart, **/*_entity.dart
- Testing files: **/*_test.dart, **/test/**/*.dart
- Build files: pubspec.yaml, build.yaml, analysis_options.yaml
- Configuration: .env*, lib/core/common/config/**/*.dart

## Critical Rules for Glidic Flutter App
- Rule files will be located and named ALWAYS as: `.cursor/rules/{organizational-folder}/rule-name-{auto|agent|manual|always}.mdc`
- Rules will NEVER be created anywhere other than .cursor/rules/**
- You will always check to see if there is an existing rule to update under all .cursor/rules sub-folders
- **Flutter-specific naming**: Use descriptive names like `flutter-clean-architecture-agent.mdc`, `bloc-state-management-agent.mdc`, `dart-best-practices-agent.mdc`
- FrontMatter Rules Types:
  - **Manual Rule**: IF a Manual rule is requested - description and globs MUST be blank and alwaysApply: false and filename ends with -manual.mdc
  - **Auto Rule**: IF a rule is requested that should apply always to certain glob patterns (example all Dart files or all Flutter widgets) - description must be blank, and alwaysApply: false and filename ends with -auto.mdc
  - **Always Rule**: Global Rule applies to every chat and cmd/ctrl-k - description and globs blank, and alwaysApply: true and filename ends with -always.mdc
  - **Agent Select Rule**: The rule does not need to be loaded into every chat thread, it serves a specific purpose. The description MUST provide comprehensive context about when to apply the rule, including scenarios like Flutter widget creation, Clean Architecture implementation, BLoC state management, API integration, or new feature development. Globs blank, and alwaysApply:false and filename ends with -agent.mdc
- For Rule Content - focus on actionable, clear directives without unnecessary explanation
- When a rule will only be used sometimes (alwaysApply: false) the description MUST provide enough context for the AI to confidently determine when to load and apply the rule
- Use Concise Markdown Tailored to Agent Context Window usage
- Always indent content within XML Example section with 2 spaces
- Emojis and Mermaid diagrams are allowed and encouraged if it is not redundant and better explains the rule for Flutter development comprehension
- While there is no strict line limit, be judicious with content length as it impacts performance. Focus on essential information that helps the agent make Flutter development decisions
- Always include a valid and invalid example relevant to Flutter/Dart development
- NEVER use quotes around glob patterns, NEVER group glob extensions with `{}`
- If the request for a rule or a future behavior change includes context of a Flutter development mistake, this would be great to use in the example for the rule
- After rule is created or updated, Respond with the following:
  - AutoRuleGen Success: path/rule-name.mdc
  - Rule Type: {Rule Type}
  - Rule Description: {The exact content of the description field}

## Examples

<example>
  Creating a new Flutter Clean Architecture rule:
  
  File: `.cursor/rules/flutter-rules/clean-architecture-implementation-agent.mdc`
  
  ```mdc
  ---
  description: This rule governs Clean Architecture implementation in Glidic Flutter App. It should be applied when: (1) Creating new features following the feature-first structure, (2) Implementing domain, data, and presentation layers, (3) Setting up dependency injection with GetIt, (4) Creating BLoC/Cubit state management, (5) Reviewing architecture compliance in feature modules, (6) Implementing repository pattern with multiple data sources.
  globs: 
  alwaysApply: false
  ---
  
  # Clean Architecture Implementation for Flutter
  
  ## Critical Rules
  - Features must follow Clean Architecture with domain/data/presentation layers
  - Use feature-first folder structure with strict dependency rules (Features → Core)
  - Repository pattern with abstract interfaces in domain layer
  - BLoC/Cubit for state management with immutable state classes
  - Dependency injection using GetIt service locator pattern
  - Exception-based error handling with AppFailure hierarchy
  ```
</example>

<example type="invalid">
  Creating rule without proper front matter:
  
  ```mdc
  # Some Flutter Rule
  
  ## Rules
  - Do something with Flutter
  ```
  
  Missing: front matter, proper description, examples, organizational folder structure
</example>

## Rule Improvement Triggers

- New Flutter development patterns not covered by existing rules
- Repeated similar implementations across features
- Common error patterns that could be prevented with BLoC/Cubit
- New packages or dependencies being used consistently
- Emerging best practices in the Flutter Clean Architecture codebase

### Analysis Process:
- Compare new Dart code with existing rules
- Identify Flutter patterns that should be standardized
- Look for references to external Flutter documentation
- Check for consistent error handling patterns with AppFailure
- Monitor BLoC/Cubit patterns and state management consistency

### Rule Updates:

- **Add New Rules When:**
  - A new Flutter pattern/package is used in 3+ features
  - Common Flutter bugs could be prevented by a rule
  - Code reviews repeatedly mention the same Flutter feedback
  - New security or performance patterns emerge in Flutter context

- **Modify Existing Rules When:**
  - Better Flutter examples exist in the codebase
  - Additional edge cases are discovered in Flutter development
  - Related Flutter rules have been updated
  - Implementation details have changed in Flutter/Dart

- **Example Pattern Recognition:**

  ```dart
  // If you see repeated patterns like:
  class SomeFeatureCubit extends Cubit<SomeFeatureState> {
    SomeFeatureCubit({required this.repository}) : super(const SomeFeatureInitial());
    
    Future<void> loadData() async {
      emit(const SomeFeatureLoading());
      try {
        final result = await repository.getData();
        emit(SomeFeatureSuccess(data: result));
      } catch (e) {
        emit(SomeFeatureError(message: e.toString()));
      }
    }
  }

  // Consider adding to [bloc-patterns.mdc](mdc:.cursor/rules/flutter-rules/bloc-patterns.mdc):
  // - Standard BLoC/Cubit structure
  // - Common state management patterns
  // - Error handling in state management
  ```

- **Rule Quality Checks:**
- Rules should be actionable and specific to Flutter development
- Examples should come from actual Flutter code
- References should be up to date with current Flutter/Dart versions
- Patterns should be consistently enforced across features

## Continuous Improvement:

- Monitor Flutter code review comments
- Track common Flutter development questions
- Update rules after major Flutter/Dart version upgrades
- Add links to relevant Flutter documentation
- Cross-reference related Flutter rules

## Rule Deprecation

- Mark outdated Flutter patterns as deprecated
- Remove rules that no longer apply to current Flutter/Dart versions
- Update references to deprecated Flutter packages
- Document migration paths for old Flutter patterns

## Documentation Updates:

- Keep examples synchronized with current Flutter codebase
- Update references to external Flutter docs
- Maintain links between related Flutter rules
- Document breaking changes in Flutter/Dart versions

Follow [flutter-development-standards.mdc](mdc:.cursor/rules/flutter-rules/flutter-development-standards.mdc) for proper Flutter development formatting and structure.

  ```
  
  Missing: front matter, proper description, examples, organizational folder structure
</example>
