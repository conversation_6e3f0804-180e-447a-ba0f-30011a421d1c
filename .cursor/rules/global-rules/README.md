# Global Rules

This directory contains rules that are ALWAYS applied to every chat and cmd/ctrl-k context in the YConnectGen2iOS project.

## Purpose

Global rules define fundamental standards, principles, and constraints that must be applied universally across all development activities. These rules are automatically loaded into every AI assistant interaction to ensure consistency and adherence to core project standards.

## Rule Characteristics

### Always Applied Rules
- **alwaysApply: true**: These rules are automatically loaded in every context
- **No Target Globs**: Applied regardless of file type or location
- **Universal Scope**: Apply to all development activities and code interactions
- **Foundational Standards**: Core principles that cannot be overridden

## Current Status

🚫 **No Global Rules Currently Defined**

This folder is currently empty but is reserved for rules that need universal application. Examples of future global rules might include:

### Potential Global Rules
- **Universal Code Standards**: Absolute coding standards that apply to all code
- **Security Requirements**: Non-negotiable security practices for all development
- **Architecture Constraints**: Fundamental architectural principles that cannot be violated
- **Quality Gates**: Minimum quality thresholds that apply universally

## Usage Guidelines

### When to Create Global Rules
Global rules should ONLY be created for standards that:
1. **Must Apply Universally**: No exceptions across the entire project
2. **Are Foundational**: Core principles that underpin all development
3. **Cannot Be Overridden**: Non-negotiable requirements
4. **Are Stable**: Unlikely to change frequently

### Rule Naming Convention
Global rules must follow the pattern: `[rule-name]-always.mdc`

### Implementation Considerations
- **Performance Impact**: Global rules increase AI context size for every interaction
- **Maintenance Overhead**: Changes affect all development interactions
- **Team Agreement**: Must have unanimous team agreement for global status
- **Documentation**: Requires comprehensive documentation and justification

## Examples of Appropriate Global Rules

### Universal Security Standards
```mdc
---
description: 
globs: 
alwaysApply: true
---

# Universal Security Standards

## Critical Rules
- NEVER commit API keys, passwords, or secrets to version control
- ALL user input must be validated and sanitized
- ALL sensitive data must use Keychain storage
- ALL network communication must use HTTPS
```

### Fundamental Architecture Principles
```mdc
---
description: 
globs: 
alwaysApply: true
---

# Clean Architecture Fundamentals

## Critical Rules
- Domain layer NEVER depends on UI or external frameworks
- All business logic must be in Use Cases
- Repository pattern MUST be used for all data access
- Dependency injection MUST be used for all dependencies
```

## Anti-Patterns to Avoid

### Inappropriate Global Rules
- **Feature-Specific Logic**: Rules that only apply to certain features
- **Tool-Specific Rules**: Rules that only apply when using specific tools
- **Context-Dependent Rules**: Rules that vary based on situation
- **Frequently Changing Rules**: Rules that need regular updates

### Common Mistakes
- **Over-Globalization**: Making too many rules global when they should be specific
- **Premature Globalization**: Making rules global before they've proven universal need
- **Conflicting Rules**: Global rules that conflict with necessary specific rules
- **Overly Verbose**: Global rules that are too detailed and impact performance

## Review and Approval Process

### Global Rule Creation Requirements
1. **Team Consensus**: All team members must agree to the global nature
2. **Architecture Review**: Architectural impact assessment and approval
3. **Performance Assessment**: Context size and performance impact evaluation
4. **Documentation**: Comprehensive rationale and usage documentation

### Regular Review
- **Quarterly Review**: Regular assessment of global rule necessity and effectiveness
- **Impact Analysis**: Ongoing analysis of global rule impact on development
- **Optimization**: Regular optimization to minimize context size impact
- **Archival**: Process for moving global rules to specific categories when appropriate

## Integration with Other Rule Categories

### Rule Hierarchy
1. **Global Rules**: Always applied (highest priority)
2. **Always Rules**: Applied based on context and patterns
3. **Agent Rules**: Applied when specifically requested
4. **Manual Rules**: Applied on explicit user request

### Conflict Resolution
- **Global Override**: Global rules take precedence over conflicting specific rules
- **Exception Handling**: Clear process for handling necessary exceptions
- **Documentation**: All conflicts and resolutions must be documented
- **Review Process**: Regular review of conflicts to optimize rule organization

## Monitoring and Metrics

### Usage Tracking
- **Application Frequency**: How often global rules are applied and referenced
- **Performance Impact**: Measurement of context size and processing impact
- **Compliance**: Tracking of adherence to global rule requirements
- **Exception Requests**: Monitoring of requests for global rule exceptions

### Optimization Opportunities
- **Rule Consolidation**: Opportunities to combine or streamline global rules
- **Context Reduction**: Minimizing global rule content to reduce context size
- **Effectiveness Analysis**: Regular analysis of global rule effectiveness
- **Refinement**: Continuous refinement based on usage patterns and feedback

## Best Practices

### Global Rule Design
- **Concise Content**: Keep global rules as brief as possible while maintaining clarity
- **Clear Language**: Use unambiguous language that cannot be misinterpreted
- **Actionable Items**: Focus on specific, actionable requirements
- **Universal Applicability**: Ensure rules truly apply to all development contexts

### Maintenance Practices
- **Version Control**: Careful version control of global rule changes
- **Impact Testing**: Testing global rule changes across various development scenarios
- **Team Communication**: Clear communication of global rule changes to all team members
- **Rollback Plans**: Prepared rollback plans for problematic global rule changes

Global rules represent the fundamental standards that define the character and quality of the YConnectGen2 project. They should be used sparingly and only for truly universal requirements that benefit from consistent application across all development activities. 