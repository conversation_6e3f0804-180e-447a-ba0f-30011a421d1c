# My Rules

This directory contains gitignored personal rules for individual developers working on the YConnectGen2iOS project.

## Purpose

The my-rules directory provides a space for individual developers to create personal coding rules, preferences, and guidelines that supplement the team-wide rules without affecting other team members. These rules are gitignored to maintain personal customization while preserving team consistency.

## Rule Characteristics

### Personal & Private Rules
- **Gitignored**: All files in this directory are excluded from version control
- **Individual Preferences**: Customized rules that reflect personal coding style and preferences
- **Supplementary**: Adds to team rules without overriding mandatory standards
- **Development Environment**: Can include environment-specific configurations and preferences

## Current Status

📁 **Empty Directory - Ready for Personal Rules**

This folder is currently empty and ready for individual developers to add their personal rules and preferences.

## Usage Guidelines

### When to Create Personal Rules
Personal rules are appropriate for:
1. **Individual Preferences**: Coding style preferences that don't conflict with team standards
2. **Environment-Specific**: Rules specific to your development setup or workflow
3. **Learning Aids**: Personal reminders and learning guidelines
4. **Productivity Tools**: Custom shortcuts and workflow optimizations

### Rule Naming Conventions
- **Format**: `[descriptive-name]-manual.mdc` for manual rules
- **Examples**: 
  - `personal-coding-style-manual.mdc`
  - `development-shortcuts-manual.mdc`
  - `learning-reminders-manual.mdc`
  - `productivity-tips-manual.mdc`

### File Structure
```
my-rules/
├── README.md (this file - tracked in git)
├── personal-coding-style-manual.mdc (gitignored)
├── development-workflow-manual.mdc (gitignored)
├── learning-notes-manual.mdc (gitignored)
└── productivity-shortcuts-manual.mdc (gitignored)
```

## Example Personal Rules

### Personal Coding Style Preferences
```mdc
---
description: 
globs: 
alwaysApply: false
---

# Personal Coding Style

## My Preferences
- Always use explicit types for complex generics
- Prefer guard statements over nested if conditions
- Use descriptive variable names even if they're longer
- Add TODO comments for future optimizations

## Reminders
- Check for retain cycles in closures
- Consider using @MainActor for UI-related classes
- Remember to add accessibility labels to custom views
```

### Development Workflow Personal Rules
```mdc
---
description: 
globs: 
alwaysApply: false
---

# My Development Workflow

## Before Committing
- Run SwiftLint locally
- Test on both simulator and device
- Check for any force unwrapping
- Verify all TODOs are intentional

## Code Review Checklist
- Focus on business logic correctness
- Check for potential performance issues
- Verify proper error handling
- Ensure consistent naming conventions
```

### Learning and Development Notes
```mdc
---
description: 
globs: 
alwaysApply: false
---

# Personal Learning Reminders

## Swift Language Features to Practice
- Modern async/await patterns
- @Observable vs ObservableObject
- Advanced Combine operators
- SwiftUI performance optimization

## Architecture Patterns to Remember
- Always implement Use Cases for business logic
- Keep ViewModels thin and focused
- Use dependency injection consistently
- Separate concerns clearly between layers
```

## Integration with Team Rules

### Rule Hierarchy
1. **Team Standards**: Mandatory rules that cannot be overridden
2. **Project Guidelines**: Recommended practices for consistency
3. **Personal Preferences**: Individual customizations that supplement team rules

### Conflict Resolution
- **Team Rules Win**: Team standards always take precedence over personal preferences
- **Non-Conflicting Additions**: Personal rules can add to but not override team standards
- **Documentation**: Document any personal rules that might affect code review or collaboration

## Best Practices

### Personal Rule Creation
- **Supplement, Don't Replace**: Add to team standards rather than replacing them
- **Document Rationale**: Include reasons for personal preferences and rules
- **Keep Updated**: Regularly review and update personal rules as skills develop
- **Share Insights**: Consider sharing useful personal insights with the team

### Collaboration Considerations
- **Team Awareness**: Let team members know about significant personal preferences
- **Code Review**: Be prepared to explain personal coding choices during reviews
- **Flexibility**: Be willing to adapt personal preferences for team consistency
- **Learning**: Use personal rules as a learning tool to improve skills

## Example Personal Rule Categories

### Code Quality & Style
- Personal formatting preferences (within team guidelines)
- Specific naming conventions you prefer
- Code organization patterns you find helpful
- Error handling approaches you favor

### Development Environment
- Xcode configuration preferences
- Simulator setup and usage
- Debugging techniques and shortcuts
- Productivity tools and extensions

### Learning & Growth
- Language features you're learning
- Architecture patterns you want to practice
- Technical debt items you want to address
- Performance optimization techniques

### Project-Specific
- YConnectGen2-specific patterns you've discovered
- BLE debugging techniques
- Vehicle testing approaches
- API integration shortcuts

## Maintenance and Organization

### Regular Review
- **Monthly Review**: Review and update personal rules based on learning and experience
- **Team Sync**: Periodically discuss useful personal insights with the team
- **Cleanup**: Remove outdated or no longer relevant personal rules
- **Documentation**: Keep personal rules well-documented and organized

### Backup and Sharing
- **Local Backup**: Consider backing up useful personal rules outside of git
- **Selective Sharing**: Share particularly useful insights with team members
- **Knowledge Transfer**: Document important personal discoveries for team benefit
- **Evolution**: Allow personal rules to evolve as skills and understanding grow

## Privacy and Security

### Personal Information
- **No Sensitive Data**: Never include personal API keys, passwords, or sensitive information
- **Local References**: Keep references to local paths and personal setup details private
- **Team Boundaries**: Respect team coding standards while maintaining personal preferences
- **Professional Content**: Ensure all personal rules maintain professional standards

### Gitignore Configuration
The `.gitignore` file is configured to exclude all `.mdc` files in the `my-rules` directory:
```gitignore
.cursor/rules/my-rules/*.mdc
```

This ensures personal rules remain private while allowing the README.md to be shared with the team.

## Support and Resources

### Getting Started
1. Create your first personal rule file using the naming convention
2. Start with simple preferences and gradually add more detailed rules
3. Test your personal rules in practice and refine as needed
4. Consider sharing useful insights with the team

### Team Collaboration
- Discuss personal preferences that might affect collaboration
- Be open to feedback on personal coding choices
- Share discoveries that could benefit the entire team
- Respect team decisions that may override personal preferences

Personal rules in the my-rules directory provide a valuable space for individual growth and customization while maintaining team consistency and collaboration standards. 