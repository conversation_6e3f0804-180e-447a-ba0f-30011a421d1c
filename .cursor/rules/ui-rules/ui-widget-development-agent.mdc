---
description: This rule governs UI widget development patterns in the Glidic Flutter App. It should be applied when: (1) Creating new UI components and screens, (2) Breaking down large widgets into smaller, reusable components, (3) Organizing shared widgets within feature presentation folders, (4) Implementing responsive design patterns, (5) Creating custom widgets with proper state management, (6) Setting up widget composition and reusability patterns.
globs: 
alwaysApply: false
---

# UI Widget Development Patterns

## Critical Rules

- **Break down large widgets** into smaller, focused, reusable components
- **Shared widgets** go in `feature/presentation/widgets/` for feature-level reuse
- **Screen-specific widgets** go in `feature/presentation/screens/screen_name/widgets/`
- **Core widgets** go in `core/presentation/widgets/` for cross-feature reuse
- Use **consistent naming** for widget files: `component_name_widget.dart`
- Follow **single responsibility principle** - each widget has one clear purpose
- Implement **proper state management** separation (StatelessWidget vs StatefulWidget)
- Use **constants** for all styling, dimensions, and colors

## Widget Organization Structure

```
lib/
├── core/presentation/widgets/          # Cross-feature reusable widgets
│   ├── buttons/
│   │   ├── primary_button.dart
│   │   ├── outline_primary_button.dart
│   │   └── circle_icon_button.dart
│   ├── inputs/
│   │   ├── primary_text_field.dart
│   │   └── search_input_widget.dart
│   ├── layouts/
│   │   ├── app_scaffold.dart
│   │   └── responsive_layout.dart
│   └── indicators/
│       ├── loading_indicator.dart
│       └── progress_indicator.dart
├── features/auth/presentation/
│   ├── screens/login_screen/
│   │   ├── login_screen.dart           # Main screen
│   │   ├── login_cubit.dart           # State management
│   │   ├── login_state.dart           # State classes
│   │   └── widgets/                   # Screen-specific widgets
│   │       ├── login_form_widget.dart
│   │       ├── login_header_widget.dart
│   │       └── forgot_password_link_widget.dart
│   └── widgets/                       # Feature-level shared widgets
│       ├── auth_social_buttons.dart
│       ├── password_strength_indicator.dart
│       └── terms_acceptance_widget.dart
```

## Widget Decomposition Patterns

### Large Screen Breakdown
```dart
// ❌ Wrong - Monolithic screen widget
class BadLoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(Dimensions.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section - should be extracted
              Row(
                children: [
                  Image.asset(PathConstants.logoImage, height: Dimensions.iconLg),
                  SizedBox(width: Dimensions.gapMd),
                  Text('Welcome Back', style: TextStyleConstants.title),
                ],
              ),
              SizedBox(height: Dimensions.extraLargePadding),
              
              // Form section - should be extracted
              Form(
                child: Column(
                  children: [
                    PrimaryTextField(
                      labelText: AuthConstants.labelEmail,
                      hintText: AuthConstants.hintEmail,
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) => _validateEmail(value),
                    ),
                    SizedBox(height: Dimensions.gapLg),
                    PrimaryTextField(
                      labelText: AuthConstants.labelPassword,
                      hintText: AuthConstants.hintPassword,
                      obscureText: true,
                      validator: (value) => _validatePassword(value),
                    ),
                    SizedBox(height: Dimensions.gapMd),
                    
                    // Forgot password - should be extracted
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () => _onForgotPasswordTap(context),
                        child: Text(
                          AuthConstants.linkForgotPassword,
                          style: TextStyleConstants.bodySmall.copyWith(
                            color: ColorConstants.primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              Spacer(),
              
              // Action buttons - should be extracted
              Column(
                children: [
                  PrimaryButton(
                    text: AuthConstants.buttonTextLogin,
                    onPressed: () => _onLoginTap(context),
                    isLoading: context.select<LoginCubit, bool>(
                      (cubit) => cubit.state is LoginLoading,
                    ),
                  ),
                  SizedBox(height: Dimensions.gapMd),
                  OutlinePrimaryButton(
                    text: AuthConstants.buttonTextSignUp,
                    onPressed: () => _onSignUpTap(context),
                  ),
                  SizedBox(height: Dimensions.gapLg),
                  
                  // Social login - should be extracted
                  Row(
                    children: [
                      Expanded(child: Divider()),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: Dimensions.gapMd),
                        child: Text(
                          AuthConstants.labelOrContinueWith,
                          style: TextStyleConstants.bodySmall,
                        ),
                      ),
                      Expanded(child: Divider()),
                    ],
                  ),
                  SizedBox(height: Dimensions.gapMd),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildSocialButton(PathConstants.googleIcon, () => _onGoogleLogin()),
                      _buildSocialButton(PathConstants.appleIcon, () => _onAppleLogin()),
                      _buildSocialButton(PathConstants.facebookIcon, () => _onFacebookLogin()),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

### ✅ Correct - Decomposed Screen with Extracted Widgets

#### Main Screen (Clean and Focused)
```dart
class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<LoginCubit>(),
      child: const _LoginScreenContent(),
    );
  }
}

class _LoginScreenContent extends StatelessWidget {
  const _LoginScreenContent();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(Dimensions.defaultPadding),
          child: Column(
            children: [
              const LoginHeaderWidget(),
              SizedBox(height: Dimensions.extraLargePadding),
              const Expanded(child: LoginFormWidget()),
              const LoginActionsWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
```

#### Extracted Header Widget
```dart
// lib/features/auth/presentation/screens/login_screen/widgets/login_header_widget.dart
class LoginHeaderWidget extends StatelessWidget {
  const LoginHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(
          PathConstants.logoImage,
          height: Dimensions.iconLg,
        ),
        SizedBox(width: Dimensions.gapMd),
        Text(
          AuthConstants.titleWelcomeBack,
          style: TextStyleConstants.title,
        ),
      ],
    );
  }
}
```

#### Extracted Form Widget
```dart
// lib/features/auth/presentation/screens/login_screen/widgets/login_form_widget.dart
class LoginFormWidget extends StatefulWidget {
  const LoginFormWidget({super.key});

  @override
  State<LoginFormWidget> createState() => _LoginFormWidgetState();
}

class _LoginFormWidgetState extends State<LoginFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          PrimaryTextField(
            controller: _emailController,
            labelText: AuthConstants.labelEmail,
            hintText: AuthConstants.hintEmail,
            keyboardType: TextInputType.emailAddress,
            validator: (value) => context.read<LoginCubit>().validateEmail(value),
          ),
          SizedBox(height: Dimensions.gapLg),
          
          PrimaryTextField(
            controller: _passwordController,
            labelText: AuthConstants.labelPassword,
            hintText: AuthConstants.hintPassword,
            obscureText: true,
            validator: (value) => context.read<LoginCubit>().validatePassword(value),
          ),
          SizedBox(height: Dimensions.gapMd),
          
          const ForgotPasswordLinkWidget(),
          
          const Spacer(),
          
          PrimaryButton(
            text: AuthConstants.buttonTextLogin,
            onPressed: () => _handleLoginSubmit(context),
            isLoading: context.select<LoginCubit, bool>(
              (cubit) => cubit.state is LoginLoading,
            ),
          ),
        ],
      ),
    );
  }

  void _handleLoginSubmit(BuildContext context) {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<LoginCubit>().login(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
```

#### Feature-Level Shared Widget
```dart
// lib/features/auth/presentation/widgets/auth_social_buttons_widget.dart
class AuthSocialButtonsWidget extends StatelessWidget {
  const AuthSocialButtonsWidget({
    super.key,
    required this.onGooglePressed,
    required this.onApplePressed,
    required this.onFacebookPressed,
  });

  final VoidCallback onGooglePressed;
  final VoidCallback onApplePressed;
  final VoidCallback onFacebookPressed;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            const Expanded(child: Divider()),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Dimensions.gapMd),
              child: Text(
                AuthConstants.labelOrContinueWith,
                style: TextStyleConstants.bodySmall,
              ),
            ),
            const Expanded(child: Divider()),
          ],
        ),
        SizedBox(height: Dimensions.gapMd),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            SocialLoginButton(
              iconPath: PathConstants.googleIcon,
              onPressed: onGooglePressed,
              label: AuthConstants.labelGoogleLogin,
            ),
            SocialLoginButton(
              iconPath: PathConstants.appleIcon,
              onPressed: onApplePressed,
              label: AuthConstants.labelAppleLogin,
            ),
            SocialLoginButton(
              iconPath: PathConstants.facebookIcon,
              onPressed: onFacebookPressed,
              label: AuthConstants.labelFacebookLogin,
            ),
          ],
        ),
      ],
    );
  }
}
```

## Examples

<example>
  ```dart
  // ✅ Correct - Well-structured recording screen with decomposed widgets
  
  // Main screen (lib/features/recording/presentation/screens/recording/recording_screen.dart)
  class RecordingScreen extends StatelessWidget {
    const RecordingScreen({super.key});

    @override
    Widget build(BuildContext context) {
      return BlocProvider(
        create: (context) => sl<RecordingCubit>(),
        child: const _RecordingScreenContent(),
      );
    }
  }

  class _RecordingScreenContent extends StatelessWidget {
    const _RecordingScreenContent();

    @override
    Widget build(BuildContext context) {
      return Scaffold(
        backgroundColor: ColorConstants.backgroundColor,
        appBar: const RecordingAppBarWidget(),
        body: const Column(
          children: [
            RecordingStatusWidget(),
            Expanded(child: RecordingWaveformWidget()),
            RecordingControlsWidget(),
            RecordingInfoWidget(),
          ],
        ),
      );
    }
  }
  
  // Screen-specific widget (lib/features/recording/presentation/screens/recording/widgets/recording_controls_widget.dart)
  class RecordingControlsWidget extends StatelessWidget {
    const RecordingControlsWidget({super.key});

    @override
    Widget build(BuildContext context) {
      return BlocBuilder<RecordingCubit, RecordingState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.all(Dimensions.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (state is RecordingInProgress) ...[
                  CircleIconButton(
                    icon: Icons.pause,
                    onPressed: () => context.read<RecordingCubit>().pauseRecording(),
                    backgroundColor: ColorConstants.warningColor,
                  ),
                  CircleIconButton(
                    icon: Icons.stop,
                    onPressed: () => context.read<RecordingCubit>().stopRecording(),
                    backgroundColor: ColorConstants.errorColor,
                  ),
                ] else if (state is RecordingPaused) ...[
                  CircleIconButton(
                    icon: Icons.play_arrow,
                    onPressed: () => context.read<RecordingCubit>().resumeRecording(),
                    backgroundColor: ColorConstants.successColor,
                  ),
                  CircleIconButton(
                    icon: Icons.stop,
                    onPressed: () => context.read<RecordingCubit>().stopRecording(),
                    backgroundColor: ColorConstants.errorColor,
                  ),
                ] else ...[
                  CircleIconButton(
                    icon: Icons.mic,
                    onPressed: () => context.read<RecordingCubit>().startRecording(),
                    backgroundColor: ColorConstants.primaryColor,
                    size: Dimensions.iconLg,
                  ),
                ],
              ],
            ),
          );
        },
      );
    }
  }
  
  // Feature-level shared widget (lib/features/recording/presentation/widgets/waveform_display_widget.dart)
  class WaveformDisplayWidget extends StatelessWidget {
    const WaveformDisplayWidget({
      super.key,
      required this.waveformData,
      this.isPlaying = false,
      this.currentPosition = 0.0,
      this.height,
      this.color,
    });

    final List<double> waveformData;
    final bool isPlaying;
    final double currentPosition;
    final double? height;
    final Color? color;

    @override
    Widget build(BuildContext context) {
      return Container(
        height: height ?? Dimensions.waveformHeight,
        padding: EdgeInsets.symmetric(horizontal: Dimensions.gapMd),
        child: CustomPaint(
          painter: WaveformPainter(
            waveformData: waveformData,
            isPlaying: isPlaying,
            currentPosition: currentPosition,
            color: color ?? ColorConstants.primaryColor,
          ),
          size: Size.infinite,
        ),
      );
    }
  }
  ```
</example>

<example type="invalid">
  ```dart
  // ❌ Wrong - Monolithic widget with mixed responsibilities
  class BadRecordingScreen extends StatefulWidget {
    @override
    State<BadRecordingScreen> createState() => _BadRecordingScreenState();
  }

  class _BadRecordingScreenState extends State<BadRecordingScreen> {
    // ❌ State management mixed with UI
    bool isRecording = false;
    List<double> waveformData = [];
    Duration recordingDuration = Duration.zero;
    
    @override
    Widget build(BuildContext context) {
      return Scaffold(
        // ❌ Hardcoded colors and dimensions
        backgroundColor: Color(0xFFFFFFFF),
        appBar: AppBar(
          // ❌ Hardcoded title and styling
          title: Text(
            'Recording',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          backgroundColor: Color(0xFF8BAABD),
          actions: [
            // ❌ Should be extracted to separate widget
            IconButton(
              icon: Icon(Icons.settings),
              onPressed: () => Navigator.pushNamed(context, '/settings'),
            ),
          ],
        ),
        body: Column(
          children: [
            // ❌ Status section should be extracted
            Container(
              padding: EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    isRecording ? 'Recording...' : 'Ready to Record',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 8.0),
                  Text(
                    '${recordingDuration.inMinutes}:${(recordingDuration.inSeconds % 60).toString().padLeft(2, '0')}',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.w700),
                  ),
                ],
              ),
            ),
            
            // ❌ Waveform section should be extracted
            Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16.0),
                decoration: BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Center(
                  child: waveformData.isEmpty
                      ? Text('No audio data')
                      : CustomPaint(
                          painter: WaveformPainter(waveformData),
                          size: Size.infinite,
                        ),
                ),
              ),
            ),
            
            // ❌ Controls section should be extracted
            Container(
              padding: EdgeInsets.all(24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // ❌ Button creation logic should be extracted
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        isRecording = !isRecording;
                      });
                      // ❌ Business logic mixed with UI
                      if (isRecording) {
                        _startRecording();
                      } else {
                        _stopRecording();
                      }
                    },
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: isRecording ? Color(0xFFFF0000) : Color(0xFF8BAABD),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isRecording ? Icons.stop : Icons.mic,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    
    // ❌ Business logic should be in Cubit
    void _startRecording() {
      // Recording logic
    }
    
    void _stopRecording() {
      // Stop recording logic
    }
  }
  ```
</example>

## Core Reusable Widgets

### Button Components
```dart
// lib/core/presentation/widgets/buttons/primary_button.dart
class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? Dimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorConstants.primaryColor,
          foregroundColor: ColorConstants.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(Dimensions.primaryButtonBorderRadius),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? SizedBox(
                height: Dimensions.iconSm,
                width: Dimensions.iconSm,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.onPrimary),
                ),
              )
            : Text(
                text,
                style: TextStyleConstants.buttonText,
              ),
      ),
    );
  }
}
```

## Widget Testing Patterns

```dart
void main() {
  group('LoginHeaderWidget', () {
    testWidgets('should display logo and welcome text', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginHeaderWidget(),
          ),
        ),
      );

      expect(find.text(AuthConstants.titleWelcomeBack), findsOneWidget);
      expect(find.byType(Image), findsOneWidget);
    });
  });

  group('PrimaryButton', () {
    testWidgets('should show loading indicator when isLoading is true', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PrimaryButton(
              text: 'Test Button',
              onPressed: () {},
              isLoading: true,
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Test Button'), findsNothing);
    });
  });
}
```

## Responsive Design Patterns

```dart
// lib/core/presentation/widgets/layouts/responsive_layout.dart
class ResponsiveLayout extends StatelessWidget {
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= ScreenSizes.desktop) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= ScreenSizes.tablet) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}
```