import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:glidic_app/core/common/constants/color_constants.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/features/recording/data/services/upload_worker_service.dart';

/// Feature-first folder structure
/// - Dependency injection using GetIt
/// - State management with Cubit (flutter_bloc)
/// - Navigation with go_router
/// - Exception-based error handling with AppFailure hierarchy
/// - Environment variable management with flutter_dotenv
/// - Proper separation of concerns across presentation, domain, and data layers
void main() async {
  // Ensure Flutter binding is initialized before running the app
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection container
  // This sets up all the required dependencies for the entire application
  // including environment configuration, repositories, use cases, data sources, and cubits
  await initializeDependencies();

  // Set preferred device orientations (optional)
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Run the application
  runApp(const GlidicApp());
}

/// Root application widget
///
/// This widget serves as the entry point for the entire application.
/// It configures the MaterialApp with:
/// - Theme configuration
/// - Router configuration using go_router
/// - Global app settings
class GlidicApp extends StatefulWidget {
  const GlidicApp({super.key});

  @override
  State<GlidicApp> createState() => _GlidicAppState();
}

class _GlidicAppState extends State<GlidicApp> with WidgetsBindingObserver {
  final _navigatorKey = GlobalKey<NavigatorState>();
  late final UploadWorkerService _uploadWorkerService;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    AppNavigator.init(
      navKey: _navigatorKey,
    );

    // Initialize upload worker service
    _uploadWorkerService = sl<UploadWorkerService>();
    _uploadWorkerService.initialize();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _uploadWorkerService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      routerConfig: AppNavigator.appRouter.config(),
      // App metadata
      title: 'Glidic App',
      debugShowCheckedModeBanner: false,

      // Localization configuration
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      // Locale resolution strategy
      localeResolutionCallback: (locale, supportedLocales) {
        // Check if the current device locale is supported
        for (final supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale?.languageCode) {
            return supportedLocale;
          }
        }
        // If the device locale is not supported, return English as default
        return const Locale('en');
      },

      // Theme configuration
      theme: _buildAppTheme(),
    );
  }

  /// Build fixed app theme configuration
  ///
  /// Defines the single, consistent visual appearance for the application
  /// Uses Material 3 design system with custom color scheme based on ColorConstants
  ThemeData _buildAppTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: ColorConstants.primaryColor,
        brightness: Brightness.light,
      ),

      // Input decoration theme for consistent text field styling
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: ColorConstants.backgroundColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: ColorConstants.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide:
              const BorderSide(color: ColorConstants.primaryColor, width: 2),
        ),
      ),

      // Elevated button theme for consistent button styling
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // App bar theme
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 1,
      ),
    );
  }
}
