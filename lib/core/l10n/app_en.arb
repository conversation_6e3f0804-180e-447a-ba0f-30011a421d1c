{"@@locale": "en", "@@last_modified": "2025-07-02T00:00:00.000Z", "welcome": "Welcome back!", "@welcome": {"description": "Welcome message displayed to users"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Section title for quick actions"}, "addNew": "Add New", "@addNew": {"description": "Button text for adding new items"}, "settings": "Settings", "@settings": {"description": "Button text for settings"}, "featureComingSoon": "Feature coming soon", "@featureComingSoon": {"description": "Message shown for features under development"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "noDataAvailable": "No data available", "@noDataAvailable": {"description": "Message when no data is available"}, "pullToRefresh": "Pull to refresh", "@pullToRefresh": {"description": "Pull to refresh instruction"}, "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "@loginFailed": {"description": "Error message for failed login attempt"}, "unexpectedError": "An unexpected error occurred. Please try again.", "@unexpectedError": {"description": "Generic error message for unexpected errors"}, "loadingDashboardData": "Loading dashboard data...", "@loadingDashboardData": {"description": "Loading message for dashboard data"}, "failedToLoadDashboard": "Failed to load dashboard", "@failedToLoadDashboard": {"description": "Error message when dashboard fails to load"}, "refreshing": "Refreshing...", "@refreshing": {"description": "Message shown when refreshing data"}, "failedToRefresh": "Failed to refresh: {error}", "@failedToRefresh": {"description": "Error message when refresh fails", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "welcomeMessage": "Here's what's happening with your dashboard today.", "@welcomeMessage": {"description": "Welcome message subtitle on dashboard"}, "forgotPasswordFeatureComingSoon": "Forgot password feature coming soon", "@forgotPasswordFeatureComingSoon": {"description": "Message for forgot password feature under development"}, "emailPlaceholder": "Email Address", "@emailPlaceholder": {"description": "Placeholder text for email input field"}, "passwordPlaceholder": "Password", "@passwordPlaceholder": {"description": "Placeholder text for password input field"}, "loginButton": "<PERSON><PERSON>", "@loginButton": {"description": "Text for login button"}, "signUpButton": "Create New Account", "@signUpButton": {"description": "Text for sign up button"}, "forgotPasswordLink": "Forgot Password?", "@forgotPasswordLink": {"description": "Text for forgot password link"}, "signUpFeatureComingSoon": "Sign up feature coming soon", "@signUpFeatureComingSoon": {"description": "Message for sign up feature under development"}, "deleteAction": "Delete", "@deleteAction": {"description": "Action to delete an item."}, "saveAction": "Save", "@saveAction": {"description": "Action to save an item."}, "remaining": "rem.", "@remaining": {"description": "Remaining quantity or time"}, "minute": "m", "@minute": {"description": "Minute unit label"}, "importAudio": "Import Audio", "@importAudio": {"description": "Import audio button text"}, "startRecording": "Start Recording", "@startRecording": {"description": "Start recording button text"}, "allFiles": "All Files", "@allFiles": {"description": "All files button text"}, "recordingCancel": "Cancel", "@recordingCancel": {"description": "<PERSON><PERSON> text to cancel recording"}, "recordingDelete": "Delete", "@recordingDelete": {"description": "Button text to delete recording"}, "recordingConfirmDelete": "Delete Recording", "@recordingConfirmDelete": {"description": "Title for delete confirmation dialog"}, "recordingConfirmDeleteMessage": "Are you sure you want to delete this recording? This action cannot be undone.", "@recordingConfirmDeleteMessage": {"description": "Message for delete confirmation dialog"}, "errorRecordingStartFailed": "Failed to start recording. Please check your device and try again.", "@errorRecordingStartFailed": {"description": "Error when recording fails to start"}, "errorRecordingSaveFailed": "Failed to save recording: {error}", "@errorRecordingSaveFailed": {"description": "Error when recording fails to save", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "errorRecordingDeleteFailed": "Failed to delete recording: {error}", "@errorRecordingDeleteFailed": {"description": "Error when recording fails to delete", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "errorRecordingPermissionRequired": "Microphone access is needed to record audio. Please check your device settings.", "@errorRecordingPermissionRequired": {"description": "Error message for microphone permission requirement"}, "errorRecordingStorageSpace": "Not enough storage space to record. Please free up some space.", "@errorRecordingStorageSpace": {"description": "Error when insufficient storage space for recording"}, "errorRecordingHardware": "Audio recording is not available. Please check your device settings.", "@errorRecordingHardware": {"description": "Error when audio hardware is not available"}, "errorRecordingDeviceCheck": "Unable to start recording. Please check your device and try again.", "@errorRecordingDeviceCheck": {"description": "Generic device check error for recording"}, "permissionOpenSettings": "Open Settings", "@permissionOpenSettings": {"description": "Button text to open device settings"}, "uploadInProgress": "Uploading...", "@uploadInProgress": {"description": "Status text when upload is in progress"}, "uploadCompleted": "Upload completed", "@uploadCompleted": {"description": "Status text when upload is completed"}, "uploadFailed": "Upload failed", "@uploadFailed": {"description": "Status text when upload fails"}, "uploadCancelled": "Upload cancelled", "@uploadCancelled": {"description": "Status text when upload is cancelled"}, "uploadProgress": "Uploading: {progress}%", "@uploadProgress": {"description": "Upload progress display", "placeholders": {"progress": {"type": "int", "description": "Upload progress percentage"}}}, "errorUploadGeneric": "Upload error occurred", "@errorUploadGeneric": {"description": "Generic upload error"}, "errorUploadNetworkUnavailable": "Network connection not available for upload", "@errorUploadNetworkUnavailable": {"description": "Error when network is not available for upload"}, "errorUploadServerUnavailable": "Upload server is not available", "@errorUploadServerUnavailable": {"description": "Error when upload server is not available"}, "errorUploadFileSizeExceeded": "File size exceeds upload limit", "@errorUploadFileSizeExceeded": {"description": "Error when file is too large for upload"}, "errorUploadAuthenticationFailed": "Upload authentication failed", "@errorUploadAuthenticationFailed": {"description": "Error when upload authentication fails"}, "uploadWorkerStarted": "Upload worker started", "@uploadWorkerStarted": {"description": "Message when upload worker starts"}, "uploadWorkerStopped": "Upload worker stopped", "@uploadWorkerStopped": {"description": "Message when upload worker stops"}, "transcription": "Transcription", "@transcription": {"description": "Button text for transcription"}, "aiSummary": "AI Summary", "@aiSummary": {"description": "Button text for AI Summary"}}