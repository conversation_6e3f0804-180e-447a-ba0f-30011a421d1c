import 'package:glidic_app/core/common/config/env_config.dart' show EnvConfig;

/// Network-related constants
/// Contains all network configuration, API endpoints, and network error messages
/// Now uses environment variables for sensitive configuration
class NetworkConstants {
  // Private constructor to prevent instantiation
  NetworkConstants._();

  // API Configuration
  /// Base URL for the API
  static String get baseUrl => EnvConfig.baseUrl;

  /// API version identifier
  static String get apiVersion => EnvConfig.apiVersion;

  /// Connection timeout duration for HTTP requests
  static const Duration connectTimeout = Duration(seconds: 30);

  /// Receive timeout duration for HTTP requests
  static const Duration receiveTimeout = Duration(seconds: 30);

  /// Send timeout duration for HTTP requests
  static const Duration sendTimeout = Duration(seconds: 30);

  static const int defaultPerPage = 20;

  // API Endpoints
  /// Authentication endpoints
  static const String authLogin = '/auth/login';
  static const String authRegister = '/auth/register';
  static const String authLogout = '/auth/logout';
  static const String authMe = '/auth/me';
  static const String authRefresh = '/auth/refresh';
  static const String authForgotPassword = '/auth/forgot-password';
  static const String authVerifyEmail = '/auth/verify-email';

  /// Transcription endpoints
  static const String transcriptionUpload = '/transcriptions';
  static const String transcriptionList = '/transcriptions/users/{userId}';
  static const String transcriptionAudio = '/transcriptions/{audioId}';

  /// Home/Dashboard endpoints
  static const String homeDashboard = '/dashboard';
  static const String homeRefreshDashboard = '/dashboard/refresh';

  // Error Messages
  /// Error message displayed when network connection is unavailable
  static const String networkErrorMessage =
      'Please check your internet connection';

  /// Error message displayed when server returns an error
  static const String serverErrorMessage =
      'Something went wrong. Please try again later';

  /// Error message displayed for unknown/unexpected errors
  static const String unknownErrorMessage = 'An unexpected error occurred';

  /// Error message displayed for authentication failures
  static const String authErrorMessage = 'Authentication failed';
}
