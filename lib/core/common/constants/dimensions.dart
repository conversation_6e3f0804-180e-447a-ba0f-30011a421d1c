class Dimensions {
  const Dimensions._();

  // Gaps
  /// Extra small gap for tight spacing (4.0)
  static const double gapXS = 4.0;
  /// Small gap for standard spacing (6.0)
  static const double gapSm = 6.0;
  /// Medium gap for standard spacing (8.0)
  static const double gapMd = 8.0;
  /// Large gap for generous spacing (10.0)
  static const double gapLg = 10.0;
  /// Extra large gap for maximum spacing (12.0)
  static const double gapXL = 12.0;

  // Radius
  /// Extra small radius for tight (2.0)
  static const double radiusXS = 2.0;
  /// Small radius for standard (4.0)
  static const double radiusSm = 4.0;
  /// Medium radius for standard (8.0)
  static const double radiusMd = 8.0;
  /// Large radius for generous (10.0)
  static const double radiusLg = 10.0;
  /// Extra large radius for maximum (12.0)
  static const double radiusXL = 12.0;
  /// Primary button radius (matching Figma design)
  static const double primaryButtonBorderRadius = 24.0;
  /// Text field border radius (matching Figma design)
  static const double textFieldBorderRadius = 10.0;
  /// Circle radius for circular progress bar
  static const double circleRadius = 10000;

  // Icon
  /// Extra small icon for tight (12.0)
  static const double iconXS = 12.0;
  /// Small icon for standard (16.0)
  static const double iconSm = 16.0;
  /// Medium icon for standard (24.0)
  static const double iconMd = 24.0;
  /// Large icon for generous (34.0)
  static const double iconLg = 34.0;
  /// Icon size for button (18.0)
  static const double iconButton = 18.0;

  // Border
  /// Extra small border for tight (1.0)
  static const double borderXS = 1.0;
  /// Small border for standard (2.0)
  static const double borderSm = 2.0;
  /// Medium border for standard (4.0)
  static const double borderMd = 4.0;
  /// Large border for generous (6.0)
  static const double borderLg = 6.0;

  // Padding and Spacing
  /// Default padding value used throughout the app
  static const double defaultPadding = 16.0;

  /// Medium padding value for standard spacing
  static const double mediumPadding = 12.0;

  /// Small padding value for tight spacing
  static const double smallPadding = 8.0;

  /// Large padding value for generous spacing
  static const double largePadding = 24.0;

  /// Extra large padding value for maximum spacing
  static const double extraLargePadding = 32.0;

  // Component Dimensions
  /// Standard height for buttons across the app
  static const double buttonHeight = 48.0;

  /// Text field horizontal padding (matching Figma design)
  static const double textFieldHorizontalPadding = 14.0;

  /// Text field vertical padding (matching Figma design)
  static const double textFieldVerticalPadding = 13;

  // Border Width
  /// Outline button border width (matching Figma design)
  static const double outlineButtonBorderWidth = 1.0;
}

