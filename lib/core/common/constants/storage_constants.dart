/// Storage and validation constants
/// Contains storage keys, validation rules, and related constants
class StorageConstants {
  // Private constructor to prevent instantiation
  StorageConstants._();

  // Storage Keys
  /// Key for storing access token in secure storage
  static const String accessTokenKey = 'access_token';

  /// Key for storing refresh token in secure storage
  static const String refreshTokenKey = 'refresh_token';

  /// Key for storing user data in local storage
  static const String userDataKey = 'user_data';

  /// Key for storing user data in preferences (alias for userDataKey)
  static const String userKey = 'user_data';

  /// Key for tracking if this is the first app launch
  static const String isFirstLaunchKey = 'is_first_launch';

  // Secure Storage Configuration
  /// Secure storage preferences name for Android
  static const String secureStoragePrefsName = 'FlutterSecureStorage';

  /// Secure storage key prefix
  static const String secureStorageKeyPrefix = 'flutter_secure_storage_';

  /// iOS keychain group identifier
  static const String iosKeychainGroup =
      'com.example.flutter-base-project.keychain';

  /// iOS keychain account identifier
  static const String iosKeychainAccount = 'flutter_secure_storage';

  /// macOS keychain group identifier
  static const String macosKeychainGroup =
      'com.example.flutter-base-project.keychain';

  /// macOS keychain account identifier
  static const String macosKeychainAccount = 'flutter_secure_storage';

  // Storage Validation Constants
  /// Maximum allowed length for storage keys
  static const int maxKeyLength = 255;

  /// Maximum allowed length for storage values
  static const int maxValueLength = 8192;

  /// Maximum allowed length for string lists
  static const int maxListLength = 100;

  // Validation Constants
  /// Minimum required length for passwords
  static const int minPasswordLength = 6;

  /// Maximum allowed length for passwords
  static const int maxPasswordLength = 50;

  /// Regular expression pattern for email validation
  static const String emailPattern =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';

  // Validation Error Messages
  /// Error message displayed for validation failures
  static const String validationErrorMessage = 'Please check your input';
}
