import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

/// TextStyleConstants is a class that contains all the [TextStyle] for the app
class TextStyleConstants {
  TextStyleConstants._();

  static const String fontFamily = 'Inter';

  /// TextStyle title
  /// [fontSize]: 24, [fontWeight]: 700, [color]: ColorConstants.textPrimary
  static const TextStyle title = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle body
  /// [fontSize]: 16, [fontWeight]: 400, [color]: ColorConstants.textPrimary
  static const TextStyle body = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle bodyMedium
  /// [fontSize]: 14, [fontWeight]: 400, [color]: ColorConstants.textPrimary
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle bodySmall
  /// [fontSize]: 11, [fontWeight]: 400, [color]: ColorConstants.textPrimary
  static const TextStyle bodySmall = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w400,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle labelLarge
  /// [fontSize]: 14, [fontWeight]: 700, [color]: ColorConstants.textPrimary
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w700,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle label
  /// [fontSize]: 12, [fontWeight]: 400, [color]: ColorConstants.textPrimary
  static const TextStyle label = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle label medium
  /// [fontSize]: 12, [fontWeight]: 500, [color]: ColorConstants.textPrimary
  static const TextStyle labelMedium = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle label small
  /// [fontSize]: 10, [fontWeight]: 400, [color]: ColorConstants.textPrimary
  static const TextStyle labelSmall = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w300,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );

  /// TextStyle label extra small
  /// [fontSize]: 9, [fontWeight]: 400, [color]: ColorConstants.textPrimary
  static const TextStyle labelExtraSmall = TextStyle(
    fontSize: 8,
    fontWeight: FontWeight.w400,
    fontFamily: fontFamily,
    color: ColorConstants.textPrimary,
  );
}

/// TextStyleExtension is a extension providing a set of methods to modify the [TextStyle]
extension TextStyleExtension on TextStyle {
  // TextStyle size
  /// Set [fontSize] of [TextStyle] to 16
  TextStyle get s16 => copyWith(fontSize: 16);

  /// Set [fontSize] of [TextStyle] to 14
  TextStyle get s14 => copyWith(fontSize: 14);

  /// Set [fontSize] of [TextStyle] to 12
  TextStyle get s12 => copyWith(fontSize: 12);

  /// Set [fontSize] of [TextStyle] to 11
  TextStyle get s11 => copyWith(fontSize: 11);

  // TextStyle weight
  /// Set [fontWeight] of [TextStyle] to 400
  TextStyle get w400 => copyWith(fontWeight: FontWeight.w400);

  /// Set [fontWeight] of [TextStyle] to 500
  TextStyle get w500 => copyWith(fontWeight: FontWeight.w500);

  /// Set [fontWeight] of [TextStyle] to 700
  TextStyle get w700 => copyWith(fontWeight: FontWeight.w700);

  // TextStyle color
  /// Set [color] of [TextStyle] to primary
  TextStyle get primaryColor => copyWith(color: ColorConstants.textPrimary);

  /// Set [color] of [TextStyle] to secondary
  TextStyle get secondaryColor => copyWith(color: ColorConstants.textSecondary);

  /// Set [color] of [TextStyle] to disabled
  TextStyle get disabledColor => copyWith(color: ColorConstants.textDisabled);

  /// Set [color] of [TextStyle] to onPrimary
  TextStyle get onPrimaryColor => copyWith(color: ColorConstants.onPrimary);

  /// Set [color] of [TextStyle] to onSecondary
  TextStyle get onSecondaryColor => copyWith(color: ColorConstants.onSecondary);

  /// Set [color] of [TextStyle] to onTertiary
  TextStyle get onTertiaryColor => copyWith(color: ColorConstants.onTertiary);

  // TextStyle overflow
  /// Set [overflow] of [TextStyle] to ellipsis
  TextStyle get ellipsis => copyWith(overflow: TextOverflow.ellipsis);

  /// Set [overflow] of [TextStyle] to clip
  TextStyle get clip => copyWith(overflow: TextOverflow.clip);

  /// Set [overflow] of [TextStyle] to fade
  TextStyle get fade => copyWith(overflow: TextOverflow.fade);

  /// Set [overflow] of [TextStyle] to visible
  TextStyle get visible => copyWith(overflow: TextOverflow.visible);

  // TextStyle decoration
  /// Set [decoration] of [TextStyle] to underline
  TextStyle get underline => copyWith(decoration: TextDecoration.underline);

  /// Set [decoration] of [TextStyle] to overline
  TextStyle get overline => copyWith(decoration: TextDecoration.overline);

  /// Set [decoration] of [TextStyle] to lineThrough
  TextStyle get lineThrough => copyWith(decoration: TextDecoration.lineThrough);

  /// Set [decoration] of [TextStyle] to none
  TextStyle get none => copyWith(decoration: TextDecoration.none);
}
