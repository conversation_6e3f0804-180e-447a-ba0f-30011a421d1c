import 'package:flutter/material.dart';

/// Color constants
/// Defines the common color palette for the application
/// This provides a centralized location for managing app colors
class ColorConstants {
  // Private constructor to prevent instantiation
  ColorConstants._();

  // Primary Colors
  static const Color primaryColor = Color(0xFF8BAABD);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryLight = Color(0xFFBBDEFB);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color primaryContainer = Color(0xFFF3F3F3);
  static const Color onPrimaryContainer = Color(0xFFD9D9D9);

  // Secondary Colors
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color secondaryDark = Color(0xFF018786);
  static const Color secondaryLight = Color(0xFFB2DFDB);
  static const Color onSecondary = Color(0xFF000000);

  // Tertiary Colors
  static const Color tertiaryColor = Color(0xFFFF0000);
  static const Color onTertiary = Color(0xFFFFFFFF);

  // Surface and Background Colors
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceVariant = Color(0xFF757575);

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textDisabled = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Status Colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFFF0000);
  static const Color infoColor = Color(0xFF2196F3);
  static const Color onError = Color(0xFFFFFFFF);

  // Border and Outline Colors
  static const Color borderColor = Color(0xFFE0E0E0);
  static const Color outlineColor = Color(0xFFBDBDBD);
  static const Color dividerColor = Color(0xFFE0E0E0);

  // Semantic Colors for UI Components
  static const Color buttonPrimary = primaryColor;
  static const Color buttonSecondary = Color(0xFFE0E0E0);
  static const Color buttonDisabled = Color(0xFFBDBDBD);

  // Primary Button Colors (matching Figma design)
  static const Color primaryButtonBackground = Color(0xFF000000);
  static const Color primaryButtonText = Color(0xFFFFFFFF);

  // Outline Primary Button Colors (matching Figma design)
  static const Color outlinePrimaryButtonBackground = Colors.transparent;
  static const Color outlinePrimaryButtonBorder = Color(0xFF000000);
  static const Color outlinePrimaryButtonText = Color(0xFF000000);
  static const Color outlinePrimaryButtonDisabledBorder = Color(0xFFBDBDBD);
  static const Color outlinePrimaryButtonDisabledText = Color(0xFFBDBDBD);

  static const Color inputBorder = outlineColor;
  static const Color inputFocused = primaryColor;
  static const Color inputError = errorColor;
  static const Color inputBackground = surfaceColor;

  // Text Field Colors (matching Figma design)
  static const Color textFieldBackground = Color(0xFFFFFFFF);
  static const Color textFieldBorder = Color(0xFFEDF1F3);
  static const Color textFieldText = Color(0xFF1A1C1E);
  static const Color textFieldIcon = Color(0xFFACB5BB);
  static const Color textFieldShadow = Color(0xFFE4E5E7);
  static const Color textFieldPlaceholder = Color(0xFF1A1C1E);

  // Record wave form
  static const Color recordWaveForm = Color(0xFFADADAD);

  // Utility methods for color variations
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  static Color primaryWithOpacity(double opacity) {
    return primaryColor.withValues(alpha: opacity);
  }
}
