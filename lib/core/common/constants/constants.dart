// Constants barrel file
// Exports all constant files for convenient importing
//
// Usage:
// ```dart
// import 'package:your_app/core/constants/constants.dart';
//
// // Access any constant from any file
// final url = NetworkConstants.baseUrl;
// final padding = UIConstants.defaultPadding;
// final token = StorageConstants.accessTokenKey;
// ```
// Export all constant files
export 'color_constants.dart';
export 'database_constants.dart';
export 'dimensions.dart';
export 'error_codes.dart';
export 'network_constants.dart';
export 'path_constants.dart';
export 'storage_constants.dart';
export 'text_style_constants.dart';

class Constants {
  /// App Group ID for sharing data between main app and widget extension
  static const String appGroupId = 'group.jp.co.iotbank.glidici.shared';
  static const String appUrlScheme = 'glidic';

  /// Upload worker config
  static const int uploadWorkerInterval = 1;
  static const int uploadWorkerMaxConcurrentUploads = 3;
  static const int maxUploadFileSize = 100 * 1024 * 1024;

  /// Recording constants
  static const String defaultUploadFileName = 'audio_file.m4a';
  static const String defaultAudioExtension = '.m4a';
  static const int maxAmplitudeBufferSize = 30;
  static const int defaultAudioBitrate = 128000;
  static const int defaultSampleRate = 44100;
  static const int liveActivityUpdateInterval = 100;

  static const String recordingStatusRecording = 'recording';
  static const String recordingStatusPaused = 'paused';
  static const String recordingStatusStopped = 'stopped';
  static const String recordingStatusCancelled = 'cancelled';
  static const String recordingStatusSaved = 'saved';

  /// Transcription processing statuses
  static const String transcriptionStatusPending = 'pending';
  static const String transcriptionStatusProcessing = 'processing';
  static const String transcriptionStatusCompleted = 'completed';
  static const String transcriptionStatusFailed = 'failed';

  /// Time constants
  static const int millisecondsPerSecond = 1000;
}
