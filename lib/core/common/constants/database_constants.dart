/// Database-related constants
/// Contains database configuration, table names, and database-related constants
class DatabaseConstants {
  // Private constructor to prevent instantiation
  DatabaseConstants._();

  // Database Configuration
  /// Database name for the local SQLite database
  static const String databaseName = 'app_database.db';

  /// Database version for schema migrations
  static const int databaseVersion = 2;

  /// Database schema version identifier
  static const int schemaVersion = 1;

  // Table Names
  /// Table name for storing user data
  static const String usersTable = 'users';

  /// Table name for storing audio records
  static const String recordsTable = 'records';

  /// Table name for storing authentication tokens
  static const String tokensTable = 'tokens';

  /// Table name for storing app settings
  static const String settingsTable = 'settings';

  /// Table name for storing cached data
  static const String cacheTable = 'cache';

  // Column Names - Users Table
  /// Primary key column for users table
  static const String userIdColumn = 'id';

  /// Email column for users table
  static const String userEmailColumn = 'email';

  /// Name column for users table
  static const String userNameColumn = 'name';

  /// Profile picture URL column for users table
  static const String userProfilePictureColumn = 'profile_picture';

  /// Email verification status column for users table
  static const String userEmailVerifiedColumn = 'is_email_verified';

  /// Created at timestamp column for users table
  static const String userCreatedAtColumn = 'created_at';

  /// Updated at timestamp column for users table
  static const String userUpdatedAtColumn = 'updated_at';

  // Column Names - Tokens Table
  /// Primary key column for tokens table
  static const String tokenIdColumn = 'id';

  /// User ID foreign key column for tokens table
  static const String tokenUserIdColumn = 'user_id';

  /// Access token column for tokens table
  static const String accessTokenColumn = 'access_token';

  /// Refresh token column for tokens table
  static const String refreshTokenColumn = 'refresh_token';

  /// Token expiry timestamp column for tokens table
  static const String tokenExpiryColumn = 'expires_at';

  /// Token created at timestamp column for tokens table
  static const String tokenCreatedAtColumn = 'created_at';

  // Column Names - Settings Table
  /// Primary key column for settings table
  static const String settingIdColumn = 'id';

  /// Setting key column for settings table
  static const String settingKeyColumn = 'key';

  /// Setting value column for settings table
  static const String settingValueColumn = 'value';

  /// Setting type column for settings table
  static const String settingTypeColumn = 'type';

  /// Setting updated at timestamp column for settings table
  static const String settingUpdatedAtColumn = 'updated_at';

  // Column Names - Cache Table
  /// Primary key column for cache table
  static const String cacheIdColumn = 'id';

  /// Cache key column for cache table
  static const String cacheKeyColumn = 'key';

  /// Cache value column for cache table
  static const String cacheValueColumn = 'value';

  /// Cache expiry timestamp column for cache table
  static const String cacheExpiryColumn = 'expires_at';

  /// Cache created at timestamp column for cache table
  static const String cacheCreatedAtColumn = 'created_at';

  // Database Constraints
  /// Maximum length for email fields
  static const int maxEmailLength = 255;

  /// Maximum length for name fields
  static const int maxNameLength = 100;

  /// Maximum length for URL fields
  static const int maxUrlLength = 500;

  /// Maximum length for token fields
  static const int maxTokenLength = 1000;

  /// Maximum length for setting key fields
  static const int maxSettingKeyLength = 100;

  /// Maximum length for setting value fields
  static const int maxSettingValueLength = 1000;

  /// Maximum length for cache key fields
  static const int maxCacheKeyLength = 255;

  /// Maximum length for recording title field
  static const int maxTitleLength = 200;

  /// Maximum length for file path field
  static const int maxFilePathLength = 1000;

  // Cache Configuration
  /// Default cache expiry duration in hours
  static const int defaultCacheExpiryHours = 24;

  /// Maximum number of cache entries to keep
  static const int maxCacheEntries = 1000;

  /// Cache cleanup interval in hours
  static const int cacheCleanupIntervalHours = 6;

  // Database Queries
  /// Query to check if database tables exist
  static const String checkTablesExistQuery = '''
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name IN (?, ?, ?, ?)
  ''';

  /// Query to get database version
  static const String getDatabaseVersionQuery = 'PRAGMA user_version';

  /// Query to set database version
  static const String setDatabaseVersionQuery = 'PRAGMA user_version = ?';
}
