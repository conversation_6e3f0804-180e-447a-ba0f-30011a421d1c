/// Centralized error codes for the application
/// This class contains all error codes used throughout the app for consistent
/// error handling and programmatic checking of specific error types
class ErrorCodes {
  // Private constructor to prevent instantiation
  ErrorCodes._();

  // =============================================================================
  // NETWORK ERROR CODES
  // =============================================================================

  /// No internet connection available
  static const String noConnection = 'NO_CONNECTION';

  /// Request timed out
  static const String timeout = 'TIMEOUT';

  /// HTTP error with specific status code (will be prefixed with HTTP_)
  static const String httpErrorPrefix = 'HTTP_';

  /// Connection failed
  static const String connectionFailed = 'CONNECTION_FAILED';

  /// Rate limit exceeded
  static const String rateLimitExceeded = 'RATE_LIMIT_EXCEEDED';

  /// Server error
  static const String serverError = 'SERVER_ERROR';

  // =============================================================================
  // AUTHENTICATION ERROR CODES
  // =============================================================================

  /// User session has expired
  static const String tokenExpired = 'TOKEN_EXPIRED';

  /// Invalid email or password provided
  static const String invalidCredentials = 'INVALID_CREDENTIALS';

  /// Authentication failed
  static const String authenticationFailed = 'AUTHENTICATION_FAILED';

  /// Access denied (authorization error)
  static const String accessDenied = 'ACCESS_DENIED';

  // =============================================================================
  // VALIDATION ERROR CODES
  // =============================================================================

  /// Required field is missing
  static const String requiredField = 'REQUIRED_FIELD';

  /// Invalid date range provided
  static const String invalidDateRange = 'INVALID_DATE_RANGE';

  // =============================================================================
  // STORAGE ERROR CODES
  // =============================================================================

  /// Cache operation failed
  static const String cacheError = 'CACHE_ERROR';

  // =============================================================================
  // CALENDAR ERROR CODES
  // =============================================================================

  /// Calendar permissions denied by user
  static const String calendarPermissionDenied = 'CALENDAR_PERMISSION_DENIED';

  /// Calendar access failed
  static const String calendarAccessFailed = 'CALENDAR_ACCESS_FAILED';

  /// Calendar event creation failed
  static const String calendarEventCreationFailed =
      'CALENDAR_EVENT_CREATION_FAILED';

  /// Calendar event retrieval failed
  static const String calendarEventRetrievalFailed =
      'CALENDAR_EVENT_RETRIEVAL_FAILED';

  /// No calendar events found for the specified criteria
  static const String noCalendarEventsFound = 'NO_CALENDAR_EVENTS_FOUND';

  /// Google Calendar not found on device
  static const String googleCalendarNotFound = 'GOOGLE_CALENDAR_NOT_FOUND';

  /// Google Calendar not available (read-only or disabled)
  static const String googleCalendarNotAvailable =
      'GOOGLE_CALENDAR_NOT_AVAILABLE';

  // Google Calendar API specific error codes
  /// Google Calendar API authentication failed
  static const String googleCalendarAuthFailed = 'GOOGLE_CALENDAR_AUTH_FAILED';

  /// Google Calendar API access denied
  static const String googleCalendarAccessDenied =
      'GOOGLE_CALENDAR_ACCESS_DENIED';

  /// Google Calendar API quota exceeded
  static const String googleCalendarQuotaExceeded =
      'GOOGLE_CALENDAR_QUOTA_EXCEEDED';

  /// Google Calendar API event creation failed
  static const String googleCalendarEventCreationFailed =
      'GOOGLE_CALENDAR_EVENT_CREATION_FAILED';

  /// Google Calendar API not configured
  static const String googleCalendarNotConfigured =
      'GOOGLE_CALENDAR_NOT_CONFIGURED';

  // =============================================================================
  // RECORDING ERROR CODES
  // =============================================================================

  /// Microphone permission denied by user
  static const String recordingPermissionDenied = 'RECORDING_PERMISSION_DENIED';

  /// Recording hardware failure or device unavailable
  static const String recordingHardwareFailure = 'RECORDING_HARDWARE_FAILURE';

  /// Insufficient storage space for recording
  static const String recordingInsufficientStorage =
      'RECORDING_INSUFFICIENT_STORAGE';

  /// Recording file access or creation failed
  static const String recordingFileFailure = 'RECORDING_FILE_FAILURE';

  /// Invalid recording state for operation
  static const String recordingInvalidState = 'RECORDING_INVALID_STATE';

  /// Recording start operation failed
  static const String recordingStartFailed = 'RECORDING_START_FAILED';

  /// Recording stop operation failed
  static const String recordingStopFailed = 'RECORDING_STOP_FAILED';

  /// Recording save operation failed
  static const String recordingSaveFailed = 'RECORDING_SAVE_FAILED';

  /// File size exceeds upload limit
  static const String recordingFileSizeExceeded =
      'RECORDING_FILE_SIZE_EXCEEDED';

  // =============================================================================
  // RECORD ERROR CODES
  // =============================================================================

  /// Record not found
  static const String recordNotFound = 'RECORD_NOT_FOUND';

  // =============================================================================
  // GENERAL ERROR CODES
  // =============================================================================

  /// Unexpected error occurred
  static const String unexpectedError = 'UNEXPECTED_ERROR';

  /// Format error (JSON parsing, etc.)
  static const String formatError = 'FORMAT_ERROR';

  static const String databaseError = 'DATABASE_ERROR';

  // =============================================================================
  // HELPER METHODS
  // =============================================================================

  /// Generate HTTP error code with status code
  static String httpError(int statusCode) => '$httpErrorPrefix$statusCode';

  /// Check if error code is a network-related error
  static bool isNetworkError(String? code) {
    if (code == null) return false;
    return code == noConnection ||
        code == timeout ||
        code == connectionFailed ||
        code == rateLimitExceeded ||
        code == serverError ||
        code.startsWith(httpErrorPrefix);
  }

  /// Check if error code is an authentication-related error
  static bool isAuthError(String? code) {
    if (code == null) return false;
    return code == tokenExpired ||
        code == invalidCredentials ||
        code == authenticationFailed ||
        code == accessDenied;
  }

  /// Check if error code is a validation-related error
  static bool isValidationError(String? code) {
    if (code == null) return false;
    return code == requiredField || code == invalidDateRange;
  }

  /// Check if error code is a calendar-related error
  static bool isCalendarError(String? code) {
    if (code == null) return false;
    return code == calendarPermissionDenied ||
        code == calendarAccessFailed ||
        code == calendarEventCreationFailed ||
        code == calendarEventRetrievalFailed ||
        code == noCalendarEventsFound ||
        code == googleCalendarNotFound ||
        code == googleCalendarNotAvailable;
  }

  /// Get all error codes as a list (useful for testing or debugging)
  static List<String> getAllErrorCodes() {
    return [
      noConnection,
      timeout,
      connectionFailed,
      rateLimitExceeded,
      serverError,
      tokenExpired,
      invalidCredentials,
      authenticationFailed,
      accessDenied,
      requiredField,
      invalidDateRange,
      cacheError,
      calendarPermissionDenied,
      calendarAccessFailed,
      calendarEventCreationFailed,
      calendarEventRetrievalFailed,
      noCalendarEventsFound,
      googleCalendarNotFound,
      googleCalendarNotAvailable,
      googleCalendarAuthFailed,
      googleCalendarAccessDenied,
      googleCalendarQuotaExceeded,
      googleCalendarEventCreationFailed,
      googleCalendarNotConfigured,
      recordingPermissionDenied,
      recordingHardwareFailure,
      recordingInsufficientStorage,
      recordingFileFailure,
      recordingInvalidState,
      recordingStartFailed,
      recordingStopFailed,
      recordingSaveFailed,
      recordingFileSizeExceeded,
      unexpectedError,
      formatError,
    ];
  }
}
