/// Asset path constants
/// Contains all asset paths for images, icons, fonts, and other resources
///
/// Usage:
/// ```dart
/// import 'package:flutter_svg/flutter_svg.dart';
/// import 'package:glidic_app/core/common/constants/path_constants.dart';
///
/// // Use SVG icons
/// SvgPicture.asset(PathConstants.homeIcon)
///
/// // Use images
/// Image.asset(PathConstants.logoImage)
/// ```
class PathConstants {
  // Private constructor to prevent instantiation
  PathConstants._();

  // Base asset paths
  static const String _imagesPath = 'assets/images';
  static const String _iconsPath = 'assets/icons';
  static const String _fontsPath = 'assets/fonts';

  // ===================
  // IMAGES
  // ===================

  /// App logo image
  static const String logoImage = '$_imagesPath/logo.svg';

  //Devices images
  /// Earbuds image
  static const String earbudImage = '$_imagesPath/earbud.png';

  /// Note device image
  static const String noteDeviceImage = '$_imagesPath/note_device.png';

  // ===================
  // ICONS (SVG)
  // ===================

  // Navigation icons
  /// Home navigation icon
  static const String homeIcon = '$_iconsPath/home.svg';

  /// Profile navigation icon
  static const String profileIcon = '$_iconsPath/profile.svg';

  /// Settings navigation icon
  static const String settingsIcon = '$_iconsPath/settings.svg';

  /// Search icon
  static const String searchIcon = '$_iconsPath/search.svg';

  // Action icons
  /// Share icon
  static const String shareIcon = '$_iconsPath/share.svg';

  /// Rewind 15s icon
  static const String rewind15sIcon = '$_iconsPath/rewind_15s.svg';

  /// Scissors icon
  static const String scissorsIcon = '$_iconsPath/scissors.svg';

  /// Play icon
  static const String playIcon = '$_iconsPath/play.svg';

  /// Forward 15s icon
  static const String forward15sIcon = '$_iconsPath/forward_15s.svg';

  // UI icons
  /// Back arrow icon
  static const String backIcon = '$_iconsPath/back-icon.svg';

  /// Menu/Hamburger icon
  static const String menuIcon = '$_iconsPath/menu.svg';

  /// Message icon
  static const String messageIcon = '$_iconsPath/message.svg';

  /// Person pin icon
  static const String personPinIcon = '$_iconsPath/person-pin.svg';

  /// Dots horizontal icon
  static const String dotsHorizontalIcon = '$_iconsPath/dots_horizontal_icon.svg';

  /// Eye off icon (for password visibility toggle)
  static const String eyeOffIcon = '$_iconsPath/eye-off.svg';

  /// Hamburger button icon
  static const String hamburgerButtonIcon = '$_iconsPath/hamburger_button.svg';

  /// Star icon
  static const String starIcon = '$_iconsPath/star_border.svg';

  /// Clock icon
  static const String clockIcon = '$_iconsPath/clock.svg';

  // Status icons
  /// Pause icon
  static const String pauseIcon = '$_iconsPath/pause_icon.svg';

  /// Voice alert icon
  static const String voiceAlertIcon = '$_iconsPath/voice_alert.svg';

  // ===================
  // FONTS
  // ===================

  /// Primary font family name
  static const String primaryFontFamily = 'Roboto';

  /// Secondary font family name
  static const String secondaryFontFamily = 'OpenSans';

  // Font file paths (if using custom fonts)
  /// Primary font regular
  static const String primaryFontRegular = '$_fontsPath/Roboto-Regular.ttf';

  /// Primary font bold
  static const String primaryFontBold = '$_fontsPath/Roboto-Bold.ttf';

  /// Primary font medium
  static const String primaryFontMedium = '$_fontsPath/Roboto-Medium.ttf';

  // ===================
  // HELPER METHODS
  // ===================

  /// Get image path with proper extension
  static String getImagePath(String fileName, {String extension = 'png'}) {
    return '$_imagesPath/$fileName.$extension';
  }

  /// Get icon path (SVG)
  static String getIconPath(String fileName) {
    return '$_iconsPath/$fileName.svg';
  }

  /// Get font path
  static String getFontPath(String fileName, {String extension = 'ttf'}) {
    return '$_fontsPath/$fileName.$extension';
  }
}
