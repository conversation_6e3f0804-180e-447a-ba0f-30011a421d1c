import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';

/// Utility class for converting platform exceptions to AppFailure instances
/// Provides centralized exception handling logic for the try-catch pattern
class ExceptionHandler {
  ExceptionHandler._(); // Private constructor to prevent instantiation

  /// Convert any exception to an appropriate AppFailure
  /// This is the main entry point for exception handling
  static AppFailure handle(dynamic exception, [StackTrace? stackTrace]) {
    // If it's already an AppFailure, return as-is
    if (exception is AppFailure) {
      return exception;
    }

    // Handle Dio HTTP exceptions
    if (exception is DioException) {
      return _handleDioException(exception);
    }

    // Handle Socket exceptions (network connectivity)
    if (exception is SocketException) {
      return _handleSocketException(exception);
    }

    // Handle HTTP exceptions
    if (exception is HttpException) {
      return _handleHttpException(exception);
    }

    // Handle format exceptions (JSON parsing, etc.)
    if (exception is FormatException) {
      return _handleFormatException(exception);
    }

    // Handle timeout exceptions
    if (exception is TimeoutException) {
      return AppFailureConstructors.timeout();
    }

    // Handle argument errors (validation)
    if (exception is ArgumentError) {
      return ValidationFailure(
        'Invalid input: ${exception.message}',
        code: 'INVALID_ARGUMENT',
      );
    }

    // Handle state errors
    if (exception is StateError) {
      return BusinessFailure(
        'Invalid operation: ${exception.message}',
        code: 'INVALID_STATE',
      );
    }

    // Fallback for unknown exceptions
    return AppFailureConstructors.fromException(exception, stackTrace);
  }

  /// Handle Dio HTTP exceptions with specific error mapping
  static AppFailure _handleDioException(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppFailureConstructors.timeout();

      case DioExceptionType.connectionError:
        return AppFailureConstructors.noConnection();

      case DioExceptionType.badResponse:
        return _handleHttpStatusCode(
          exception.response?.statusCode ?? 0,
          exception.response?.statusMessage ?? 'Unknown error',
          exception.response?.data,
        );

      case DioExceptionType.cancel:
        return const BusinessFailure(
          'Request was cancelled',
          code: 'REQUEST_CANCELLED',
        );

      case DioExceptionType.badCertificate:
        return const NetworkFailure(
          'SSL certificate error',
          code: 'SSL_ERROR',
        );

      case DioExceptionType.unknown:
        return NetworkFailure(
          'Network error: ${exception.message}',
          code: 'NETWORK_ERROR',
        );
    }
  }

  /// Handle HTTP status codes and map to appropriate failures
  static AppFailure _handleHttpStatusCode(
    int statusCode,
    String message,
    dynamic responseData,
  ) {
    switch (statusCode) {
      case 400:
        return ValidationFailure(
          'Bad request: $message',
          code: 'BAD_REQUEST',
          details: {'statusCode': statusCode, 'response': responseData},
        );

      case 401:
        return AppFailureConstructors.tokenExpired();

      case 403:
        return const AuthFailure(
          'Access denied',
          code: 'ACCESS_DENIED',
        );

      case 404:
        return NetworkFailure(
          'Resource not found',
          code: 'NOT_FOUND',
          statusCode: statusCode,
        );

      case 409:
        return BusinessFailure(
          'Conflict: $message',
          code: 'CONFLICT',
          details: {'statusCode': statusCode, 'response': responseData},
        );

      case 422:
        return ValidationFailure(
          'Validation failed: $message',
          code: 'VALIDATION_ERROR',
          details: {'statusCode': statusCode, 'response': responseData},
        );

      case 429:
        return NetworkFailure(
          'Too many requests. Please try again later.',
          code: 'RATE_LIMITED',
          statusCode: statusCode,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return NetworkFailure(
          'Server error: $message',
          code: 'SERVER_ERROR',
          statusCode: statusCode,
        );

      default:
        return NetworkFailure(
          'HTTP error ($statusCode): $message',
          code: 'HTTP_ERROR',
          statusCode: statusCode,
        );
    }
  }

  /// Handle Socket exceptions (network connectivity issues)
  static AppFailure _handleSocketException(SocketException exception) {
    if (exception.message.toLowerCase().contains('failed host lookup')) {
      return AppFailureConstructors.noConnection();
    }

    return NetworkFailure(
      'Connection failed: ${exception.message}',
      code: 'CONNECTION_FAILED',
      details: {'originalException': exception.toString()},
    );
  }

  /// Handle HTTP exceptions
  static AppFailure _handleHttpException(HttpException exception) {
    return NetworkFailure(
      'HTTP error: ${exception.message}',
      code: 'HTTP_EXCEPTION',
      details: {'uri': exception.uri?.toString()},
    );
  }

  /// Handle Format exceptions (JSON parsing, etc.)
  static AppFailure _handleFormatException(FormatException exception) {
    return AppFailureConstructors.formatError(
      exception.message,
      details: {
        'source': exception.source,
        'offset': exception.offset,
      },
    );
  }

  /// Check if an exception indicates a network connectivity issue
  static bool isNetworkException(dynamic exception) {
    if (exception is SocketException) return true;
    if (exception is DioException) {
      return exception.type == DioExceptionType.connectionError ||
          exception.type == DioExceptionType.connectionTimeout;
    }
    return false;
  }

  /// Check if an exception indicates an authentication issue
  static bool isAuthException(dynamic exception) {
    if (exception is AuthFailure) return true;
    if (exception is DioException) {
      return exception.response?.statusCode == 401 ||
          exception.response?.statusCode == 403;
    }
    return false;
  }

  /// Check if an exception indicates a validation issue
  static bool isValidationException(dynamic exception) {
    if (exception is ValidationFailure) return true;
    if (exception is ArgumentError) return true;
    if (exception is DioException) {
      return exception.response?.statusCode == 400 ||
          exception.response?.statusCode == 422;
    }
    return false;
  }
}
