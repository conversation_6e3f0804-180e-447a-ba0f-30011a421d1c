import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/failures.dart';

// =============================================================================
// ERROR LOGGING
// =============================================================================

/// Simple error logging utility
class ErrorLogger {
  /// Log a failure with context
  static void logFailure(
    AppFailure failure, {
    String? context,
    Map<String, dynamic>? additionalData,
  }) {
    final logData = {
      'failure_type': failure.runtimeType.toString(),
      'category': failure.category,
      'message': failure.message,
      'code': failure.code,
      'can_retry': failure.canRetry,
      'is_critical': failure.isCritical,
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
      ...?failure.details,
      ...?additionalData,
    };

    if (kDebugMode) {
      developer.log(
        'Error: ${failure.message}',
        name: 'ErrorHandler',
        error: failure,
        stackTrace: StackTrace.current,
      );
    }

    // In production, you might want to send this to a logging service
    // like Firebase Crashlytics, Sentry, etc.
    _logToService(logData);
  }

  static void _logToService(Map<String, dynamic> logData) {
    // TODO: Implement your preferred logging service
    // Examples: Firebase Crashlytics, Sentry, custom analytics
  }
}

// =============================================================================
// STATE MANAGEMENT INTEGRATION
// =============================================================================

/// Mixin for Cubit states to provide error handling capabilities
mixin ErrorStateMixin {
  /// The failure associated with this state
  AppFailure? get failure => null;

  /// Whether this state has an error
  bool get hasError => failure != null;

  /// Whether the error is retryable
  bool get canRetry => failure?.canRetry ?? false;

  /// Whether the error is critical
  bool get isCritical => failure?.isCritical ?? false;
}

/// Mixin for Cubits to provide error handling methods
mixin ErrorHandlingMixin<T> on Cubit<T> {
  /// Execute a use case with automatic error handling (new exception-based pattern)
  Future<void> executeWithErrorHandling<R>(
    Future<R> Function() useCase, {
    required void Function(R data) onSuccess,
    void Function()? onLoading,
    String? errorContext,
    bool canRetry = true,
    VoidCallback? retryAction,
  }) async {
    try {
      onLoading?.call();

      final result = await useCase();
      onSuccess(result);
    } catch (e, stackTrace) {
      final failure =
          e is AppFailure ? e : ExceptionHandler.handle(e, stackTrace);
      _handleFailure(
        failure,
        context: errorContext,
        canRetry: canRetry,
        retryAction: retryAction,
      );
    }
  }

  /// Handle a failure and emit appropriate error state
  void _handleFailure(
    AppFailure failure, {
    String? context,
    bool canRetry = true,
    VoidCallback? retryAction,
  }) {
    // Log the error
    ErrorLogger.logFailure(
      failure,
      context: context,
      additionalData: {
        'cubit_type': runtimeType.toString(),
        'current_state': state.runtimeType.toString(),
        'can_retry': canRetry,
        'has_retry_action': retryAction != null,
      },
    );

    // Emit error state (must be implemented by concrete cubit)
    emitErrorState(failure, canRetry: canRetry, retryAction: retryAction);
  }

  /// Abstract method to emit error state - must be implemented by concrete cubit
  void emitErrorState(
    AppFailure failure, {
    bool canRetry = true,
    VoidCallback? retryAction,
  });
}

// =============================================================================
// ERROR MESSAGE RESOLUTION
// =============================================================================

/// Utility to resolve user-friendly error messages
class ErrorMessageResolver {
  /// Get user-friendly error message
  static String getErrorMessage(BuildContext context, AppFailure failure) {
    // Try to get localized message first
    final localizedMessage = _getLocalizedMessage(context, failure);
    if (localizedMessage != null) return localizedMessage;

    // Fallback to default messages based on failure type
    return _getDefaultMessage(failure);
  }

  /// Get error title for dialogs
  static String getErrorTitle(BuildContext context, AppFailure failure) {
    switch (failure.category) {
      case 'network':
        return 'Connection Error';
      case 'auth':
        return 'Authentication Error';
      case 'validation':
        return 'Validation Error';
      case 'storage':
        return 'Storage Error';
      case 'ble':
        return 'Bluetooth Error';
      case 'business':
        return 'Operation Failed';
      case 'system':
        return 'System Error';
      default:
        return 'Error';
    }
  }

  static String? _getLocalizedMessage(
    BuildContext context,
    AppFailure failure,
  ) {
    // TODO: Implement localization lookup based on failure.code
    // Example: AppLocalizations.of(context)?.errorMessage(failure.code ?? 'unknown')
    return null;
  }

  static String _getDefaultMessage(AppFailure failure) {
    // Return the failure message or a category-based default
    if (failure.message.isNotEmpty) return failure.message;

    switch (failure.category) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection.';
      case 'auth':
        return 'Authentication failed. Please try again.';
      case 'validation':
        return 'Please check your input and try again.';
      case 'storage':
        return 'Unable to access local storage.';
      case 'ble':
        return 'Bluetooth connection failed.';
      case 'business':
        return 'Operation could not be completed.';
      case 'system':
        return 'An unexpected error occurred.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }
}

// =============================================================================
// COMMON ERROR HANDLING UTILITIES
// =============================================================================

/// Standardized error handling patterns for the application
/// This class provides reusable error handling methods that follow
/// the established Clean Architecture patterns
class StandardErrorHandler {
  /// Standard error handling for use cases
  /// This method provides consistent error handling across all use cases
  static Future<T> handleUseCase<T>(
    Future<T> Function() useCase, {
    String? context,
  }) async {
    try {
      return await useCase();
    } catch (e, stackTrace) {
      final failure = e is AppFailure ? e : ExceptionHandler.handle(e, stackTrace);

      // Log the error with context
      ErrorLogger.logFailure(
        failure,
        context: context ?? 'Use Case Execution',
      );

      // Re-throw the failure to be handled by the presentation layer
      throw failure;
    }
  }

  /// Standard error handling for repository operations
  /// This method provides consistent error handling across all repositories
  static Future<T> handleRepository<T>(
    Future<T> Function() operation, {
    String? context,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      final failure = e is AppFailure ? e : ExceptionHandler.handle(e, stackTrace);

      // Log the error with context
      ErrorLogger.logFailure(
        failure,
        context: context ?? 'Repository Operation',
      );

      // Re-throw the failure to be handled by the use case layer
      throw failure;
    }
  }

  /// Standard error handling for data source operations
  /// This method provides consistent error handling across all data sources
  static Future<T> handleDataSource<T>(
    Future<T> Function() operation, {
    String? context,
  }) async {
    try {
      return await operation();
    } catch (e, stackTrace) {
      final failure = e is AppFailure ? e : ExceptionHandler.handle(e, stackTrace);

      // Log the error with context
      ErrorLogger.logFailure(
        failure,
        context: context ?? 'Data Source Operation',
      );

      // Re-throw the failure to be handled by the repository layer
      throw failure;
    }
  }

  /// Check if an error should trigger a retry
  static bool shouldRetry(AppFailure failure) {
    return failure.canRetry && !failure.isCritical;
  }

  /// Get retry delay based on failure type
  static Duration getRetryDelay(AppFailure failure, int attemptCount) {
    // Exponential backoff for network errors
    if (failure is NetworkFailure) {
      return Duration(seconds: (2 * attemptCount).clamp(1, 30));
    }

    // Fixed delay for other retryable errors
    return const Duration(seconds: 3);
  }
}
