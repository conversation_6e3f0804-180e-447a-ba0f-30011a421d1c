import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/constants/error_codes.dart';

/// Base class for all application failures
/// Simplified error handling following Clean Architecture principles
/// Now extends Exception to work with try-catch pattern
abstract class AppFailure extends Equatable implements Exception {
  const AppFailure({
    required this.message,
    this.code,
    this.details,
  });

  /// Human-readable error message
  final String message;

  /// Optional error code for programmatic handling
  final String? code;

  /// Optional additional details (for debugging/logging)
  final Map<String, dynamic>? details;

  /// Error category for UI display and handling logic
  String get category;

  /// Whether this error can be retried
  bool get canRetry => false;

  /// Whether this is a critical error requiring immediate attention
  bool get isCritical => false;

  /// Enhanced toString method for better debugging
  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('$runtimeType: $message');

    if (code != null) {
      buffer.write(' (Code: $code)');
    }

    if (details != null && details!.isNotEmpty) {
      buffer.write(' - Details: $details');
    }

    return buffer.toString();
  }

  @override
  List<Object?> get props => [message, code, details];
}

// =============================================================================
// NETWORK FAILURES
// =============================================================================

/// Network-related failures (API, connectivity, timeouts)
class NetworkFailure extends AppFailure {
  // ignore: use_super_parameters
  const NetworkFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.statusCode,
  }) : super(message: message, code: code, details: details);

  final int? statusCode;

  @override
  String get category => 'network';

  @override
  bool get canRetry => true;

  @override
  bool get isCritical => statusCode != null && statusCode! >= 500;

  @override
  List<Object?> get props => [...super.props, statusCode];
}

/// Authentication and authorization failures
class AuthFailure extends AppFailure {
  // ignore: use_super_parameters
  const AuthFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.isTokenExpired = false,
  }) : super(message: message, code: code, details: details);

  final bool isTokenExpired;

  @override
  String get category => 'auth';

  @override
  bool get canRetry => isTokenExpired;

  @override
  List<Object?> get props => [...super.props, isTokenExpired];
}

// =============================================================================
// VALIDATION FAILURES
// =============================================================================

/// Input validation failures
class ValidationFailure extends AppFailure {
  // ignore: use_super_parameters
  const ValidationFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.fieldErrors,
  }) : super(message: message, code: code, details: details);

  /// Field-specific validation errors
  final Map<String, List<String>>? fieldErrors;

  @override
  String get category => 'validation';

  @override
  List<Object?> get props => [...super.props, fieldErrors];
}

// =============================================================================
// STORAGE FAILURES
// =============================================================================

/// Storage-related failures (database, secure storage, preferences)
class StorageFailure extends AppFailure {
  // ignore: use_super_parameters
  const StorageFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.storageType,
  }) : super(message: message, code: code, details: details);

  /// Type of storage (database, secure_storage, preferences)
  final String? storageType;

  @override
  String get category => 'storage';

  @override
  bool get canRetry => true;

  @override
  List<Object?> get props => [...super.props, storageType];
}

// =============================================================================
// BLE FAILURES
// =============================================================================

/// Bluetooth Low Energy failures
class BleFailure extends AppFailure {
  // ignore: use_super_parameters
  const BleFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.deviceId,
  }) : super(message: message, code: code, details: details);

  final String? deviceId;

  @override
  String get category => 'ble';

  @override
  bool get canRetry => true;

  @override
  List<Object?> get props => [...super.props, deviceId];
}

// =============================================================================
// CALENDAR FAILURES
// =============================================================================

/// Calendar-related failures (permissions, access, event creation)
class CalendarFailure extends AppFailure {
  // ignore: use_super_parameters
  const CalendarFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.permissionDenied = false,
  }) : super(message: message, code: code, details: details);

  final bool permissionDenied;

  @override
  String get category => 'calendar';

  @override
  bool get canRetry => !permissionDenied;

  @override
  List<Object?> get props => [...super.props, permissionDenied];
}

// =============================================================================
// RECORDING FAILURES
// =============================================================================

/// Recording-related failures (permissions, hardware, file operations)
class RecordingFailure extends AppFailure {
  // ignore: use_super_parameters
  const RecordingFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.permissionDenied = false,
  }) : super(message: message, code: code, details: details);

  final bool permissionDenied;

  @override
  String get category => 'recording';

  @override
  bool get canRetry => !permissionDenied;

  @override
  List<Object?> get props => [...super.props, permissionDenied];
}

// =============================================================================
// DATABASE FAILURES
// =============================================================================
class DatabaseFailure extends AppFailure {
  // ignore: use_super_parameters
  const DatabaseFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
  }) : super(message: message, code: code, details: details);
  @override
  String get category => 'database';

  @override
  bool get canRetry => true;

  @override
  List<Object?> get props => [...super.props];
}

// =============================================================================
// BUSINESS LOGIC FAILURES
// =============================================================================

/// Business logic and domain rule failures
class BusinessFailure extends AppFailure {
  // ignore: use_super_parameters
  const BusinessFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
  }) : super(message: message, code: code, details: details);

  @override
  String get category => 'business';
}

// =============================================================================
// SYSTEM FAILURES
// =============================================================================

/// Platform-specific failures (iOS/Android features, permissions)
class PlatformFailure extends AppFailure {
  // ignore: use_super_parameters
  const PlatformFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.platform,
  }) : super(message: message, code: code, details: details);

  final String? platform;

  @override
  String get category => 'platform';

  @override
  List<Object?> get props => [...super.props, platform];
}

/// Unknown or unexpected failures
class UnknownFailure extends AppFailure {
  // ignore: use_super_parameters
  const UnknownFailure(
    String message, {
    String? code,
    Map<String, dynamic>? details,
    this.originalException,
    this.stackTrace,
  }) : super(message: message, code: code, details: details);

  final dynamic originalException;
  final StackTrace? stackTrace;

  @override
  String get category => 'system';

  @override
  bool get isCritical => true;

  @override
  List<Object?> get props => [...super.props, originalException, stackTrace];
}

class NotFoundFailure extends AppFailure {
  const NotFoundFailure(String message) : super(message: message);

  @override
  String get category => 'not_found';
}

// =============================================================================
// CONVENIENCE CONSTRUCTORS
// =============================================================================

/// Convenience constructors for common error scenarios
extension AppFailureConstructors on AppFailure {
  /// Create a network failure from HTTP status code
  static NetworkFailure fromHttpError(int statusCode, String message) {
    return NetworkFailure(
      message,
      code: ErrorCodes.httpError(statusCode),
      statusCode: statusCode,
      details: {'httpStatusCode': statusCode},
    );
  }

  /// Create a network failure for connectivity issues
  static NetworkFailure noConnection() {
    return const NetworkFailure(
      'No internet connection available',
      code: ErrorCodes.noConnection,
    );
  }

  /// Create a network failure for timeouts
  static NetworkFailure timeout() {
    return const NetworkFailure(
      'Request timed out',
      code: ErrorCodes.timeout,
    );
  }

  /// Create a network failure for connection failed
  static NetworkFailure connectionFailed() {
    return const NetworkFailure(
      'Connection failed',
      code: ErrorCodes.connectionFailed,
    );
  }

  /// Create a network failure for rate limit exceeded
  static NetworkFailure rateLimitExceeded() {
    return const NetworkFailure(
      'Too many requests',
      code: ErrorCodes.rateLimitExceeded,
      statusCode: 429,
    );
  }

  /// Create a network failure for server error
  static NetworkFailure serverError(int? statusCode) {
    return NetworkFailure(
      'Server error occurred',
      code: ErrorCodes.serverError,
      statusCode: statusCode,
    );
  }

  /// Create an auth failure for expired tokens
  static AuthFailure tokenExpired() {
    return const AuthFailure(
      'Your session has expired. Please login again.',
      code: ErrorCodes.tokenExpired,
      isTokenExpired: true,
    );
  }

  /// Create an auth failure for invalid credentials
  static AuthFailure invalidCredentials() {
    return const AuthFailure(
      'Invalid email or password',
      code: ErrorCodes.invalidCredentials,
    );
  }

  /// Create an auth failure for authentication failed
  static AuthFailure authenticationFailed() {
    return const AuthFailure(
      'Authentication failed',
      code: ErrorCodes.authenticationFailed,
      isTokenExpired: true,
    );
  }

  /// Create an auth failure for access denied
  static AuthFailure accessDenied() {
    return const AuthFailure(
      'Access denied',
      code: ErrorCodes.accessDenied,
    );
  }

  /// Create a validation failure for required fields
  static ValidationFailure requiredField(String fieldName) {
    return ValidationFailure(
      '$fieldName is required',
      code: ErrorCodes.requiredField,
      fieldErrors: {
        fieldName: const ['This field is required'],
      },
    );
  }

  /// Create an unknown failure from an exception
  static UnknownFailure fromException(
    dynamic exception, [
    StackTrace? stackTrace,
  ]) {
    return UnknownFailure(
      'An unexpected error occurred: ${exception.toString()}',
      code: ErrorCodes.unexpectedError,
      originalException: exception,
      details: {
        'exception': exception.toString(),
        'stackTrace': stackTrace?.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Create a storage failure for cache operations
  static StorageFailure cache(String message) {
    return StorageFailure(
      message,
      code: ErrorCodes.cacheError,
      storageType: 'cache',
    );
  }

  /// Create a business failure for format errors
  static BusinessFailure formatError(
    String message, {
    Map<String, dynamic>? details,
  }) {
    return BusinessFailure(
      'Data format error: $message',
      code: ErrorCodes.formatError,
      details: details,
    );
  }

  /// Create a calendar failure for permission denied
  static CalendarFailure calendarPermissionDenied() {
    return const CalendarFailure(
      'Calendar permissions denied by user',
      code: ErrorCodes.calendarPermissionDenied,
      permissionDenied: true,
    );
  }

  /// Create a calendar failure for access issues
  static CalendarFailure calendarAccessFailed(String message) {
    return CalendarFailure(
      'Calendar access failed: $message',
      code: ErrorCodes.calendarAccessFailed,
    );
  }

  /// Create a calendar failure for event creation
  static CalendarFailure calendarEventCreationFailed(String message) {
    return CalendarFailure(
      'Failed to create calendar event: $message',
      code: ErrorCodes.calendarEventCreationFailed,
    );
  }

  /// Create a calendar failure for event retrieval
  static CalendarFailure calendarEventRetrievalFailed(String message) {
    return CalendarFailure(
      'Failed to retrieve calendar events: $message',
      code: ErrorCodes.calendarEventRetrievalFailed,
    );
  }

  /// Create a calendar failure when Google Calendar is not found
  static CalendarFailure googleCalendarNotFound() {
    return const CalendarFailure(
      'Google Calendar not found on this device',
      code: ErrorCodes.googleCalendarNotFound,
    );
  }

  /// Create a calendar failure when Google Calendar is not available
  static CalendarFailure googleCalendarNotAvailable() {
    return const CalendarFailure(
      'Google Calendar is not available (read-only or disabled)',
      code: ErrorCodes.googleCalendarNotAvailable,
    );
  }

  /// Create a calendar failure for no events found
  static CalendarFailure noCalendarEventsFound() {
    return const CalendarFailure(
      'No calendar events found for the specified criteria',
      code: ErrorCodes.noCalendarEventsFound,
    );
  }

  // Google Calendar API specific failures
  /// Create a calendar failure for Google Calendar API authentication
  static CalendarFailure googleCalendarAuthFailed(String message) {
    return CalendarFailure(
      'Google Calendar authentication failed: $message',
      code: ErrorCodes.googleCalendarAuthFailed,
    );
  }

  /// Create a calendar failure for Google Calendar API access denied
  static CalendarFailure googleCalendarAccessDenied() {
    return const CalendarFailure(
      'Google Calendar access denied. Please grant calendar permissions.',
      code: ErrorCodes.googleCalendarAccessDenied,
    );
  }

  /// Create a calendar failure for Google Calendar API quota exceeded
  static CalendarFailure googleCalendarQuotaExceeded() {
    return const CalendarFailure(
      'Google Calendar API quota exceeded. Please try again later.',
      code: ErrorCodes.googleCalendarQuotaExceeded,
    );
  }

  /// Create a calendar failure for Google Calendar API event creation
  static CalendarFailure googleCalendarEventCreationFailed(String message) {
    return CalendarFailure(
      'Failed to create Google Calendar event: $message',
      code: ErrorCodes.googleCalendarEventCreationFailed,
    );
  }

  /// Create a calendar failure for Google Calendar API not configured
  static CalendarFailure googleCalendarNotConfigured() {
    return const CalendarFailure(
      'Google Calendar API is not properly configured',
      code: ErrorCodes.googleCalendarNotConfigured,
    );
  }

  // Recording failure convenience constructors
  /// Create a recording failure for permission denied
  static RecordingFailure recordingPermissionDenied() {
    return const RecordingFailure(
      'Recording permission denied',
      code: ErrorCodes.recordingPermissionDenied,
      permissionDenied: true,
    );
  }

  /// Create a recording failure for hardware issues
  static RecordingFailure recordingHardwareFailure() {
    return const RecordingFailure(
      'Recording hardware failure',
      code: ErrorCodes.recordingHardwareFailure,
    );
  }

  /// Create a recording failure for insufficient storage
  static RecordingFailure recordingInsufficientStorage() {
    return const RecordingFailure(
      'Insufficient storage space for recording',
      code: ErrorCodes.recordingInsufficientStorage,
    );
  }

  /// Create a recording failure for file operations
  static RecordingFailure recordingFileFailure(String message) {
    return RecordingFailure(
      message,
      code: ErrorCodes.recordingFileFailure,
    );
  }

  /// Create a recording failure for invalid state
  static RecordingFailure recordingInvalidState(String message) {
    return RecordingFailure(
      'Invalid recording state for operation: $message',
      code: ErrorCodes.recordingInvalidState,
    );
  }

  /// Create a recording failure for start operation
  static RecordingFailure recordingStartFailed(String message) {
    return RecordingFailure(
      'Failed to start recording $message',
      code: ErrorCodes.recordingStartFailed,
    );
  }

  /// Create a recording failure for stop operation
  static RecordingFailure recordingStopFailed(String message) {
    return RecordingFailure(
      'Failed to stop recording $message',
      code: ErrorCodes.recordingStopFailed,
    );
  }

  /// Create a recording failure for save operation
  static RecordingFailure recordingSaveFailed(String message) {
    return RecordingFailure(
      message,
      code: ErrorCodes.recordingSaveFailed,
    );
  }

  /// Create a recording failure for file size exceeded
  static RecordingFailure recordingFileSizeExceeded(String message) {
    return RecordingFailure(
      message,
      code: ErrorCodes.recordingFileSizeExceeded,
    );
  }

  /// Create database failure for database operations
  static DatabaseFailure database(String message) {
    return DatabaseFailure(
      message,
      code: ErrorCodes.databaseError,
    );
  }
}
