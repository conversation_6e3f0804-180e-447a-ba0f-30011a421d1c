extension DurationExtensions on Duration {
  /// Format duration based on the specified format
  ///
  /// [isShowFull] - If true, shows all units even if they are zero
  /// [format] - The format to use (hms or digital)
  String toFormattedString({
    bool isShowFull = false,
    DurationFormatType format = DurationFormatConfig.hms,
  }) {
    final hours = inHours;
    final minutes = inMinutes.remainder(60);
    final seconds = inSeconds.remainder(60);

    final values = [hours, minutes, seconds];
    final parts = <String>[];

    for (var i = 0; i < values.length; i++) {
      final value = values[i];
      final unit = format.units[i];

      if (value > 0 || isShowFull || i == 2) {
        final str = '${value.toString().padLeft(2, '0')}$unit';
        parts.add(str);
      }
    }

    return parts.join(format.separator);
  }

  /// Format duration based on the custom format string
  ///
  /// Format patterns:
  /// - h: hours (1-digit)
  /// - hh: hours (2-digits, zero-padded)
  /// - m: minutes (1-digit)
  /// - mm: minutes (2-digits, zero-padded)
  /// - s: seconds (1-digit)
  /// - ss: seconds (2-digits, zero-padded)
  ///
  /// Example formats:
  /// - "hh:mm:ss" => "01:01:01"
  /// - "h m s" => "1 1 1"
  /// - "hh hours mm minutes ss seconds" => "01 hours 01 minutes 01 seconds"
  /// - "mm:ss" => "61:01" (when duration is 1h 1m 1s)
  String format([String? formatStr]) {
    if (formatStr == null || formatStr.isEmpty) {
      return toFormattedString();
    }

    final hours = inHours;
    final minutes = inMinutes.remainder(60);
    final seconds = inSeconds.remainder(60);
    final totalMinutes = inMinutes;
    final totalSeconds = inSeconds;

    // Replace format placeholders
    String result = formatStr;

    // Handle 2-digit patterns first (to avoid conflicts with 1-digit patterns)
    result = result.replaceAll('hh', hours.toString().padLeft(2, '0'));
    result = result.replaceAll('mm', minutes.toString().padLeft(2, '0'));
    result = result.replaceAll('ss', seconds.toString().padLeft(2, '0'));

    // Handle 1-digit patterns
    result = result.replaceAll('h', hours.toString());
    result = result.replaceAll('m', minutes.toString());
    result = result.replaceAll('s', seconds.toString());

    // Special case for total values when only minutes or seconds are requested
    if (!formatStr.contains('h') && formatStr.contains('m')) {
      // Replace already substituted minutes with total minutes
      if (minutes > 0) {
        result = result.replaceAll(minutes.toString(), totalMinutes.toString());
      }
    }

    if (!formatStr.contains('h') &&
        !formatStr.contains('m') &&
        formatStr.contains('s')) {
      // Replace already substituted seconds with total seconds
      if (seconds > 0) {
        result = result.replaceAll(seconds.toString(), totalSeconds.toString());
      }
    }

    return result;
  }
}

class DurationFormatConfig {
  const DurationFormatConfig._();

  static const hms = DurationFormatType(separator: ' ', units: ['h', 'm', 's']);
  static const digital =
      DurationFormatType(separator: ':', units: ['', '', '']);
}

class DurationFormatType {
  /// Separator between units
  final String separator;

  /// Units are in order of hours, minutes, seconds
  ///
  /// [0] - hours
  /// [1] - minutes
  /// [2] - seconds
  final List<String> units;

  const DurationFormatType({
    required this.separator,
    required this.units,
  });
}
