import 'package:intl/intl.dart';

/// DateTime extensions for common date formatting operations
/// Provides centralized date formatting utilities following clean architecture principles
extension DateTimeExtensions on DateTime {
  /// Format date and time for calendar events display
  /// Returns format: "MMM dd, yyyy - HH:mm" (e.g., "Jan 15, 2024 - 14:30")
  String toCalendarEventFormat() {
    return DateFormat('MMM dd, yyyy - HH:mm').format(this);
  }

  /// Format date only for display
  /// Returns format: "MMM dd, yyyy" (e.g., "Jan 15, 2024")
  String toDateOnlyFormat() {
    return DateFormat('MMM dd, yyyy').format(this);
  }

  /// Format time only for display
  /// Returns format: "HH:mm" (e.g., "14:30")
  String toTimeOnlyFormat() {
    return DateFormat('HH:mm').format(this);
  }

  /// Format date and time for API requests
  /// Returns ISO 8601 format: "yyyy-MM-ddTHH:mm:ss.SSSZ"
  String toApiFormat() {
    return toIso8601String();
  }

  /// Format date for user-friendly display
  /// Returns format: "EEEE, MMM dd, yyyy" (e.g., "Monday, Jan 15, 2024")
  String toUserFriendlyFormat() {
    return DateFormat('EEEE, MMM dd, yyyy').format(this);
  }

  /// Format date and time with seconds for detailed display
  /// Returns format: "MMM dd, yyyy - HH:mm:ss" (e.g., "Jan 15, 2024 - 14:30:45")
  String toDetailedFormat() {
    return DateFormat('MMM dd, yyyy - HH:mm:ss').format(this);
  }

  /// Format date and time in Japanese style with day of week
  /// Returns format: "yyyy/MM/dd(aaa) HH:mm" (e.g., "2024/01/15(月) 14:30")
  String toDateAndTimeDetailedJapaneseFormat({
    bool withSeconds = false,
  }) {
    return DateFormat(
      'yyyy/MM/dd(E) ${withSeconds ? 'HH:mm:ss' : 'HH:mm'}',
      'ja_JP',
    ).format(this);
  }

  /// Format date and time in Japanese style with day of week
  /// Returns format: "MM/dd(E) HH:mm" (e.g., "01/15(月) 14:30")
  String toTimeDetailedJapaneseFormat() {
    return DateFormat('MM/dd(E) HH:mm', 'ja_JP').format(this);
  }

  /// Format duration in Japanese style for time display
  /// Returns format: "Xh XXm XXs" (e.g., "1h 23m 45s")
  String toDurationFormat() {
    final now = DateTime.now();
    final duration = now.difference(this);

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    return '${hours}h ${minutes.toString().padLeft(2, '0')}m ${seconds.toString().padLeft(2, '0')}s';
  }
}
