import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:glidic_app/core/common/annotation/annotation.dart';

/// Supported deployment environments
enum Environment {
  development('development', '.env.development'),
  staging('staging', '.env.staging'),
  production('production', '.env.production');

  const Environment(this.name, this.fileName);

  final String name;
  final String fileName;
}

/// Environment configuration service
/// Provides a centralized way to access environment variables
/// loaded from .env files using flutter_dotenv package
///
/// Supports multiple environments: development, staging, production
/// Automatically detects environment based on build mode and configuration
class EnvConfig {
  // Private constructor to prevent instantiation
  EnvConfig._();

  static Environment? _currentEnvironment;
  static bool _isInitialized = false;

  /// Get the current environment
  static Environment get currentEnvironment {
    if (!_isInitialized) {
      throw StateError(
        'EnvConfig not initialized. Call EnvConfig.init() first.',
      );
    }
    return _currentEnvironment ?? Environment.development;
  }

  /// Check if the environment configuration is initialized
  static bool get isInitialized => _isInitialized;

  /// Initialize environment configuration
  /// This should be called before the app starts
  ///
  /// [environment] - Specific environment to load. If null, auto-detects based on build mode
  /// [fallbackFileName] - Fallback .env file if environment-specific file is not found
  static Future<void> init({
    Environment? environment,
    String fallbackFileName = '.env',
  }) async {
    try {
      // Determine which environment to use
      _currentEnvironment = environment ?? _detectEnvironment();

      // Try to load environment-specific file first
      try {
        await dotenv.load(fileName: _currentEnvironment!.fileName);
        if (kDebugMode) {
          print(
            '✅ Loaded environment config: ${_currentEnvironment!.fileName}',
          );
        }
      } catch (e) {
        // Fallback to default .env file if environment-specific file doesn't exist
        if (kDebugMode) {
          print(
            '⚠️  Environment file ${_currentEnvironment!.fileName} not found, falling back to $fallbackFileName',
          );
        }
        await dotenv.load(fileName: fallbackFileName);
      }

      // Validate required environment variables
      _validateRequiredVariables();

      _isInitialized = true;

      if (kDebugMode) {
        print('🚀 Environment initialized: ${_currentEnvironment!.name}');
        print('📍 Base URL: $baseUrl');
        print('🔧 Debug Mode: $isDebugMode');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize environment config: $e');
      }
      rethrow;
    }
  }

  /// Auto-detect environment based on build configuration
  static Environment _detectEnvironment() {
    // First, try to detect from build flavor (Android) or scheme (iOS)
    // This is set via --dart-define during build
    const flavorEnvironment = String.fromEnvironment('ENVIRONMENT');
    if (flavorEnvironment.isNotEmpty) {
      switch (flavorEnvironment.toLowerCase()) {
        case 'staging':
          return Environment.staging;
        case 'production':
          return Environment.production;
        case 'development':
        default:
          return Environment.development;
      }
    }

    // Fallback: Check if environment is specified in existing .env file
    try {
      // Try to load default .env to check ENVIRONMENT variable
      final envString = dotenv.env['ENVIRONMENT']?.toLowerCase();
      switch (envString) {
        case 'staging':
          return Environment.staging;
        case 'production':
          return Environment.production;
        case 'development':
        default:
          return Environment.development;
      }
    } catch (e) {
      // If no .env file exists, default to development in debug mode
      return kDebugMode ? Environment.development : Environment.production;
    }
  }

  /// Validate that all required environment variables are present
  static void _validateRequiredVariables() {
    final requiredVars = <String>[
      'ENVIRONMENT',
      'BASE_URL',
      'API_VERSION',
    ];

    final missingVars = <String>[];
    for (final varName in requiredVars) {
      if (!dotenv.env.containsKey(varName) || dotenv.env[varName]!.isEmpty) {
        missingVars.add(varName);
      }
    }

    if (missingVars.isNotEmpty) {
      throw StateError(
        'Missing required environment variables: ${missingVars.join(', ')}\n'
        'Please check your ${_currentEnvironment!.fileName} file.',
      );
    }
  }

  // Network Configuration
  /// Base URL for the API
  static String get baseUrl =>
      dotenv.env['BASE_URL'] ?? 'https://api.example.com';

  /// API version identifier
  static String get apiVersion => dotenv.env['API_VERSION'] ?? 'v1';

  /// Connection timeout in milliseconds
  @visibleForFutureUse
  static int get connectTimeoutSeconds =>
      int.tryParse(dotenv.env['CONNECT_TIMEOUT_SECONDS'] ?? '30') ?? 30;

  /// Receive timeout in milliseconds
  @visibleForFutureUse
  static int get receiveTimeoutSeconds =>
      int.tryParse(dotenv.env['RECEIVE_TIMEOUT_SECONDS'] ?? '30') ?? 30;

  /// Send timeout in milliseconds
  @visibleForFutureUse
  static int get sendTimeoutSeconds =>
      int.tryParse(dotenv.env['SEND_TIMEOUT_SECONDS'] ?? '30') ?? 30;

  // Third-party API Keys
  /// Firebase API key
  @visibleForFutureUse
  static String get firebaseApiKey => dotenv.env['FIREBASE_API_KEY'] ?? '';

  /// Google Maps API key
  @visibleForFutureUse
  static String get googleMapsApiKey => dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';

  /// Analytics tracking ID
  @visibleForFutureUse
  static String get analyticsTrackingId =>
      dotenv.env['ANALYTICS_TRACKING_ID'] ?? '';

  // Google OAuth Configuration
  /// Google OAuth client ID for authentication
  /// Used for Google Calendar integration and other Google services
  static String get googleOAuthClientId => dotenv.env['GOOGLE_OAUTH_CLIENT_ID'] ?? '';

  /// Google OAuth server client ID for Android
  /// This should be the Web application Client ID from Google Cloud Console
  /// Required for Android platform when not using google-services.json
  static String get googleOAuthServerClientId => dotenv.env['GOOGLE_OAUTH_SERVER_CLIENT_ID'] ?? '';

  /// Google OAuth iOS client ID
  /// This is the iOS client ID from Google Cloud Console
  /// Required for iOS platform Google Sign-In integration
  static String get googleOAuthIosClientId => dotenv.env['GOOGLE_OAUTH_IOS_CLIENT_ID'] ?? '';

  // App Configuration
  /// Environment name (development, staging, production)
  static String get environment => dotenv.env['ENVIRONMENT'] ?? 'development';

  /// Enable debug mode
  static bool get isDebugMode =>
      dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true' ||
      currentEnvironment == Environment.development;

  /// Enable logging
  static bool get enableLogging =>
      dotenv.env['ENABLE_LOGGING']?.toLowerCase() == 'true' || isDebugMode;

  // Feature Flags
  /// Enable analytics
  static bool get enableAnalytics =>
      dotenv.env['ENABLE_ANALYTICS']?.toLowerCase() == 'true';

  /// Enable feature X (example feature flag)
  static bool get enableFeatureX =>
      dotenv.env['ENABLE_FEATURE_X']?.toLowerCase() == 'true';

  // Environment-specific helpers
  /// Check if running in development environment
  static bool get isDevelopment =>
      currentEnvironment == Environment.development;

  /// Check if running in staging environment
  static bool get isStaging => currentEnvironment == Environment.staging;

  /// Check if running in production environment
  static bool get isProduction => currentEnvironment == Environment.production;

  /// Get a custom environment variable
  /// Returns the value if found, otherwise returns the default value
  static String getCustom(String key, {String defaultValue = ''}) {
    return dotenv.env[key] ?? defaultValue;
  }

  /// Get a custom environment variable as boolean
  static bool getCustomBool(String key, {bool defaultValue = false}) {
    final value = dotenv.env[key]?.toLowerCase();
    if (value == null) return defaultValue;
    return value == 'true' || value == '1' || value == 'yes';
  }

  /// Get a custom environment variable as integer
  static int getCustomInt(String key, {int defaultValue = 0}) {
    return int.tryParse(dotenv.env[key] ?? '') ?? defaultValue;
  }

  /// Get a custom environment variable as double
  static double getCustomDouble(String key, {double defaultValue = 0.0}) {
    return double.tryParse(dotenv.env[key] ?? '') ?? defaultValue;
  }

  /// Check if an environment variable exists
  static bool hasKey(String key) {
    return dotenv.env.containsKey(key);
  }

  /// Get all environment variables (for debugging purposes)
  /// Should only be used in development mode
  static Map<String, String> getAllEnvVars() {
    if (!isDebugMode) {
      throw Exception('getAllEnvVars() can only be called in debug mode');
    }
    return Map<String, String>.from(dotenv.env);
  }

  /// Get environment summary for debugging
  static Map<String, dynamic> getEnvironmentSummary() {
    if (!isDebugMode) {
      throw Exception(
        'getEnvironmentSummary() can only be called in debug mode',
      );
    }

    return {
      'environment': currentEnvironment.name,
      'fileName': currentEnvironment.fileName,
      'baseUrl': baseUrl,
      'apiVersion': apiVersion,
      'isDebugMode': isDebugMode,
      'enableLogging': enableLogging,
      'enableAnalytics': enableAnalytics,
      'isDevelopment': isDevelopment,
      'isStaging': isStaging,
      'isProduction': isProduction,
    };
  }
}
