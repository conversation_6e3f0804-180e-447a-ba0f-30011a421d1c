import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:glidic_app/core/common/config/env_config.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/data/datasources/database/app_database.dart';
import 'package:glidic_app/core/data/datasources/network/interceptors/auth_interceptor.dart';
import 'package:glidic_app/core/data/datasources/network/interceptors/error_interceptor.dart';
import 'package:glidic_app/core/data/datasources/network/network_info.dart';
import 'package:glidic_app/core/data/datasources/network/pretty_dio_logger.dart';
import 'package:glidic_app/core/data/datasources/storage/interfaces/preferences_service.dart';
import 'package:glidic_app/core/data/datasources/storage/interfaces/secure_storage_service.dart';
import 'package:glidic_app/core/data/datasources/storage/preferences_service_impl.dart';
import 'package:glidic_app/core/data/datasources/storage/secure_storage_service_impl.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_local_datasource.dart';
import 'package:glidic_app/features/auth/di/auth_di.dart';
import 'package:glidic_app/features/calendar_event/di/calendar_event_di.dart';
import 'package:glidic_app/features/digital_twin/di/digital_twin_di.dart';
import 'package:glidic_app/features/home/<USER>/home_di.dart';
import 'package:glidic_app/features/recording/di/recording_di.dart';

/// Service Locator using GetIt for dependency injection
/// This follows the dependency inversion principle by providing
/// a centralized way to register and resolve dependencies
final GetIt sl = GetIt.instance;

/// Initialize all dependencies
/// This method should be called in main() before running the app
Future<void> initializeDependencies() async {
  // Initialize environment configuration first
  await _initEnvironment();

  // Initialize core dependencies
  await _initCore();

  // Initialize feature-specific dependencies
  await _initFeatures();

  // Configure network interceptors after all dependencies are ready
  await _configureNetworkInterceptors();
}

/// Initialize environment configuration
/// Loads environment variables from .env file
Future<void> _initEnvironment() async {
  await EnvConfig.init();
}

/// Initialize core dependencies (network, storage, etc.)
Future<void> _initCore() async {
  // Initialize storage services first
  await _initStorage();

  // Dio instance for HTTP requests with proper configuration
  sl.registerLazySingleton<Dio>(() {
    final dio = Dio();

    // Configure base options
    dio.options = BaseOptions(
      baseUrl: NetworkConstants.baseUrl,
      connectTimeout: NetworkConstants.connectTimeout,
      receiveTimeout: NetworkConstants.receiveTimeout,
      sendTimeout: NetworkConstants.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // TODO: Add authentication interceptor (will be registered after auth feature is initialized)
    // dio.interceptors.add(AuthInterceptor(sl()));

    // Add error handling interceptor
    dio.interceptors.add(ErrorInterceptor());

    // Add beautiful logging interceptor
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseHeader: false,
        responseBody: true,
        error: true,
        compact: true,
      ),
    );

    return dio;
  });

  // Network Info
  sl.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(),
  );
}

/// Initialize storage dependencies
Future<void> _initStorage() async {
  // Database
  sl.registerLazySingleton<AppDatabase>(
    () => AppDatabase(),
  );

  // Secure Storage Service
  sl.registerLazySingleton<SecureStorageService>(
    () => SecureStorageServiceImpl(),
  );

  // Preferences Service
  sl.registerLazySingleton<PreferencesService>(
    () => PreferencesServiceImpl(),
  );
}

/// Initialize feature-specific dependencies
/// Each feature has its own DI configuration
Future<void> _initFeatures() async {
  // Initialize Auth feature dependencies
  await AuthDI.init();

  // Initialize Home feature dependencies
  await HomeDI.init();

  // Initialize Calendar Event feature dependencies
  await CalendarEventDI.init();
  
  // Initialize Recording feature dependencies
  await RecordingDI.init();

  // Initialize Digital Twin feature dependencies
  await DigitalTwinDI.init();
}

/// Reset all dependencies (useful for testing)
Future<void> resetDependencies() async {
  await sl.reset();
}

/// Configure network interceptors after all dependencies are initialized
Future<void> _configureNetworkInterceptors() async {
  final dio = sl<Dio>();

  // Add authentication interceptor with proper dependency injection
  dio.interceptors.add(AuthInterceptor(sl<AuthLocalDataSource>()));
}

/// Check if a dependency is registered
bool isDependencyRegistered<T extends Object>() {
  return sl.isRegistered<T>();
}
