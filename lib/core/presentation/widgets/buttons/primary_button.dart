import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

const _kIconSize = 18.0;

/// Primary button widget - fundamental UI component
/// This is a core widget that provides consistent primary button styling
/// across the entire application
class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
    this.backgroundColor,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final IconData? icon;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? Dimensions.buttonHeight,
      child: ElevatedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              backgroundColor ?? ColorConstants.primaryButtonBackground,
          foregroundColor: ColorConstants.primaryButtonText,
          disabledBackgroundColor: ColorConstants.buttonDisabled,
          disabledForegroundColor: ColorConstants.textDisabled,
          elevation: 2,
          shadowColor: Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              Dimensions.primaryButtonBorderRadius,
            ),
          ),
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    ColorConstants.primaryButtonText,
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    Icon(icon, size: _kIconSize),
                    const SizedBox(width: Dimensions.gapMd),
                  ],
                  Flexible(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        text,
                        style: TextStyleConstants.body.w700.onPrimaryColor,
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
