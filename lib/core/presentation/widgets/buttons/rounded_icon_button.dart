import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/dimensions.dart';

const _kRoundedIconButtonSize = 34.0;
const _kRoundedIconButtonBackgroundColor = Color(0xFFE6E9EC);

/// Rounded icon button widget - fundamental UI component
class RoundedIconButton extends StatelessWidget {
  const RoundedIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.backgroundColor = _kRoundedIconButtonBackgroundColor,
  });

  final Widget icon;
  final VoidCallback onPressed;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: _kRoundedIconButtonSize,
        height: _kRoundedIconButtonSize,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(Dimensions.radiusLg),
        ),
        alignment: Alignment.center,
        child: icon,
      ),
    );
  }
}
