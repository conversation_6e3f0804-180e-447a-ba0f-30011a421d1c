import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/color_constants.dart';

const _kCircleIconButtonSize = 34.0;

class CircleIconButton extends StatelessWidget {
  const CircleIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.backgroundColor = ColorConstants.onSecondary,
  });

  final Widget icon;
  final VoidCallback onPressed;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: _kCircleIconButtonSize,
        height: _kCircleIconButtonSize,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: icon,
      ),
    );
  }
}
