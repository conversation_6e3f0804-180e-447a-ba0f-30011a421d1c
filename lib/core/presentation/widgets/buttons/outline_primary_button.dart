import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

/// Outline primary button widget - fundamental UI component
/// This is a core widget that provides consistent outline primary button styling
/// across the entire application with transparent background and black border
class OutlinePrimaryButton extends StatelessWidget {
  const OutlinePrimaryButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
    this.icon,
    this.backgroundColor,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;
  final IconData? icon;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? Dimensions.buttonHeight,
      child: OutlinedButton(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: OutlinedButton.styleFrom(
          backgroundColor:
              backgroundColor ?? ColorConstants.outlinePrimaryButtonBackground,
          foregroundColor: ColorConstants.outlinePrimaryButtonText,
          disabledForegroundColor:
              ColorConstants.outlinePrimaryButtonDisabledText,
          elevation: 0,
          side: BorderSide(
            color: isEnabled
                ? ColorConstants.outlinePrimaryButtonBorder
                : ColorConstants.outlinePrimaryButtonDisabledBorder,
            width: Dimensions.outlineButtonBorderWidth,
          ),
          shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.circular(Dimensions.primaryButtonBorderRadius),
          ),
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    ColorConstants.outlinePrimaryButtonText,
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    Icon(icon, size: Dimensions.iconButton),
                    const SizedBox(width: Dimensions.gapMd),
                  ],
                  Flexible(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        text,
                        style: TextStyleConstants.body.w700.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
