import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

class HorizontalProgress extends StatelessWidget {
  const HorizontalProgress({super.key, required this.percentage});

  final double percentage;

  @override
  Widget build(BuildContext context) {

    return LayoutBuilder(
      builder: (context, constraints) {
        final double percentageWidget = constraints.maxWidth * percentage / 100;
        return Container(
          width: constraints.maxWidth,
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(Dimensions.radiusXS),
            color: ColorConstants.onPrimaryContainer,
          ),
          child: Container(
            width: percentageWidget,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Dimensions.radiusXS),
              color: ColorConstants.onSecondary,
            ),
          ),
        );
      },
    );
  }
}
