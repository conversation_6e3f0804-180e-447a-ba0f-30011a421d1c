import 'package:flutter/cupertino.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

const double _kTextSize = 15;
const Color _kTextColor = Color(0xFF707070);

class GDRefreshIndicator extends StatelessWidget {
  const GDRefreshIndicator({
    super.key,
    this.radius = Dimensions.radiusMd,
    this.text,
    this.percentSize = 1,
    this.isAnimating = true,
  });

  final double radius;
  final String? text;
  final double percentSize;
  final bool isAnimating;

  Widget get _cupertinoIndicator {
    if (isAnimating) {
      return CupertinoActivityIndicator(
        radius: radius * percentSize,
      );
    }

    return CupertinoActivityIndicator.partiallyRevealed(
      radius: radius,
      progress: percentSize,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (text == null) {
      return Center(child: _cupertinoIndicator);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _cupertinoIndicator,
        SizedBox(width: Dimensions.gapMd * percentSize),
        Text(
          text!,
          style: TextStyleConstants.body.copyWith(
            color: _kTextColor,
            fontSize: _kTextSize * percentSize,
          ),
        ),
      ],
    );
  }
}
