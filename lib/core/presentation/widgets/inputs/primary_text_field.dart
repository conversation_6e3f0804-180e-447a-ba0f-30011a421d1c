import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

/// Primary text field widget - fundamental UI component
/// This is a core widget that provides consistent text input styling
/// across the entire application
class PrimaryTextField extends StatelessWidget {
  const PrimaryTextField({
    super.key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.focusNode,
  });

  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool enabled;
  final int maxLines;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final String? Function(String?)? validator;
  final FocusNode? focusNode;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null) ...[
          Text(
            labelText!,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: ColorConstants.textSecondary,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius:
                BorderRadius.circular(Dimensions.textFieldBorderRadius),
            boxShadow: [
              BoxShadow(
                color: ColorConstants.textFieldShadow.withValues(alpha: 0.24),
                offset: const Offset(0, 1),
                blurRadius: 2,
                spreadRadius: 0,
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            focusNode: focusNode,
            obscureText: obscureText,
            enabled: enabled,
            maxLines: maxLines,
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            onChanged: onChanged,
            onFieldSubmitted: onSubmitted,
            validator: validator,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: ColorConstants.textFieldText,
              letterSpacing: -0.14, // -1% of 14px
            ),
            decoration: InputDecoration(
              hintText: hintText,
              errorText: errorText,
              prefixIcon: prefixIcon != null
                  ? IconTheme(
                      data: const IconThemeData(
                        color: ColorConstants.textFieldIcon,
                      ),
                      child: prefixIcon!,
                    )
                  : null,
              suffixIcon: suffixIcon != null
                  ? IconTheme(
                      data: const IconThemeData(
                        color: ColorConstants.textFieldIcon,
                      ),
                      child: suffixIcon!,
                    )
                  : null,
              filled: true,
              fillColor: enabled
                  ? ColorConstants.textFieldBackground
                  : ColorConstants.surfaceVariant,
              border: OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(Dimensions.textFieldBorderRadius),
                borderSide:
                    const BorderSide(color: ColorConstants.textFieldBorder),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(Dimensions.textFieldBorderRadius),
                borderSide:
                    const BorderSide(color: ColorConstants.textFieldBorder),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(Dimensions.textFieldBorderRadius),
                borderSide: const BorderSide(
                  color: ColorConstants.inputFocused,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(Dimensions.textFieldBorderRadius),
                borderSide: const BorderSide(
                  color: ColorConstants.inputError,
                  width: 2,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(Dimensions.textFieldBorderRadius),
                borderSide: const BorderSide(
                  color: ColorConstants.inputError,
                  width: 2,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius:
                    BorderRadius.circular(Dimensions.textFieldBorderRadius),
                borderSide: const BorderSide(color: ColorConstants.borderColor),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: Dimensions.textFieldHorizontalPadding,
                vertical: Dimensions.textFieldVerticalPadding,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
