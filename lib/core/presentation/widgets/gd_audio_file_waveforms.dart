// Import necessary packages
import 'dart:async';
import 'dart:math' as math;

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';

// GDAudioFileWaveforms widget, adapted for fit width, gestures, and moving seekline
class GDAudioFileWaveforms extends StatefulWidget {
  final Size size;
  final PlayerController playerController;
  final List<double> waveformData;
  final bool continuousWaveform;
  final PlayerWaveStyle playerWaveStyle;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BoxDecoration? decoration;
  final Color? backgroundColor;
  final Duration animationDuration;
  final Curve animationCurve;
  final Clip clipBehavior;
  final bool enableSeekGesture;
  final Function(DragStartDetails)? onDragStart;
  final Function(DragEndDetails)? onDragEnd;
  final Function(DragUpdateDetails)? dragUpdateDetails;
  final Function(TapUpDetails)? tapUpUpdateDetails;

  const GDAudioFileWaveforms({
    super.key,
    required this.size,
    required this.playerController,
    this.waveformData = const [],
    this.continuousWaveform = true,
    this.playerWaveStyle = const PlayerWaveStyle(),
    this.padding,
    this.margin,
    this.decoration,
    this.backgroundColor,
    this.animationDuration = const Duration(milliseconds: 500),
    this.animationCurve = Curves.easeIn,
    this.clipBehavior = Clip.none,
    this.enableSeekGesture = true,
    this.onDragStart,
    this.onDragEnd,
    this.dragUpdateDetails,
    this.tapUpUpdateDetails,
  });

  @override
  State<GDAudioFileWaveforms> createState() => _GDAudioFileWaveformsState();
}

class _GDAudioFileWaveformsState extends State<GDAudioFileWaveforms> with SingleTickerProviderStateMixin {
  late AnimationController _growingWaveController;
  late Animation<double> _growAnimation;

  double _growAnimationProgress = 0.0;
  final ValueNotifier<int> _seekProgress = ValueNotifier(0);

  late StreamSubscription<int> onCurrentDurationSubscription;
  late StreamSubscription<void> onCompletionSubscription;
  StreamSubscription<List<double>>? onCurrentExtractedWaveformData;

  double get spacing => widget.playerWaveStyle.spacing;

  double _audioProgress = 0.0;
  double _cachedAudioProgress = 0.0;

  bool _isScrolled = false;
  double scrollScale = 1.0;
  double _proportion = 0.0;

  final List<double> _waveformData = [];

  @override
  void initState() {
    super.initState();
    _growingWaveController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _growAnimation = CurvedAnimation(
      parent: _growingWaveController,
      curve: widget.animationCurve,
    );

    _growingWaveController
      ..forward()
      ..addListener(_updateGrowAnimationProgress);

    onCurrentDurationSubscription = widget.playerController.onCurrentDurationChanged.listen((event) {
      _seekProgress.value = event;
      _updatePlayerPercent();
    });

    onCompletionSubscription = widget.playerController.onCompletion.listen((event) {
      _seekProgress.value = widget.playerController.maxDuration;
      _updatePlayerPercent();
    });

    if (widget.waveformData.isNotEmpty) {
      _addWaveformData(widget.waveformData);
    } else {
      if (widget.playerController.waveformData.isNotEmpty) {
        _addWaveformData(widget.playerController.waveformData);
      }
      if (!widget.continuousWaveform) {
        widget.playerController.addListener(_addWaveformDataFromController);
      } else {
        onCurrentExtractedWaveformData = widget.playerController.onCurrentExtractedWaveformData.listen(_addWaveformData);
      }
    }
  }

  @override
  void dispose() {
    onCurrentDurationSubscription.cancel();
    onCurrentExtractedWaveformData?.cancel();
    onCompletionSubscription.cancel();
    widget.playerController.removeListener(_addWaveformDataFromController);
    _growingWaveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      margin: widget.margin,
      decoration: widget.decoration,
      clipBehavior: widget.clipBehavior,
      child: GestureDetector(
        onHorizontalDragUpdate: widget.enableSeekGesture ? _handleDragGestures : null,
        onTapUp: widget.enableSeekGesture ? _handleScrubberSeekStart : null,
        onHorizontalDragStart: widget.enableSeekGesture ? _handleHorizontalDragStart : null,
        onHorizontalDragEnd: widget.enableSeekGesture ? _handleOnDragEnd : null,
        child: RepaintBoundary(
          child: ValueListenableBuilder<int>(
            builder: (_, __, ___) {
              return CustomPaint(
                isComplex: true,
                painter: GDPlayerWavePainter(
                  playerWaveStyle: widget.playerWaveStyle,
                  waveformData: _waveformData,
                  animValue: _growAnimationProgress,
                  audioProgress: _audioProgress,
                  callPushback: !_isScrolled,
                  pushBack: _pushBackWave,
                  scrollScale: scrollScale,
                  cachedAudioProgress: _cachedAudioProgress,
                ),
                size: widget.size,
              );
            },
            valueListenable: _seekProgress,
          ),
        ),
      ),
    );
  }

  void _addWaveformDataFromController() => _addWaveformData(widget.playerController.waveformData);

  void _updateGrowAnimationProgress() {
    if (mounted) {
      setState(() {
        _growAnimationProgress = _growAnimation.value;
      });
    }
  }

  void _handleOnDragEnd(DragEndDetails dragEndDetails) {
    _isScrolled = false;
    scrollScale = 1.0;
    if (mounted) setState(() {});

    widget.playerController.seekTo((widget.playerController.maxDuration * _proportion).toInt());
    widget.onDragEnd?.call(dragEndDetails);
  }

  void _addWaveformData(List<double> data) {
    if (data.isEmpty) return;

    final desiredSamples = widget.playerWaveStyle.getSamplesForWidth(widget.size.width);
    List<double> processedData = [];

    if (data.length == desiredSamples) {
      processedData = List.from(data);
    } else {
      final bucketSize = data.length / desiredSamples;
      for (int b = 0; b < desiredSamples; b++) {
        final start = (b * bucketSize).floor();
        final end = math.min(data.length, ((b + 1) * bucketSize).floor());
        double maxAmp = 0.0;
        for (int j = start; j < end; j++) {
          final amp = data[j].abs();
          if (amp > maxAmp) maxAmp = amp;
        }
        processedData.add(maxAmp);
      }
    }

    _waveformData
      ..clear()
      ..addAll(processedData);

    if (mounted) setState(() {});
  }

  void _handleDragGestures(DragUpdateDetails details) {
    _handleScrubberSeekUpdate(details);
    widget.dragUpdateDetails?.call(details);
  }

  void _handleScrubberSeekUpdate(DragUpdateDetails details) {
    final localPosition = details.localPosition.dx;
    _proportion = localPosition <= 0 ? 0 : localPosition / widget.size.width;
    final seekPosition = widget.playerController.maxDuration * _proportion;
    widget.playerController.seekTo(seekPosition.toInt());
  }

  void _handleScrubberSeekStart(TapUpDetails details) {
    _proportion = details.localPosition.dx / widget.size.width;
    final seekPosition = widget.playerController.maxDuration * _proportion;
    widget.playerController.seekTo(seekPosition.toInt());
    widget.tapUpUpdateDetails?.call(details);
  }

  void _handleHorizontalDragStart(DragStartDetails details) {
    widget.onDragStart?.call(details);
  }

  void _updatePlayerPercent() {
    if (widget.playerController.maxDuration == 0) return;
    _audioProgress = _seekProgress.value / widget.playerController.maxDuration;
    _cachedAudioProgress = _audioProgress;
    if (mounted) setState(() {});
  }

  void _pushBackWave() {
    // For fit width, no pushing back needed
  }
}

const Color _kSeekLineColor = Color(0xFFFD5C5C);
const Color _kFixedWaveColor = Color(0x4C000000);
const Color _kLiveLineColor = Color(0xFF707070);

/// Custom player wave style for playing audio
class GDPlayerWaveStyle extends PlayerWaveStyle {

  const GDPlayerWaveStyle({
     super.fixedWaveColor = _kFixedWaveColor,
    super.liveWaveColor = _kLiveLineColor,
    super.seekLineColor = _kSeekLineColor,
    super.seekLineThickness = 2,
    super.showSeekLine = true,
    super.showBottom = true,
    super.showTop = true,
    super.waveThickness = 0.8,
    super.spacing = 1.2,
  });
}

// GDPlayerWavePainter class, adapted to draw seekline at audio position
class GDPlayerWavePainter extends CustomPainter {
  final List<double> waveformData;
  final double animValue;
  final double audioProgress;
  final VoidCallback pushBack;
  final bool callPushback;
  final double scrollScale;
  final PlayerWaveStyle playerWaveStyle;
  final double cachedAudioProgress;

  GDPlayerWavePainter({
    required this.waveformData,
    required this.animValue,
    required this.audioProgress,
    required this.pushBack,
    required this.callPushback,
    required this.scrollScale,
    required this.playerWaveStyle,
    required this.cachedAudioProgress,
  })  : fixedWavePaint = Paint()
          ..color = playerWaveStyle.fixedWaveColor
          ..strokeWidth = playerWaveStyle.waveThickness
          ..strokeCap = playerWaveStyle.waveCap
          ..shader = playerWaveStyle.fixedWaveGradient,
        liveWavePaint = Paint()
          ..color = playerWaveStyle.liveWaveColor
          ..strokeWidth = playerWaveStyle.waveThickness
          ..strokeCap = playerWaveStyle.waveCap
          ..shader = playerWaveStyle.liveWaveGradient,
        middleLinePaint = Paint()
          ..color = playerWaveStyle.seekLineColor
          ..strokeWidth = playerWaveStyle.seekLineThickness/2;

  Paint fixedWavePaint;
  Paint liveWavePaint;
  Paint middleLinePaint;

  @override
  void paint(Canvas canvas, Size size) {
    _drawWave(size, canvas);
    if (playerWaveStyle.showSeekLine) {
      _drawSeekLine(size, canvas);
    }
  }

  @override
  bool shouldRepaint(GDPlayerWavePainter oldDelegate) => true;

  void _drawSeekLine(Size size, Canvas canvas) {
    final seekPosition = size.width * audioProgress;
    canvas.drawLine(
      Offset(seekPosition, 0),
      Offset(seekPosition, size.height),
      middleLinePaint,
    );
  }

  void _drawWave(Size size, Canvas canvas) {
    final length = waveformData.length;
    if (length == 0) return;

    final halfHeight = size.height * 0.5;

    double effectiveSpacing = 0.0;
    if (length > 1) {
      effectiveSpacing = size.width / (length - 1);
    } else {
      // For single data point, draw at center
      final dx = size.width / 2;
      final waveHeight = (waveformData[0] * animValue) * playerWaveStyle.scaleFactor * scrollScale;
      final bottomDy = halfHeight + (playerWaveStyle.showBottom ? waveHeight : 0);
      final topDy = halfHeight + (playerWaveStyle.showTop ? -waveHeight : 0);
      canvas.drawLine(
        Offset(dx, bottomDy),
        Offset(dx, topDy),
        liveWavePaint, // Assume live for single point
      );
      return;
    }

    final seekPosition = size.width * audioProgress;

    for (int i = 0; i < length; i++) {
      final dx = i * effectiveSpacing;
      final waveHeight = (waveformData[i] * animValue) * playerWaveStyle.scaleFactor * scrollScale;
      final bottomDy = halfHeight + (playerWaveStyle.showBottom ? waveHeight : 0);
      final topDy = halfHeight + (playerWaveStyle.showTop ? -waveHeight : 0);

      // Use live paint if dx < seekPosition
      final paint = (dx < seekPosition) ? liveWavePaint : fixedWavePaint;

      canvas.drawLine(
        Offset(dx, bottomDy),
        Offset(dx, topDy),
        paint,
      );
    }
  }
}


