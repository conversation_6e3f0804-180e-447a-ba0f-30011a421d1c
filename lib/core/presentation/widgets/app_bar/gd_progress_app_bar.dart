import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/core/presentation/widgets/assets/circle_status_icon.dart';
import 'package:glidic_app/core/presentation/widgets/indicators/hozirontal_progress.dart';

const _kCircleStatusIconDiameter = 30.0;
const _kSpacing = Dimensions.gapXS;
const _kSpacingBetweenCircleStatusIcons = Dimensions.gapLg;
const _kPaddingEarBud = 4.0;
const _kHeightProgressBar = 28.0;
const _kHeightHorizontalProgress = 9.0;

/// App bar with progress bar
/// [percentage] is the percentage of the progress bar (0-100)
/// [remaining] is the remaining quantity or time
/// [total] is the total quantity or time
class GDProgressAppBar extends StatelessWidget {
  const GDProgressAppBar({
    super.key,
    required this.percentage,
    required this.remaining,
    required this.total,
    this.icEarbudConnected = true,
    this.icNoteDeviceConnected = false,
  });

  final double percentage;
  final int remaining;
  final int total;
  final bool icEarbudConnected;
  final bool icNoteDeviceConnected;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: Dimensions.defaultPadding,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Row(
            children: [
              SvgPicture.asset(
                PathConstants.hamburgerButtonIcon,
              ),
            ],
          ),
          Row(
            children: [
              // width of the circle status icons to center the progress bar
              const SizedBox(
                width: _kSpacing +
                    _kSpacingBetweenCircleStatusIcons +
                    _kCircleStatusIconDiameter *
                        2, // 2 is number of circle status icons
              ),
              Expanded(
                child: Container(
                  height: _kHeightProgressBar,
                  padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.smallPadding,
                  ),
                  decoration: BoxDecoration(
                    color: ColorConstants.primaryContainer,
                    borderRadius: BorderRadius.circular(Dimensions.circleRadius),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: _kHeightHorizontalProgress,
                          child: HorizontalProgress(percentage: percentage),
                        ),
                      ),
                      const SizedBox(width: Dimensions.gapSm),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: l10n.remaining,
                              style: TextStyleConstants.labelExtraSmall,
                            ),
                            TextSpan(
                              text: remaining.toString(),
                              style: TextStyleConstants.labelLarge.s12,
                            ),
                            TextSpan(
                              text: '/${total.toString()}${l10n.minute}',
                              style: TextStyleConstants.labelExtraSmall,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: _kSpacingBetweenCircleStatusIcons),
              CircleStatusIcon(
                diameter: _kCircleStatusIconDiameter,
                isActive: icEarbudConnected,
                child: Padding(
                  padding: const EdgeInsets.all(_kPaddingEarBud),
                  child: Image.asset(
                    PathConstants.earbudImage,
                    
                  ),
                ),
              ),
              const SizedBox(width: Dimensions.gapXS),
              CircleStatusIcon(
                diameter: _kCircleStatusIconDiameter,
                isActive: icNoteDeviceConnected,
                child: Padding(
                  padding: const EdgeInsets.all(Dimensions.smallPadding),
                  child: Image.asset(
                    PathConstants.noteDeviceImage,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
