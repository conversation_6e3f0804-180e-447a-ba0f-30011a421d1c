import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A reusable widget for displaying SVG icons with consistent styling
/// 
/// This widget provides a convenient way to display SVG icons from assets
/// with customizable size, color, and semantic labels for accessibility.
/// 
/// Example usage:
/// ```dart
/// AssetIcon(
///   iconPath: PathConstants.homeIcon,
///   size: 24.0,
///   color: Colors.blue,
/// )
/// ```
class AssetIcon extends StatelessWidget {
  /// Path to the SVG icon asset
  final String iconPath;
  
  /// Size of the icon (width and height)
  final double size;
  
  /// Color to apply to the icon (optional)
  final Color? color;
  
  /// Semantic label for accessibility
  final String? semanticLabel;
  
  /// Fit behavior for the SVG
  final BoxFit fit;
  
  /// Alignment of the SVG within its bounds
  final Alignment alignment;

  const AssetIcon({
    super.key,
    required this.iconPath,
    this.size = 24.0,
    this.color,
    this.semanticLabel,
    this.fit = BoxFit.contain,
    this.alignment = Alignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      iconPath,
      width: size,
      height: size,
      colorFilter: color != null 
          ? ColorFilter.mode(color!, BlendMode.srcIn)
          : null,
      fit: fit,
      alignment: alignment,
      semanticsLabel: semanticLabel,
    );
  }
}

/// A specialized icon button that uses SVG assets
/// 
/// This widget combines an AssetIcon with IconButton functionality
/// for consistent icon buttons throughout the app.
/// 
/// Example usage:
/// ```dart
/// AssetIconButton(
///   iconPath: PathConstants.settingsIcon,
///   onPressed: () => Navigator.pushNamed(context, '/settings'),
///   tooltip: 'Settings',
/// )
/// ```
class AssetIconButton extends StatelessWidget {
  /// Path to the SVG icon asset
  final String iconPath;
  
  /// Callback when the button is pressed
  final VoidCallback? onPressed;
  
  /// Size of the icon
  final double iconSize;
  
  /// Color of the icon
  final Color? iconColor;
  
  /// Tooltip text for the button
  final String? tooltip;
  
  /// Padding around the icon
  final EdgeInsetsGeometry? padding;
  
  /// Constraints for the button
  final BoxConstraints? constraints;

  const AssetIconButton({
    super.key,
    required this.iconPath,
    required this.onPressed,
    this.iconSize = 24.0,
    this.iconColor,
    this.tooltip,
    this.padding,
    this.constraints,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      tooltip: tooltip,
      padding: padding ?? const EdgeInsets.all(8.0),
      constraints: constraints,
      icon: AssetIcon(
        iconPath: iconPath,
        size: iconSize,
        color: iconColor ?? Theme.of(context).iconTheme.color,
      ),
    );
  }
}
