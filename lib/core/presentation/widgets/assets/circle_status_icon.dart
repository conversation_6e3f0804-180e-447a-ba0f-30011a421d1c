import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

const _kIconStatusSize = 5.0;
const _kColorActive = Color(0xFF20BF7A);
const _kColorInactive = Color(0xFFFD5C5C);

class CircleStatusIcon extends StatelessWidget {
  const CircleStatusIcon({
    super.key,
    this.diameter = Dimensions.iconLg,
    this.child,
    this.isActive = false,
  });

  final double diameter;
  final Widget? child;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Container(
          width: diameter,
          height: diameter,
          decoration: const BoxDecoration(
            color: ColorConstants.onSecondary,
            shape: BoxShape.circle,
          ),
          child: Center(child: child),
        ),
        Container(
          width: _kIconStatusSize + Dimensions.borderSm * 2,
          height: _kIconStatusSize + Dimensions.borderSm * 2,
          decoration: BoxDecoration(
            color: isActive ? _kColorActive : _kColorInactive,
            shape: BoxShape.circle,
            border: Border.all(
              color: ColorConstants.onPrimary,
              width: Dimensions.borderSm,
            ),
          ),
        ),
      ],
    );
  }
}
