import 'package:auto_route/auto_route.dart';
import 'package:glidic_app/core/presentation/router/app_router.gr.dart';
import 'package:glidic_app/core/presentation/router/route_paths.dart';

/// Main application router configuration
/// Uses go_router for declarative routing with type-safe navigation
@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  AppRouter({super.navigatorKey});

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: LoginRoute.page,
          path: RoutePaths.login,
        ),
        AutoRoute(
          page: HomeRoute.page,
          path: RoutePaths.home,
          initial: true,
        ),
        AutoRoute(
          page: CalendarEventRoute.page,
          path: RoutePaths.calendarEvent,
        ),
        AutoRoute(
          page: CalendarEventsListRoute.page,
          path: RoutePaths.calendarEventsList,
        ),
        AutoRoute(
          page: GoogleCalendarEventRoute.page,
          path: RoutePaths.googleCalendarEvent,
        ),
        AutoRoute(
          page: GoogleCalendarEventsListRoute.page,
          path: RoutePaths.googleCalendarEventsList,
        ),
        AutoRoute(
          page: RecordingRoute.page,
          path: RoutePaths.recording,
        ),
        AutoRoute(
          page: RecordsListRoute.page,
          path: RoutePaths.recordsList,
        ),
        AutoRoute(
          page: AudioAnalysisRoute.page,
          path: RoutePaths.audioAnalysis,
        ),
      ];
}
