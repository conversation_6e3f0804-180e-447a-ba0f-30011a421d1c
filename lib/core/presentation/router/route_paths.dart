/// Centralized route names for type-safe navigation
/// This ensures consistency and prevents typos in route definitions
class RoutePaths {
  // Private constructor to prevent instantiation
  RoutePaths._();

  // Auth routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';

  // Home routes
  static const String home = '/home';

  // Calendar routes
  static const String calendarEvent = '/calendar-event';
  static const String calendarEventsList = '/calendar-events-list';
  static const String googleCalendarEvent = '/google-calendar-event';
  static const String googleCalendarEventsList = '/google-calendar-events-list';

  // Recording routes
  static const String recording = '/recording';
  static const String recordsList = '/records_list';

  // Profile routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';

  // Settings routes
  static const String settings = '/settings';

  // Error routes
  static const String notFound = '/not-found';

  // Transcription routes
  static const String audioAnalysis = '/transcription-audio';
}
