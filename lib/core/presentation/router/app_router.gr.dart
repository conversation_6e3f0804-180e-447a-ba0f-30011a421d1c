// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i10;
import 'package:flutter/material.dart' as _i11;
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_screen.dart'
    as _i7;
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_screen.dart'
    as _i2;
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_events_list_screen/calendar_events_list_screen.dart'
    as _i3;
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_screen.dart'
    as _i4;
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_events_list_screen/google_calendar_events_list_screen.dart'
    as _i5;
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/audio_analysis_screen.dart'
    as _i1;
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_screen.dart'
    as _i6;
import 'package:glidic_app/features/recording/presentation/screens/recording/recording_screen.dart'
    as _i8;
import 'package:glidic_app/features/recording/presentation/screens/records_list/records_list_screen.dart'
    as _i9;

/// generated route for
/// [_i1.AudioAnalysisScreen]
class AudioAnalysisRoute extends _i10.PageRouteInfo<AudioAnalysisRouteArgs> {
  AudioAnalysisRoute({
    _i11.Key? key,
    required String audioId,
    List<_i10.PageRouteInfo>? children,
  }) : super(
          AudioAnalysisRoute.name,
          args: AudioAnalysisRouteArgs(key: key, audioId: audioId),
          initialChildren: children,
        );

  static const String name = 'AudioAnalysisRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<AudioAnalysisRouteArgs>();
      return _i10.WrappedRoute(
        child: _i1.AudioAnalysisScreen(key: args.key, audioId: args.audioId),
      );
    },
  );
}

class AudioAnalysisRouteArgs {
  const AudioAnalysisRouteArgs({this.key, required this.audioId});

  final _i11.Key? key;

  final String audioId;

  @override
  String toString() {
    return 'AudioAnalysisRouteArgs{key: $key, audioId: $audioId}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AudioAnalysisRouteArgs) return false;
    return key == other.key && audioId == other.audioId;
  }

  @override
  int get hashCode => key.hashCode ^ audioId.hashCode;
}

/// generated route for
/// [_i2.CalendarEventScreen]
class CalendarEventRoute extends _i10.PageRouteInfo<void> {
  const CalendarEventRoute({List<_i10.PageRouteInfo>? children})
      : super(CalendarEventRoute.name, initialChildren: children);

  static const String name = 'CalendarEventRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i2.CalendarEventScreen());
    },
  );
}

/// generated route for
/// [_i3.CalendarEventsListScreen]
class CalendarEventsListRoute extends _i10.PageRouteInfo<void> {
  const CalendarEventsListRoute({List<_i10.PageRouteInfo>? children})
      : super(CalendarEventsListRoute.name, initialChildren: children);

  static const String name = 'CalendarEventsListRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i3.CalendarEventsListScreen());
    },
  );
}

/// generated route for
/// [_i4.GoogleCalendarEventScreen]
class GoogleCalendarEventRoute extends _i10.PageRouteInfo<void> {
  const GoogleCalendarEventRoute({List<_i10.PageRouteInfo>? children})
      : super(GoogleCalendarEventRoute.name, initialChildren: children);

  static const String name = 'GoogleCalendarEventRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i4.GoogleCalendarEventScreen());
    },
  );
}

/// generated route for
/// [_i5.GoogleCalendarEventsListScreen]
class GoogleCalendarEventsListRoute extends _i10.PageRouteInfo<void> {
  const GoogleCalendarEventsListRoute({List<_i10.PageRouteInfo>? children})
      : super(GoogleCalendarEventsListRoute.name, initialChildren: children);

  static const String name = 'GoogleCalendarEventsListRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(
        child: const _i5.GoogleCalendarEventsListScreen(),
      );
    },
  );
}

/// generated route for
/// [_i6.HomeScreen]
class HomeRoute extends _i10.PageRouteInfo<void> {
  const HomeRoute({List<_i10.PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i6.HomeScreen());
    },
  );
}

/// generated route for
/// [_i7.LoginScreen]
class LoginRoute extends _i10.PageRouteInfo<void> {
  const LoginRoute({List<_i10.PageRouteInfo>? children})
      : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i7.LoginScreen());
    },
  );
}

/// generated route for
/// [_i8.RecordingScreen]
class RecordingRoute extends _i10.PageRouteInfo<void> {
  const RecordingRoute({List<_i10.PageRouteInfo>? children})
      : super(RecordingRoute.name, initialChildren: children);

  static const String name = 'RecordingRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i8.RecordingScreen());
    },
  );
}

/// generated route for
/// [_i9.RecordsListScreen]
class RecordsListRoute extends _i10.PageRouteInfo<void> {
  const RecordsListRoute({List<_i10.PageRouteInfo>? children})
      : super(RecordsListRoute.name, initialChildren: children);

  static const String name = 'RecordsListRoute';

  static _i10.PageInfo page = _i10.PageInfo(
    name,
    builder: (data) {
      return _i10.WrappedRoute(child: const _i9.RecordsListScreen());
    },
  );
}
