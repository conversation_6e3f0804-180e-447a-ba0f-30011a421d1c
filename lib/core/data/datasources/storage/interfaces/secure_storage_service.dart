import 'package:glidic_app/core/common/errors/app_failure.dart';

/// Abstract interface for secure storage operations
/// Used for storing sensitive data like tokens, credentials, etc.
///
/// All methods throw [AppFailure] or its subclasses on error:
/// - [StorageFailure] for storage-related errors
/// - [ValidationFailure] for invalid input data
abstract class SecureStorageService {
  /// Store a value securely with the given key
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key or value is invalid
  Future<void> store(String key, String value);

  /// Retrieve a value by key from secure storage
  /// Returns null if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<String?> retrieve(String key);

  /// Delete a value by key from secure storage
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<void> delete(String key);

  /// Check if a key exists in secure storage
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<bool> containsKey(String key);

  /// Clear all data from secure storage
  /// Throws [StorageFailure] if storage operation fails
  Future<void> clear();

  /// Get all keys from secure storage
  /// Throws [StorageFailure] if storage operation fails
  Future<List<String>> getAllKeys();

  /// Store multiple key-value pairs
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if any key or value is invalid
  Future<void> storeMultiple(Map<String, String> data);

  /// Retrieve multiple values by keys
  /// Returns map with null values for non-existent keys
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if any key is invalid
  Future<Map<String, String?>> retrieveMultiple(List<String> keys);

  /// Delete multiple keys
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if any key is invalid
  Future<void> deleteMultiple(List<String> keys);
}
