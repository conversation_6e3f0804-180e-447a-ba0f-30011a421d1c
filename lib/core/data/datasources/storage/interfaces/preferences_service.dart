import 'package:glidic_app/core/common/errors/failures.dart';

/// Abstract interface for preferences storage operations
/// Used for storing app settings and user preferences
///
/// All methods throw [AppFailure] or its subclasses on error:
/// - [StorageFailure] for storage-related errors
/// - [ValidationFailure] for invalid input data
abstract class PreferencesService {
  /// Store a string value
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key or value is invalid
  Future<void> setString(String key, String value);

  /// Retrieve a string value
  /// Returns null if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<String?> getString(String key);

  /// Store an integer value
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<void> setInt(String key, int value);

  /// Retrieve an integer value
  /// Returns null if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<int?> getInt(String key);

  /// Store a double value
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<void> setDouble(String key, double value);

  /// Retrieve a double value
  /// Returns null if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<double?> getDouble(String key);

  /// Store a boolean value
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<void> setBool(String key, bool value);

  /// Retrieve a boolean value
  /// Returns null if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<bool?> getBool(String key);

  /// Store a list of strings
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key or value is invalid
  Future<void> setStringList(String key, List<String> value);

  /// Retrieve a list of strings
  /// Returns null if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<List<String>?> getStringList(String key);

  /// Remove a value by key
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<void> remove(String key);

  /// Check if a key exists
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<bool> containsKey(String key);

  /// Clear all preferences
  /// Throws [StorageFailure] if storage operation fails
  Future<void> clear();

  /// Get all keys
  /// Throws [StorageFailure] if storage operation fails
  Future<Set<String>> getKeys();

  /// Get string with default value
  /// Returns the stored value or defaultValue if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<String> getStringWithDefault(String key, String defaultValue);

  /// Get int with default value
  /// Returns the stored value or defaultValue if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<int> getIntWithDefault(String key, int defaultValue);

  /// Get double with default value
  /// Returns the stored value or defaultValue if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<double> getDoubleWithDefault(String key, double defaultValue);

  /// Get bool with default value
  /// Returns the stored value or defaultValue if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<bool> getBoolWithDefault(String key, bool defaultValue);

  /// Get string list with default value
  /// Returns the stored value or defaultValue if key doesn't exist
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if key is invalid
  Future<List<String>> getStringListWithDefault(String key, List<String> defaultValue);

  /// Bulk set multiple values
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if any key or value is invalid
  Future<void> setBulk(Map<String, dynamic> values);

  /// Remove multiple keys
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if any key is invalid
  Future<void> removeMultiple(List<String> keys);
}
