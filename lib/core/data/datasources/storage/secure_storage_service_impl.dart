import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:glidic_app/core/common/constants/storage_constants.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/data/datasources/storage/interfaces/secure_storage_service.dart';

/// Implementation of SecureStorageService using flutter_secure_storage
class SecureStorageServiceImpl implements SecureStorageService {
  final FlutterSecureStorage _secureStorage;

  /// Constructor for dependency injection (testing)
  SecureStorageServiceImpl.withStorage(this._secureStorage);

  /// Constructor with optional custom storage options
  SecureStorageServiceImpl({
    FlutterSecureStorage? secureStorage,
  }) : _secureStorage = secureStorage ?? const FlutterSecureStorage();

  @override
  Future<void> store(String key, String value) async {
    try {
      if (key.isEmpty) {
        throw const ValidationFailure('Storage key cannot be empty');
      }

      if (value.length > StorageConstants.maxValueLength) {
        throw const ValidationFailure(
          'Value too long. Maximum length: ${StorageConstants.maxValueLength}',
        );
      }

      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to store data: ${e.toString()}');
    }
  }

  @override
  Future<String?> retrieve(String key) async {
    try {
      if (key.isEmpty) {
        throw const ValidationFailure('Storage key cannot be empty');
      }

      return await _secureStorage.read(key: key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to retrieve data: ${e.toString()}');
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      if (key.isEmpty) {
        throw const ValidationFailure('Storage key cannot be empty');
      }

      await _secureStorage.delete(key: key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to delete data: ${e.toString()}');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      if (key.isEmpty) {
        throw const ValidationFailure('Storage key cannot be empty');
      }

      return await _secureStorage.containsKey(key: key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to check key existence: ${e.toString()}');
    }
  }

  @override
  Future<void> clear() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to clear storage: ${e.toString()}');
    }
  }

  @override
  Future<List<String>> getAllKeys() async {
    try {
      final allData = await _secureStorage.readAll();
      return allData.keys.toList();
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get all keys: ${e.toString()}');
    }
  }

  @override
  Future<void> storeMultiple(Map<String, String> data) async {
    try {
      if (data.isEmpty) {
        return;
      }

      // Validate all keys and values first
      for (final entry in data.entries) {
        if (entry.key.isEmpty) {
          throw const ValidationFailure('Storage key cannot be empty');
        }
        if (entry.value.length > StorageConstants.maxValueLength) {
          throw ValidationFailure(
            'Value too long for key "${entry.key}". Maximum length: ${StorageConstants.maxValueLength}',
          );
        }
      }

      // Store all data
      for (final entry in data.entries) {
        await _secureStorage.write(key: entry.key, value: entry.value);
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to store multiple data: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, String?>> retrieveMultiple(List<String> keys) async {
    try {
      if (keys.isEmpty) {
        return {};
      }

      // Validate all keys first
      for (final key in keys) {
        if (key.isEmpty) {
          throw const ValidationFailure('Storage key cannot be empty');
        }
      }

      final result = <String, String?>{};
      for (final key in keys) {
        final value = await _secureStorage.read(key: key);
        result[key] = value;
      }

      return result;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to retrieve multiple data: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteMultiple(List<String> keys) async {
    try {
      if (keys.isEmpty) {
        return;
      }

      // Validate all keys first
      for (final key in keys) {
        if (key.isEmpty) {
          throw const ValidationFailure('Storage key cannot be empty');
        }
      }

      // Delete all keys
      for (final key in keys) {
        await _secureStorage.delete(key: key);
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to delete multiple data: ${e.toString()}');
    }
  }
}
