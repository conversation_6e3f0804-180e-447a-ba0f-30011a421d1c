import 'package:glidic_app/core/common/constants/storage_constants.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/data/datasources/storage/interfaces/preferences_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Implementation of PreferencesService using shared_preferences
class PreferencesServiceImpl implements PreferencesService {
  SharedPreferences? _prefs;

  /// Constructor for dependency injection (testing)
  PreferencesServiceImpl.withPreferences(SharedPreferences prefs)
      : _prefs = prefs;

  /// Default constructor
  PreferencesServiceImpl();

  /// Get SharedPreferences instance
  Future<SharedPreferences> get _preferences async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }

  /// Validate key
  /// Throws [ValidationFailure] if validation fails
  void _validateKey(String key) {
    if (key.isEmpty) {
      throw const ValidationFailure('Preferences key cannot be empty');
    }
    if (key.length > StorageConstants.maxKeyLength) {
      throw const ValidationFailure(
        'Key too long. Maximum length: ${StorageConstants.maxKeyLength}',
      );
    }
  }

  @override
  Future<void> setString(String key, String value) async {
    try {
      _validateKey(key);

      if (value.length > StorageConstants.maxValueLength) {
        throw const ValidationFailure(
          'Value too long. Maximum length: ${StorageConstants.maxValueLength}',
        );
      }

      final prefs = await _preferences;
      final success = await prefs.setString(key, value);

      if (!success) {
        throw const StorageFailure('Failed to store string value');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to set string: ${e.toString()}');
    }
  }

  @override
  Future<String?> getString(String key) async {
    try {
      _validateKey(key);

      final prefs = await _preferences;
      return prefs.getString(key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get string: ${e.toString()}');
    }
  }

  @override
  Future<void> setInt(String key, int value) async {
    try {
      _validateKey(key);

      final prefs = await _preferences;
      final success = await prefs.setInt(key, value);

      if (!success) {
        throw const StorageFailure('Failed to store int value');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to set int: ${e.toString()}');
    }
  }

  @override
  Future<int?> getInt(String key) async {
    try {
      _validateKey(key);

      final prefs = await _preferences;
      return prefs.getInt(key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get int: ${e.toString()}');
    }
  }

  @override
  Future<void> setDouble(String key, double value) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      final success = await prefs.setDouble(key, value);

      if (!success) {
        throw const StorageFailure('Failed to store double value');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to set double: ${e.toString()}');
    }
  }

  @override
  Future<double?> getDouble(String key) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      return prefs.getDouble(key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get double: ${e.toString()}');
    }
  }

  @override
  Future<void> setBool(String key, bool value) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      final success = await prefs.setBool(key, value);

      if (!success) {
        throw const StorageFailure('Failed to store bool value');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to set bool: ${e.toString()}');
    }
  }

  @override
  Future<bool?> getBool(String key) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      return prefs.getBool(key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get bool: ${e.toString()}');
    }
  }

  @override
  Future<void> setStringList(
    String key,
    List<String> value,
  ) async {
    try {
      _validateKey(key);

      // Validate list size
      if (value.length > StorageConstants.maxListLength) {
        throw const ValidationFailure(
          'List too long. Maximum length: ${StorageConstants.maxListLength}',
        );
      }

      // Validate each string in the list
      for (final item in value) {
        if (item.length > StorageConstants.maxValueLength) {
          throw const ValidationFailure(
            'List item too long. Maximum length: ${StorageConstants.maxValueLength}',
          );
        }
      }

      final prefs = await _preferences;
      final success = await prefs.setStringList(key, value);

      if (!success) {
        throw const StorageFailure('Failed to store string list');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to set string list: ${e.toString()}');
    }
  }

  @override
  Future<List<String>?> getStringList(String key) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      return prefs.getStringList(key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get string list: ${e.toString()}');
    }
  }

  @override
  Future<void> remove(String key) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      final success = await prefs.remove(key);

      if (!success) {
        throw const StorageFailure('Failed to remove key');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to remove: ${e.toString()}');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      _validateKey(key);
      final prefs = await _preferences;
      return prefs.containsKey(key);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to check key: ${e.toString()}');
    }
  }

  @override
  Future<void> clear() async {
    try {
      final prefs = await _preferences;
      final success = await prefs.clear();

      if (!success) {
        throw const StorageFailure('Failed to clear preferences');
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to clear: ${e.toString()}');
    }
  }

  @override
  Future<Set<String>> getKeys() async {
    try {
      final prefs = await _preferences;
      return prefs.getKeys();
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get keys: ${e.toString()}');
    }
  }

  @override
  Future<String> getStringWithDefault(
    String key,
    String defaultValue,
  ) async {
    try {
      final value = await getString(key);
      return value ?? defaultValue;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure(
        'Failed to get string with default: ${e.toString()}',
      );
    }
  }

  @override
  Future<int> getIntWithDefault(
    String key,
    int defaultValue,
  ) async {
    try {
      final value = await getInt(key);
      return value ?? defaultValue;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get int with default: ${e.toString()}');
    }
  }

  @override
  Future<double> getDoubleWithDefault(
    String key,
    double defaultValue,
  ) async {
    try {
      final value = await getDouble(key);
      return value ?? defaultValue;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure(
        'Failed to get double with default: ${e.toString()}',
      );
    }
  }

  @override
  Future<bool> getBoolWithDefault(
    String key,
    bool defaultValue,
  ) async {
    try {
      final value = await getBool(key);
      return value ?? defaultValue;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to get bool with default: ${e.toString()}');
    }
  }

  @override
  Future<List<String>> getStringListWithDefault(
    String key,
    List<String> defaultValue,
  ) async {
    try {
      final value = await getStringList(key);
      return value ?? defaultValue;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure(
        'Failed to get string list with default: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> setBulk(Map<String, dynamic> values) async {
    try {
      if (values.isEmpty) {
        return;
      }

      final prefs = await _preferences;

      for (final entry in values.entries) {
        _validateKey(entry.key);

        final value = entry.value;
        bool success = false;

        if (value is String) {
          if (value.length > StorageConstants.maxValueLength) {
            throw ValidationFailure(
              'Value too long for key "${entry.key}". Maximum length: ${StorageConstants.maxValueLength}',
            );
          }
          success = await prefs.setString(entry.key, value);
        } else if (value is int) {
          success = await prefs.setInt(entry.key, value);
        } else if (value is double) {
          success = await prefs.setDouble(entry.key, value);
        } else if (value is bool) {
          success = await prefs.setBool(entry.key, value);
        } else if (value is List<String>) {
          if (value.length > StorageConstants.maxListLength) {
            throw ValidationFailure(
              'List too long for key "${entry.key}". Maximum length: ${StorageConstants.maxListLength}',
            );
          }
          success = await prefs.setStringList(entry.key, value);
        } else {
          throw ValidationFailure(
            'Unsupported value type for key "${entry.key}": ${value.runtimeType}',
          );
        }

        if (!success) {
          throw StorageFailure('Failed to store value for key "${entry.key}"');
        }
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to set bulk values: ${e.toString()}');
    }
  }

  @override
  Future<void> removeMultiple(List<String> keys) async {
    try {
      if (keys.isEmpty) {
        return;
      }

      final prefs = await _preferences;

      for (final key in keys) {
        _validateKey(key);

        final success = await prefs.remove(key);
        if (!success) {
          throw StorageFailure('Failed to remove key "$key"');
        }
      }
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw StorageFailure('Failed to remove multiple keys: ${e.toString()}');
    }
  }
}
