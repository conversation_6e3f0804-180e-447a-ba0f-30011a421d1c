import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/common/errors/error_handler.dart';

/// Enhanced interceptor for handling errors globally with comprehensive error mapping
/// This interceptor provides centralized error handling for all HTTP requests
/// and maps them to domain-specific failures
class ErrorInterceptor extends Interceptor {
  final Connectivity _connectivity;
  final void Function(AppFailure)? onAuthenticationError;
  final void Function(AppFailure)? onCriticalError;

  ErrorInterceptor({
    Connectivity? connectivity,
    this.onAuthenticationError,
    this.onCriticalError,
  }) : _connectivity = connectivity ?? Connectivity();

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // Log the error for debugging
    final failure = NetworkFailure(
      'Network request failed: ${err.message}',
      statusCode: err.response?.statusCode,
      code: _getErrorCode(err),
    );
    ErrorLogger.logFailure(failure, context: 'HTTP Request');

    // Handle specific error cases globally
    await _handleSpecificErrors(err);

    // Continue with the original error
    handler.next(err);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add request timestamp for duration calculation
    options.extra['requestStartTime'] = DateTime.now().millisecondsSinceEpoch;

    // Add unique request ID for tracking
    options.extra['requestId'] = _generateRequestId();

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Log successful requests in debug mode - removed debug logging
    // since ErrorLogger only supports failure logging
    handler.next(response);
  }

  /// Handle specific error cases that require global actions
  Future<void> _handleSpecificErrors(DioException err) async {
    switch (err.response?.statusCode) {
      case 401:
        // Handle authentication errors
        final authFailure = AppFailureConstructors.authenticationFailed();

        ErrorLogger.logFailure(authFailure, context: 'Authentication Error');
        onAuthenticationError?.call(authFailure);
        break;

      case 403:
        // Handle authorization errors
        final authzFailure = AppFailureConstructors.accessDenied();

        ErrorLogger.logFailure(authzFailure, context: 'Authorization Error');
        break;

      case 429:
        // Handle rate limiting
        final rateLimitFailure = AppFailureConstructors.rateLimitExceeded();

        ErrorLogger.logFailure(rateLimitFailure, context: 'Rate Limit Error');
        break;

      case 500:
      case 502:
      case 503:
      case 504:
        // Handle server errors
        final serverFailure = AppFailureConstructors.serverError(err.response?.statusCode);

        ErrorLogger.logFailure(serverFailure, context: 'Server Error');

        // Notify about critical server errors
        if (err.response?.statusCode == 500) {
          onCriticalError?.call(serverFailure);
        }
        break;

      default:
        // Handle other HTTP errors
        if (err.type == DioExceptionType.connectionTimeout ||
            err.type == DioExceptionType.receiveTimeout ||
            err.type == DioExceptionType.sendTimeout) {
          final timeoutFailure = AppFailureConstructors.timeout();
          ErrorLogger.logFailure(timeoutFailure, context: 'Timeout Error');
        } else if (err.type == DioExceptionType.connectionError) {
          // Check connectivity
          final connectivityResult = await _connectivity.checkConnectivity();

          final connectivityFailure =
              connectivityResult.contains(ConnectivityResult.none)
                  ? AppFailureConstructors.noConnection()
                  : AppFailureConstructors.connectionFailed();

          ErrorLogger.logFailure(
            connectivityFailure,
            context: 'Connectivity Error',
          );
        }
        break;
    }
  }

  /// Generate error code based on DioException type and status code
  String _getErrorCode(DioException err) {
    if (err.response?.statusCode != null) {
      return 'HTTP_${err.response!.statusCode}';
    }

    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        return 'CONNECTION_TIMEOUT';
      case DioExceptionType.sendTimeout:
        return 'SEND_TIMEOUT';
      case DioExceptionType.receiveTimeout:
        return 'RECEIVE_TIMEOUT';
      case DioExceptionType.badCertificate:
        return 'BAD_CERTIFICATE';
      case DioExceptionType.badResponse:
        return 'BAD_RESPONSE';
      case DioExceptionType.cancel:
        return 'REQUEST_CANCELLED';
      case DioExceptionType.connectionError:
        return 'CONNECTION_ERROR';
      case DioExceptionType.unknown:
        return 'UNKNOWN_ERROR';
    }
  }

  /// Generate a unique request ID for tracking
  String _generateRequestId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
