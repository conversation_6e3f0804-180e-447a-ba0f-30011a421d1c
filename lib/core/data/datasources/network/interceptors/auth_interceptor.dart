import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_local_datasource.dart';

/// Interceptor for handling authentication tokens
/// This interceptor automatically adds authentication tokens to outgoing requests
class AuthInterceptor extends Interceptor {
  final AuthLocalDataSource _authLocalDataSource;

  AuthInterceptor(this._authLocalDataSource);

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // Get access token from secure storage
      final token = await _authLocalDataSource.getAccessToken();

      if (token != null && token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    } catch (e) {
      // Log error but don't block the request
      final failure = ExceptionHandler.handle(e);
      ErrorLogger.logFailure(
        failure,
        context: 'AuthInterceptor - Token Retrieval',
      );
    }

    handler.next(options);
  }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // Handle 401 Unauthorized responses
    if (err.response?.statusCode == 401) {
      try {
        // Try to refresh the token
        final newToken = await _refreshToken();

        if (newToken != null) {
          // Retry the original request with new token
          final requestOptions = err.requestOptions;
          requestOptions.headers['Authorization'] = 'Bearer $newToken';

          try {
            final dio = Dio();
            final response = await dio.fetch(requestOptions);
            handler.resolve(response);
          } catch (e) {
            handler.next(err);
          }
        } else {
          // Refresh failed, clear all auth data
          await _authLocalDataSource.clearAll();
          handler.next(err);
        }
      } catch (e) {
        // Refresh failed, clear all auth data
        await _authLocalDataSource.clearAll();
        handler.next(err);
      }
    } else {
      handler.next(err);
    }
  }

  /// Attempt to refresh the access token using the refresh token
  /// Returns the new access token if successful, null if failed
  /// Throws [AppFailure] if refresh fails
  Future<String?> _refreshToken() async {
    try {
      final refreshToken = await _authLocalDataSource.getRefreshToken();

      if (refreshToken == null || refreshToken.isEmpty) {
        throw const AuthFailure('No refresh token available');
      }

      // TODO: Implement actual token refresh API call
      // For now, return null to indicate refresh failed
      // In a real implementation, you would:
      // 1. Call the refresh token API endpoint
      // 2. Save the new access token
      // 3. Return the new access token

      return null;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw AuthFailure('Token refresh failed: ${e.toString()}');
    }
  }
}
