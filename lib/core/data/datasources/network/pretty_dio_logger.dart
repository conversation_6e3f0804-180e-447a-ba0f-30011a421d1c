import 'dart:convert';
import 'dart:math' as math;
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

/// Beautiful and structured logging interceptor for Dio HTTP requests
/// Provides colorful, well-formatted logs for debugging API calls
class PrettyDioLogger extends Interceptor {
  PrettyDioLogger({
    this.requestHeader = true,
    this.requestBody = true,
    this.responseHeader = false,
    this.responseBody = true,
    this.error = true,
    this.maxWidth = 90,
    this.compact = true,
  }) {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 5,
        lineLength: maxWidth,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
    );
  }

  /// Whether to log request headers
  final bool requestHeader;

  /// Whether to log request body
  final bool requestBody;

  /// Whether to log response headers
  final bool responseHeader;

  /// Whether to log response body
  final bool responseBody;

  /// Whether to log errors
  final bool error;

  /// Maximum width of log lines
  final int maxWidth;

  /// Whether to use compact logging format
  final bool compact;

  late Logger _logger;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (compact) {
      _logCompactRequest(options);
    } else {
      _logDetailedRequest(options);
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (compact) {
      _logCompactResponse(response);
    } else {
      _logDetailedResponse(response);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (error) {
      _logError(err);
    }
    super.onError(err, handler);
  }

  void _logCompactRequest(RequestOptions options) {
    final method = options.method.toUpperCase();
    final uri = options.uri.toString();

    _logger.i('🚀 $method $uri');

    if (requestHeader && options.headers.isNotEmpty) {
      _logger.d('📋 Headers: ${_formatHeaders(options.headers)}');
    }

    if (requestBody && options.data != null) {
      _logger.d('📦 Body: ${_formatData(options.data)}');
    }
  }

  void _logDetailedRequest(RequestOptions options) {
    final method = options.method.toUpperCase();
    final uri = options.uri.toString();

    _logger.i(
      _buildBox([
        '🚀 REQUEST',
        '$method $uri',
        if (requestHeader && options.headers.isNotEmpty)
          'Headers: ${_formatHeaders(options.headers)}',
        if (requestBody && options.data != null)
          'Body: ${_formatData(options.data)}',
      ]),
    );
  }

  void _logCompactResponse(Response response) {
    final method = response.requestOptions.method.toUpperCase();
    final uri = response.requestOptions.uri.toString();
    final statusCode = response.statusCode;
    final duration = DateTime.now().millisecondsSinceEpoch -
        (response.requestOptions.extra['start_time'] as int? ?? 0);

    final statusEmoji = _getStatusEmoji(statusCode ?? 0);
    _logger.i('$statusEmoji $method $uri [$statusCode] ${duration}ms');

    if (responseBody && response.data != null) {
      _logger.d('📥 Response: ${_formatData(response.data)}');
    }
  }

  void _logDetailedResponse(Response response) {
    final method = response.requestOptions.method.toUpperCase();
    final uri = response.requestOptions.uri.toString();
    final statusCode = response.statusCode;
    final duration = DateTime.now().millisecondsSinceEpoch -
        (response.requestOptions.extra['start_time'] as int? ?? 0);

    final statusEmoji = _getStatusEmoji(statusCode ?? 0);

    _logger.i(
      _buildBox([
        '$statusEmoji RESPONSE',
        '$method $uri',
        'Status: $statusCode',
        'Duration: ${duration}ms',
        if (responseHeader && response.headers.map.isNotEmpty)
          'Headers: ${_formatHeaders(response.headers.map)}',
        if (responseBody && response.data != null)
          'Body: ${_formatData(response.data)}',
      ]),
    );
  }

  void _logError(DioException err) {
    final method = err.requestOptions.method.toUpperCase();
    final uri = err.requestOptions.uri.toString();

    _logger.e(
      _buildBox([
        '❌ ERROR',
        '$method $uri',
        'Type: ${err.type}',
        'Message: ${err.message}',
        if (err.response != null) 'Status: ${err.response?.statusCode}',
        if (err.response?.data != null)
          'Response: ${_formatData(err.response?.data)}',
      ]),
    );
  }

  String _formatHeaders(Map<String, dynamic> headers) {
    if (headers.isEmpty) return '{}';

    final formatted =
        headers.entries.map((e) => '${e.key}: ${e.value}').join(', ');

    return '{$formatted}';
  }

  String _formatData(dynamic data) {
    if (data == null) return 'null';

    try {
      if (data is String) {
        // Try to parse as JSON for pretty printing
        try {
          final decoded = jsonDecode(data);
          return _prettyJson(decoded);
        } catch (_) {
          return data;
        }
      } else if (data is Map || data is List) {
        return _prettyJson(data);
      } else {
        return data.toString();
      }
    } catch (e) {
      return data.toString();
    }
  }

  String _prettyJson(dynamic json) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      final prettyString = encoder.convert(json);

      // Truncate if too long
      if (prettyString.length > 1000) {
        return '${prettyString.substring(0, 1000)}...';
      }

      return prettyString;
    } catch (e) {
      return json.toString();
    }
  }

  String _getStatusEmoji(int statusCode) {
    if (statusCode >= 200 && statusCode < 300) return '✅';
    if (statusCode >= 300 && statusCode < 400) return '🔄';
    if (statusCode >= 400 && statusCode < 500) return '⚠️';
    if (statusCode >= 500) return '❌';
    return '❓';
  }

  String _buildBox(List<String> lines) {
    if (lines.isEmpty) return '';

    final maxLength = math.max(
      maxWidth,
      lines.map((line) => line.length).reduce(math.max),
    );

    final border = '═' * (maxLength + 4);
    final result = StringBuffer();

    result.writeln('╔$border╗');
    for (final line in lines) {
      final padding = ' ' * (maxLength - line.length);
      result.writeln('║  $line$padding  ║');
    }
    result.writeln('╚$border╝');

    return result.toString();
  }
}
