import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:glidic_app/core/common/constants/database_constants.dart';
import 'package:glidic_app/core/data/datasources/database/daos/records_dao.dart';
import 'package:glidic_app/core/data/datasources/database/daos/users_dao.dart';
import 'package:glidic_app/core/data/datasources/database/tables/records_table.dart';
import 'package:glidic_app/core/data/datasources/database/tables/users_table.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:sqlite3/sqlite3.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

// This will be generated by drift_dev
part 'app_database.g.dart';

/// Main database class for the application
/// Uses Drift ORM for SQLite database operations
@DriftDatabase(
  tables: [
    UsersTable,
    RecordsTable,
  ],
  daos: [
    <PERSON><PERSON><PERSON>,
    <PERSON>Dao,
  ],
)
class AppDatabase extends _$AppDatabase {
  /// Constructor for the database
  AppDatabase() : super(_openConnection());

  /// Constructor for testing with custom query executor
  AppDatabase.forTesting(super.e);

  @override
  int get schemaVersion => DatabaseConstants.schemaVersion;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        // Create all tables
        await m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle database migrations here
        // Example:
        // if (from < 2) {
        //   await m.addColumn(usersTable, usersTable.newColumn);
        // }
      },
      beforeOpen: (details) async {
        // Enable foreign key constraints
        await customStatement('PRAGMA foreign_keys = ON');

        // Optimize database performance
        await customStatement('PRAGMA journal_mode = WAL');
        await customStatement('PRAGMA synchronous = NORMAL');
        await customStatement('PRAGMA cache_size = 10000');
        await customStatement('PRAGMA temp_store = MEMORY');
      },
    );
  }

  /// Get database size in bytes
  Future<int> getDatabaseSize() async {
    final dbFile = File(await _getDatabasePath());
    if (await dbFile.exists()) {
      return dbFile.length();
    }
    return 0;
  }

  /// Clear all data from the database (useful for logout)
  Future<void> clearAllData() async {
    await transaction(() async {
      await delete(usersTable).go();
      await delete(recordsTable).go();
    });
  }
}

/// Create database connection
LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    // Ensure sqlite3 is properly initialized on mobile platforms
    if (Platform.isAndroid) {
      await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
    }

    // Get the database file path
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, DatabaseConstants.databaseName));

    // Configure sqlite3 to use the bundled library on mobile
    if (Platform.isAndroid || Platform.isIOS) {
      sqlite3.tempDirectory = (await getTemporaryDirectory()).path;
    }

    return NativeDatabase.createInBackground(file);
  });
}

/// Get the database file path
Future<String> _getDatabasePath() async {
  final dbFolder = await getApplicationDocumentsDirectory();
  return p.join(dbFolder.path, DatabaseConstants.databaseName);
}
