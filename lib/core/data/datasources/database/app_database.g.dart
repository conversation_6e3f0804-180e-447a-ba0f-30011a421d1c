// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// ignore_for_file: type=lint
class $UsersTableTable extends UsersTable
    with TableInfo<$UsersTableTable, UserTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UsersTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
      'email', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(),
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _profilePictureMeta =
      const VerificationMeta('profilePicture');
  @override
  late final GeneratedColumn<String> profilePicture = GeneratedColumn<String>(
      'profile_picture', aliasedName, true,
      additionalChecks: GeneratedColumn.checkTextLength(),
      type: DriftSqlType.string,
      requiredDuringInsert: false);
  static const VerificationMeta _isEmailVerifiedMeta =
      const VerificationMeta('isEmailVerified');
  @override
  late final GeneratedColumn<bool> isEmailVerified = GeneratedColumn<bool>(
      'is_email_verified', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_email_verified" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, email, name, profilePicture, isEmailVerified, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'users';
  @override
  VerificationContext validateIntegrity(Insertable<UserTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('email')) {
      context.handle(
          _emailMeta, email.isAcceptableOrUnknown(data['email']!, _emailMeta));
    } else if (isInserting) {
      context.missing(_emailMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('profile_picture')) {
      context.handle(
          _profilePictureMeta,
          profilePicture.isAcceptableOrUnknown(
              data['profile_picture']!, _profilePictureMeta));
    }
    if (data.containsKey('is_email_verified')) {
      context.handle(
          _isEmailVerifiedMeta,
          isEmailVerified.isAcceptableOrUnknown(
              data['is_email_verified']!, _isEmailVerifiedMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  UserTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return UserTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      email: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}email'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      profilePicture: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}profile_picture']),
      isEmailVerified: attachedDatabase.typeMapping.read(
          DriftSqlType.bool, data['${effectivePrefix}is_email_verified'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $UsersTableTable createAlias(String alias) {
    return $UsersTableTable(attachedDatabase, alias);
  }
}

class UserTableData extends DataClass implements Insertable<UserTableData> {
  /// Primary key - unique identifier for each user
  final String id;

  /// User's email address - unique and required
  final String email;

  /// User's display name
  final String name;

  /// URL to user's profile picture
  final String? profilePicture;

  /// Whether the user's email has been verified
  final bool isEmailVerified;

  /// Timestamp when the user record was created
  final DateTime createdAt;

  /// Timestamp when the user record was last updated
  final DateTime updatedAt;
  const UserTableData(
      {required this.id,
      required this.email,
      required this.name,
      this.profilePicture,
      required this.isEmailVerified,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['email'] = Variable<String>(email);
    map['name'] = Variable<String>(name);
    if (!nullToAbsent || profilePicture != null) {
      map['profile_picture'] = Variable<String>(profilePicture);
    }
    map['is_email_verified'] = Variable<bool>(isEmailVerified);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  UsersTableCompanion toCompanion(bool nullToAbsent) {
    return UsersTableCompanion(
      id: Value(id),
      email: Value(email),
      name: Value(name),
      profilePicture: profilePicture == null && nullToAbsent
          ? const Value.absent()
          : Value(profilePicture),
      isEmailVerified: Value(isEmailVerified),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory UserTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return UserTableData(
      id: serializer.fromJson<String>(json['id']),
      email: serializer.fromJson<String>(json['email']),
      name: serializer.fromJson<String>(json['name']),
      profilePicture: serializer.fromJson<String?>(json['profilePicture']),
      isEmailVerified: serializer.fromJson<bool>(json['isEmailVerified']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'email': serializer.toJson<String>(email),
      'name': serializer.toJson<String>(name),
      'profilePicture': serializer.toJson<String?>(profilePicture),
      'isEmailVerified': serializer.toJson<bool>(isEmailVerified),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  UserTableData copyWith(
          {String? id,
          String? email,
          String? name,
          Value<String?> profilePicture = const Value.absent(),
          bool? isEmailVerified,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      UserTableData(
        id: id ?? this.id,
        email: email ?? this.email,
        name: name ?? this.name,
        profilePicture:
            profilePicture.present ? profilePicture.value : this.profilePicture,
        isEmailVerified: isEmailVerified ?? this.isEmailVerified,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  UserTableData copyWithCompanion(UsersTableCompanion data) {
    return UserTableData(
      id: data.id.present ? data.id.value : this.id,
      email: data.email.present ? data.email.value : this.email,
      name: data.name.present ? data.name.value : this.name,
      profilePicture: data.profilePicture.present
          ? data.profilePicture.value
          : this.profilePicture,
      isEmailVerified: data.isEmailVerified.present
          ? data.isEmailVerified.value
          : this.isEmailVerified,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('UserTableData(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('name: $name, ')
          ..write('profilePicture: $profilePicture, ')
          ..write('isEmailVerified: $isEmailVerified, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, email, name, profilePicture, isEmailVerified, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is UserTableData &&
          other.id == this.id &&
          other.email == this.email &&
          other.name == this.name &&
          other.profilePicture == this.profilePicture &&
          other.isEmailVerified == this.isEmailVerified &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class UsersTableCompanion extends UpdateCompanion<UserTableData> {
  final Value<String> id;
  final Value<String> email;
  final Value<String> name;
  final Value<String?> profilePicture;
  final Value<bool> isEmailVerified;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const UsersTableCompanion({
    this.id = const Value.absent(),
    this.email = const Value.absent(),
    this.name = const Value.absent(),
    this.profilePicture = const Value.absent(),
    this.isEmailVerified = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UsersTableCompanion.insert({
    required String id,
    required String email,
    required String name,
    this.profilePicture = const Value.absent(),
    this.isEmailVerified = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        email = Value(email),
        name = Value(name);
  static Insertable<UserTableData> custom({
    Expression<String>? id,
    Expression<String>? email,
    Expression<String>? name,
    Expression<String>? profilePicture,
    Expression<bool>? isEmailVerified,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (email != null) 'email': email,
      if (name != null) 'name': name,
      if (profilePicture != null) 'profile_picture': profilePicture,
      if (isEmailVerified != null) 'is_email_verified': isEmailVerified,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UsersTableCompanion copyWith(
      {Value<String>? id,
      Value<String>? email,
      Value<String>? name,
      Value<String?>? profilePicture,
      Value<bool>? isEmailVerified,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return UsersTableCompanion(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (profilePicture.present) {
      map['profile_picture'] = Variable<String>(profilePicture.value);
    }
    if (isEmailVerified.present) {
      map['is_email_verified'] = Variable<bool>(isEmailVerified.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UsersTableCompanion(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('name: $name, ')
          ..write('profilePicture: $profilePicture, ')
          ..write('isEmailVerified: $isEmailVerified, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $RecordsTableTable extends RecordsTable
    with TableInfo<$RecordsTableTable, RecordsTableData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $RecordsTableTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _filePathMeta =
      const VerificationMeta('filePath');
  @override
  late final GeneratedColumn<String> filePath = GeneratedColumn<String>(
      'file_path', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _durationMsMeta =
      const VerificationMeta('durationMs');
  @override
  late final GeneratedColumn<int> durationMs = GeneratedColumn<int>(
      'duration_ms', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _fileSizeMeta =
      const VerificationMeta('fileSize');
  @override
  late final GeneratedColumn<int> fileSize = GeneratedColumn<int>(
      'file_size', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _waveformDataMeta =
      const VerificationMeta('waveformData');
  @override
  late final GeneratedColumn<String> waveformData = GeneratedColumn<String>(
      'waveform_data', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isPlayingMeta =
      const VerificationMeta('isPlaying');
  @override
  late final GeneratedColumn<bool> isPlaying = GeneratedColumn<bool>(
      'is_playing', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_playing" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _playbackPositionMsMeta =
      const VerificationMeta('playbackPositionMs');
  @override
  late final GeneratedColumn<int> playbackPositionMs = GeneratedColumn<int>(
      'playback_position_ms', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('pending'));
  static const VerificationMeta _remoteUrlMeta =
      const VerificationMeta('remoteUrl');
  @override
  late final GeneratedColumn<String> remoteUrl = GeneratedColumn<String>(
      'remote_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _transcriptionIdMeta =
      const VerificationMeta('transcriptionId');
  @override
  late final GeneratedColumn<int> transcriptionId = GeneratedColumn<int>(
      'transcription_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        title,
        filePath,
        durationMs,
        createdAt,
        fileSize,
        waveformData,
        isPlaying,
        playbackPositionMs,
        status,
        remoteUrl,
        transcriptionId,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'records';
  @override
  VerificationContext validateIntegrity(Insertable<RecordsTableData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('file_path')) {
      context.handle(_filePathMeta,
          filePath.isAcceptableOrUnknown(data['file_path']!, _filePathMeta));
    } else if (isInserting) {
      context.missing(_filePathMeta);
    }
    if (data.containsKey('duration_ms')) {
      context.handle(
          _durationMsMeta,
          durationMs.isAcceptableOrUnknown(
              data['duration_ms']!, _durationMsMeta));
    } else if (isInserting) {
      context.missing(_durationMsMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('file_size')) {
      context.handle(_fileSizeMeta,
          fileSize.isAcceptableOrUnknown(data['file_size']!, _fileSizeMeta));
    } else if (isInserting) {
      context.missing(_fileSizeMeta);
    }
    if (data.containsKey('waveform_data')) {
      context.handle(
          _waveformDataMeta,
          waveformData.isAcceptableOrUnknown(
              data['waveform_data']!, _waveformDataMeta));
    }
    if (data.containsKey('is_playing')) {
      context.handle(_isPlayingMeta,
          isPlaying.isAcceptableOrUnknown(data['is_playing']!, _isPlayingMeta));
    }
    if (data.containsKey('playback_position_ms')) {
      context.handle(
          _playbackPositionMsMeta,
          playbackPositionMs.isAcceptableOrUnknown(
              data['playback_position_ms']!, _playbackPositionMsMeta));
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    }
    if (data.containsKey('remote_url')) {
      context.handle(_remoteUrlMeta,
          remoteUrl.isAcceptableOrUnknown(data['remote_url']!, _remoteUrlMeta));
    }
    if (data.containsKey('transcription_id')) {
      context.handle(
          _transcriptionIdMeta,
          transcriptionId.isAcceptableOrUnknown(
              data['transcription_id']!, _transcriptionIdMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  RecordsTableData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return RecordsTableData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      filePath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_path'])!,
      durationMs: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}duration_ms'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      fileSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_size'])!,
      waveformData: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}waveform_data']),
      isPlaying: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_playing'])!,
      playbackPositionMs: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}playback_position_ms'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
      remoteUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}remote_url']),
      transcriptionId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}transcription_id']),
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $RecordsTableTable createAlias(String alias) {
    return $RecordsTableTable(attachedDatabase, alias);
  }
}

class RecordsTableData extends DataClass
    implements Insertable<RecordsTableData> {
  /// Primary key - unique identifier for each record
  final String id;

  /// Display title of the record
  final String title;

  /// File path where the record is stored
  final String filePath;

  /// Duration of the record in milliseconds
  final int durationMs;

  /// When the record was created
  final DateTime createdAt;

  /// File size in bytes
  final int fileSize;

  /// Waveform data for visualization (stored as JSON string)
  final String? waveformData;

  /// Whether the record is currently playing (runtime state)
  final bool isPlaying;

  /// Current playback position in milliseconds (runtime state)
  final int playbackPositionMs;

  /// Upload status: pending, uploading, finished
  final String status;

  /// Remote URL after successful upload
  final String? remoteUrl;

  /// Transcription ID from transcription service
  final int? transcriptionId;

  /// Timestamp when the record was last updated
  final DateTime updatedAt;
  const RecordsTableData(
      {required this.id,
      required this.title,
      required this.filePath,
      required this.durationMs,
      required this.createdAt,
      required this.fileSize,
      this.waveformData,
      required this.isPlaying,
      required this.playbackPositionMs,
      required this.status,
      this.remoteUrl,
      this.transcriptionId,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['title'] = Variable<String>(title);
    map['file_path'] = Variable<String>(filePath);
    map['duration_ms'] = Variable<int>(durationMs);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['file_size'] = Variable<int>(fileSize);
    if (!nullToAbsent || waveformData != null) {
      map['waveform_data'] = Variable<String>(waveformData);
    }
    map['is_playing'] = Variable<bool>(isPlaying);
    map['playback_position_ms'] = Variable<int>(playbackPositionMs);
    map['status'] = Variable<String>(status);
    if (!nullToAbsent || remoteUrl != null) {
      map['remote_url'] = Variable<String>(remoteUrl);
    }
    if (!nullToAbsent || transcriptionId != null) {
      map['transcription_id'] = Variable<int>(transcriptionId);
    }
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  RecordsTableCompanion toCompanion(bool nullToAbsent) {
    return RecordsTableCompanion(
      id: Value(id),
      title: Value(title),
      filePath: Value(filePath),
      durationMs: Value(durationMs),
      createdAt: Value(createdAt),
      fileSize: Value(fileSize),
      waveformData: waveformData == null && nullToAbsent
          ? const Value.absent()
          : Value(waveformData),
      isPlaying: Value(isPlaying),
      playbackPositionMs: Value(playbackPositionMs),
      status: Value(status),
      remoteUrl: remoteUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(remoteUrl),
      transcriptionId: transcriptionId == null && nullToAbsent
          ? const Value.absent()
          : Value(transcriptionId),
      updatedAt: Value(updatedAt),
    );
  }

  factory RecordsTableData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return RecordsTableData(
      id: serializer.fromJson<String>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      filePath: serializer.fromJson<String>(json['filePath']),
      durationMs: serializer.fromJson<int>(json['durationMs']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      fileSize: serializer.fromJson<int>(json['fileSize']),
      waveformData: serializer.fromJson<String?>(json['waveformData']),
      isPlaying: serializer.fromJson<bool>(json['isPlaying']),
      playbackPositionMs: serializer.fromJson<int>(json['playbackPositionMs']),
      status: serializer.fromJson<String>(json['status']),
      remoteUrl: serializer.fromJson<String?>(json['remoteUrl']),
      transcriptionId: serializer.fromJson<int?>(json['transcriptionId']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'title': serializer.toJson<String>(title),
      'filePath': serializer.toJson<String>(filePath),
      'durationMs': serializer.toJson<int>(durationMs),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'fileSize': serializer.toJson<int>(fileSize),
      'waveformData': serializer.toJson<String?>(waveformData),
      'isPlaying': serializer.toJson<bool>(isPlaying),
      'playbackPositionMs': serializer.toJson<int>(playbackPositionMs),
      'status': serializer.toJson<String>(status),
      'remoteUrl': serializer.toJson<String?>(remoteUrl),
      'transcriptionId': serializer.toJson<int?>(transcriptionId),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  RecordsTableData copyWith(
          {String? id,
          String? title,
          String? filePath,
          int? durationMs,
          DateTime? createdAt,
          int? fileSize,
          Value<String?> waveformData = const Value.absent(),
          bool? isPlaying,
          int? playbackPositionMs,
          String? status,
          Value<String?> remoteUrl = const Value.absent(),
          Value<int?> transcriptionId = const Value.absent(),
          DateTime? updatedAt}) =>
      RecordsTableData(
        id: id ?? this.id,
        title: title ?? this.title,
        filePath: filePath ?? this.filePath,
        durationMs: durationMs ?? this.durationMs,
        createdAt: createdAt ?? this.createdAt,
        fileSize: fileSize ?? this.fileSize,
        waveformData:
            waveformData.present ? waveformData.value : this.waveformData,
        isPlaying: isPlaying ?? this.isPlaying,
        playbackPositionMs: playbackPositionMs ?? this.playbackPositionMs,
        status: status ?? this.status,
        remoteUrl: remoteUrl.present ? remoteUrl.value : this.remoteUrl,
        transcriptionId: transcriptionId.present
            ? transcriptionId.value
            : this.transcriptionId,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  RecordsTableData copyWithCompanion(RecordsTableCompanion data) {
    return RecordsTableData(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      filePath: data.filePath.present ? data.filePath.value : this.filePath,
      durationMs:
          data.durationMs.present ? data.durationMs.value : this.durationMs,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      fileSize: data.fileSize.present ? data.fileSize.value : this.fileSize,
      waveformData: data.waveformData.present
          ? data.waveformData.value
          : this.waveformData,
      isPlaying: data.isPlaying.present ? data.isPlaying.value : this.isPlaying,
      playbackPositionMs: data.playbackPositionMs.present
          ? data.playbackPositionMs.value
          : this.playbackPositionMs,
      status: data.status.present ? data.status.value : this.status,
      remoteUrl: data.remoteUrl.present ? data.remoteUrl.value : this.remoteUrl,
      transcriptionId: data.transcriptionId.present
          ? data.transcriptionId.value
          : this.transcriptionId,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('RecordsTableData(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('filePath: $filePath, ')
          ..write('durationMs: $durationMs, ')
          ..write('createdAt: $createdAt, ')
          ..write('fileSize: $fileSize, ')
          ..write('waveformData: $waveformData, ')
          ..write('isPlaying: $isPlaying, ')
          ..write('playbackPositionMs: $playbackPositionMs, ')
          ..write('status: $status, ')
          ..write('remoteUrl: $remoteUrl, ')
          ..write('transcriptionId: $transcriptionId, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      title,
      filePath,
      durationMs,
      createdAt,
      fileSize,
      waveformData,
      isPlaying,
      playbackPositionMs,
      status,
      remoteUrl,
      transcriptionId,
      updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is RecordsTableData &&
          other.id == this.id &&
          other.title == this.title &&
          other.filePath == this.filePath &&
          other.durationMs == this.durationMs &&
          other.createdAt == this.createdAt &&
          other.fileSize == this.fileSize &&
          other.waveformData == this.waveformData &&
          other.isPlaying == this.isPlaying &&
          other.playbackPositionMs == this.playbackPositionMs &&
          other.status == this.status &&
          other.remoteUrl == this.remoteUrl &&
          other.transcriptionId == this.transcriptionId &&
          other.updatedAt == this.updatedAt);
}

class RecordsTableCompanion extends UpdateCompanion<RecordsTableData> {
  final Value<String> id;
  final Value<String> title;
  final Value<String> filePath;
  final Value<int> durationMs;
  final Value<DateTime> createdAt;
  final Value<int> fileSize;
  final Value<String?> waveformData;
  final Value<bool> isPlaying;
  final Value<int> playbackPositionMs;
  final Value<String> status;
  final Value<String?> remoteUrl;
  final Value<int?> transcriptionId;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const RecordsTableCompanion({
    this.id = const Value.absent(),
    this.title = const Value.absent(),
    this.filePath = const Value.absent(),
    this.durationMs = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.waveformData = const Value.absent(),
    this.isPlaying = const Value.absent(),
    this.playbackPositionMs = const Value.absent(),
    this.status = const Value.absent(),
    this.remoteUrl = const Value.absent(),
    this.transcriptionId = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  RecordsTableCompanion.insert({
    required String id,
    required String title,
    required String filePath,
    required int durationMs,
    this.createdAt = const Value.absent(),
    required int fileSize,
    this.waveformData = const Value.absent(),
    this.isPlaying = const Value.absent(),
    this.playbackPositionMs = const Value.absent(),
    this.status = const Value.absent(),
    this.remoteUrl = const Value.absent(),
    this.transcriptionId = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        title = Value(title),
        filePath = Value(filePath),
        durationMs = Value(durationMs),
        fileSize = Value(fileSize);
  static Insertable<RecordsTableData> custom({
    Expression<String>? id,
    Expression<String>? title,
    Expression<String>? filePath,
    Expression<int>? durationMs,
    Expression<DateTime>? createdAt,
    Expression<int>? fileSize,
    Expression<String>? waveformData,
    Expression<bool>? isPlaying,
    Expression<int>? playbackPositionMs,
    Expression<String>? status,
    Expression<String>? remoteUrl,
    Expression<int>? transcriptionId,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (filePath != null) 'file_path': filePath,
      if (durationMs != null) 'duration_ms': durationMs,
      if (createdAt != null) 'created_at': createdAt,
      if (fileSize != null) 'file_size': fileSize,
      if (waveformData != null) 'waveform_data': waveformData,
      if (isPlaying != null) 'is_playing': isPlaying,
      if (playbackPositionMs != null)
        'playback_position_ms': playbackPositionMs,
      if (status != null) 'status': status,
      if (remoteUrl != null) 'remote_url': remoteUrl,
      if (transcriptionId != null) 'transcription_id': transcriptionId,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  RecordsTableCompanion copyWith(
      {Value<String>? id,
      Value<String>? title,
      Value<String>? filePath,
      Value<int>? durationMs,
      Value<DateTime>? createdAt,
      Value<int>? fileSize,
      Value<String?>? waveformData,
      Value<bool>? isPlaying,
      Value<int>? playbackPositionMs,
      Value<String>? status,
      Value<String?>? remoteUrl,
      Value<int?>? transcriptionId,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return RecordsTableCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      filePath: filePath ?? this.filePath,
      durationMs: durationMs ?? this.durationMs,
      createdAt: createdAt ?? this.createdAt,
      fileSize: fileSize ?? this.fileSize,
      waveformData: waveformData ?? this.waveformData,
      isPlaying: isPlaying ?? this.isPlaying,
      playbackPositionMs: playbackPositionMs ?? this.playbackPositionMs,
      status: status ?? this.status,
      remoteUrl: remoteUrl ?? this.remoteUrl,
      transcriptionId: transcriptionId ?? this.transcriptionId,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (filePath.present) {
      map['file_path'] = Variable<String>(filePath.value);
    }
    if (durationMs.present) {
      map['duration_ms'] = Variable<int>(durationMs.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (fileSize.present) {
      map['file_size'] = Variable<int>(fileSize.value);
    }
    if (waveformData.present) {
      map['waveform_data'] = Variable<String>(waveformData.value);
    }
    if (isPlaying.present) {
      map['is_playing'] = Variable<bool>(isPlaying.value);
    }
    if (playbackPositionMs.present) {
      map['playback_position_ms'] = Variable<int>(playbackPositionMs.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (remoteUrl.present) {
      map['remote_url'] = Variable<String>(remoteUrl.value);
    }
    if (transcriptionId.present) {
      map['transcription_id'] = Variable<int>(transcriptionId.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('RecordsTableCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('filePath: $filePath, ')
          ..write('durationMs: $durationMs, ')
          ..write('createdAt: $createdAt, ')
          ..write('fileSize: $fileSize, ')
          ..write('waveformData: $waveformData, ')
          ..write('isPlaying: $isPlaying, ')
          ..write('playbackPositionMs: $playbackPositionMs, ')
          ..write('status: $status, ')
          ..write('remoteUrl: $remoteUrl, ')
          ..write('transcriptionId: $transcriptionId, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $UsersTableTable usersTable = $UsersTableTable(this);
  late final $RecordsTableTable recordsTable = $RecordsTableTable(this);
  late final UsersDao usersDao = UsersDao(this as AppDatabase);
  late final RecordsDao recordsDao = RecordsDao(this as AppDatabase);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities =>
      [usersTable, recordsTable];
}

typedef $$UsersTableTableCreateCompanionBuilder = UsersTableCompanion Function({
  required String id,
  required String email,
  required String name,
  Value<String?> profilePicture,
  Value<bool> isEmailVerified,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$UsersTableTableUpdateCompanionBuilder = UsersTableCompanion Function({
  Value<String> id,
  Value<String> email,
  Value<String> name,
  Value<String?> profilePicture,
  Value<bool> isEmailVerified,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$UsersTableTableFilterComposer
    extends Composer<_$AppDatabase, $UsersTableTable> {
  $$UsersTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get profilePicture => $composableBuilder(
      column: $table.profilePicture,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isEmailVerified => $composableBuilder(
      column: $table.isEmailVerified,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$UsersTableTableOrderingComposer
    extends Composer<_$AppDatabase, $UsersTableTable> {
  $$UsersTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get profilePicture => $composableBuilder(
      column: $table.profilePicture,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isEmailVerified => $composableBuilder(
      column: $table.isEmailVerified,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$UsersTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $UsersTableTable> {
  $$UsersTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get email =>
      $composableBuilder(column: $table.email, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get profilePicture => $composableBuilder(
      column: $table.profilePicture, builder: (column) => column);

  GeneratedColumn<bool> get isEmailVerified => $composableBuilder(
      column: $table.isEmailVerified, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$UsersTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $UsersTableTable,
    UserTableData,
    $$UsersTableTableFilterComposer,
    $$UsersTableTableOrderingComposer,
    $$UsersTableTableAnnotationComposer,
    $$UsersTableTableCreateCompanionBuilder,
    $$UsersTableTableUpdateCompanionBuilder,
    (
      UserTableData,
      BaseReferences<_$AppDatabase, $UsersTableTable, UserTableData>
    ),
    UserTableData,
    PrefetchHooks Function()> {
  $$UsersTableTableTableManager(_$AppDatabase db, $UsersTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$UsersTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$UsersTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$UsersTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> email = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String?> profilePicture = const Value.absent(),
            Value<bool> isEmailVerified = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UsersTableCompanion(
            id: id,
            email: email,
            name: name,
            profilePicture: profilePicture,
            isEmailVerified: isEmailVerified,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String email,
            required String name,
            Value<String?> profilePicture = const Value.absent(),
            Value<bool> isEmailVerified = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UsersTableCompanion.insert(
            id: id,
            email: email,
            name: name,
            profilePicture: profilePicture,
            isEmailVerified: isEmailVerified,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$UsersTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $UsersTableTable,
    UserTableData,
    $$UsersTableTableFilterComposer,
    $$UsersTableTableOrderingComposer,
    $$UsersTableTableAnnotationComposer,
    $$UsersTableTableCreateCompanionBuilder,
    $$UsersTableTableUpdateCompanionBuilder,
    (
      UserTableData,
      BaseReferences<_$AppDatabase, $UsersTableTable, UserTableData>
    ),
    UserTableData,
    PrefetchHooks Function()>;
typedef $$RecordsTableTableCreateCompanionBuilder = RecordsTableCompanion
    Function({
  required String id,
  required String title,
  required String filePath,
  required int durationMs,
  Value<DateTime> createdAt,
  required int fileSize,
  Value<String?> waveformData,
  Value<bool> isPlaying,
  Value<int> playbackPositionMs,
  Value<String> status,
  Value<String?> remoteUrl,
  Value<int?> transcriptionId,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$RecordsTableTableUpdateCompanionBuilder = RecordsTableCompanion
    Function({
  Value<String> id,
  Value<String> title,
  Value<String> filePath,
  Value<int> durationMs,
  Value<DateTime> createdAt,
  Value<int> fileSize,
  Value<String?> waveformData,
  Value<bool> isPlaying,
  Value<int> playbackPositionMs,
  Value<String> status,
  Value<String?> remoteUrl,
  Value<int?> transcriptionId,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$RecordsTableTableFilterComposer
    extends Composer<_$AppDatabase, $RecordsTableTable> {
  $$RecordsTableTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get filePath => $composableBuilder(
      column: $table.filePath, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get durationMs => $composableBuilder(
      column: $table.durationMs, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get fileSize => $composableBuilder(
      column: $table.fileSize, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get waveformData => $composableBuilder(
      column: $table.waveformData, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isPlaying => $composableBuilder(
      column: $table.isPlaying, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get playbackPositionMs => $composableBuilder(
      column: $table.playbackPositionMs,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get remoteUrl => $composableBuilder(
      column: $table.remoteUrl, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get transcriptionId => $composableBuilder(
      column: $table.transcriptionId,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$RecordsTableTableOrderingComposer
    extends Composer<_$AppDatabase, $RecordsTableTable> {
  $$RecordsTableTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get filePath => $composableBuilder(
      column: $table.filePath, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get durationMs => $composableBuilder(
      column: $table.durationMs, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get fileSize => $composableBuilder(
      column: $table.fileSize, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get waveformData => $composableBuilder(
      column: $table.waveformData,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isPlaying => $composableBuilder(
      column: $table.isPlaying, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get playbackPositionMs => $composableBuilder(
      column: $table.playbackPositionMs,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get remoteUrl => $composableBuilder(
      column: $table.remoteUrl, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get transcriptionId => $composableBuilder(
      column: $table.transcriptionId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$RecordsTableTableAnnotationComposer
    extends Composer<_$AppDatabase, $RecordsTableTable> {
  $$RecordsTableTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<String> get filePath =>
      $composableBuilder(column: $table.filePath, builder: (column) => column);

  GeneratedColumn<int> get durationMs => $composableBuilder(
      column: $table.durationMs, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<int> get fileSize =>
      $composableBuilder(column: $table.fileSize, builder: (column) => column);

  GeneratedColumn<String> get waveformData => $composableBuilder(
      column: $table.waveformData, builder: (column) => column);

  GeneratedColumn<bool> get isPlaying =>
      $composableBuilder(column: $table.isPlaying, builder: (column) => column);

  GeneratedColumn<int> get playbackPositionMs => $composableBuilder(
      column: $table.playbackPositionMs, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<String> get remoteUrl =>
      $composableBuilder(column: $table.remoteUrl, builder: (column) => column);

  GeneratedColumn<int> get transcriptionId => $composableBuilder(
      column: $table.transcriptionId, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$RecordsTableTableTableManager extends RootTableManager<
    _$AppDatabase,
    $RecordsTableTable,
    RecordsTableData,
    $$RecordsTableTableFilterComposer,
    $$RecordsTableTableOrderingComposer,
    $$RecordsTableTableAnnotationComposer,
    $$RecordsTableTableCreateCompanionBuilder,
    $$RecordsTableTableUpdateCompanionBuilder,
    (
      RecordsTableData,
      BaseReferences<_$AppDatabase, $RecordsTableTable, RecordsTableData>
    ),
    RecordsTableData,
    PrefetchHooks Function()> {
  $$RecordsTableTableTableManager(_$AppDatabase db, $RecordsTableTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$RecordsTableTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$RecordsTableTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$RecordsTableTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<String> filePath = const Value.absent(),
            Value<int> durationMs = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> fileSize = const Value.absent(),
            Value<String?> waveformData = const Value.absent(),
            Value<bool> isPlaying = const Value.absent(),
            Value<int> playbackPositionMs = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<String?> remoteUrl = const Value.absent(),
            Value<int?> transcriptionId = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              RecordsTableCompanion(
            id: id,
            title: title,
            filePath: filePath,
            durationMs: durationMs,
            createdAt: createdAt,
            fileSize: fileSize,
            waveformData: waveformData,
            isPlaying: isPlaying,
            playbackPositionMs: playbackPositionMs,
            status: status,
            remoteUrl: remoteUrl,
            transcriptionId: transcriptionId,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String title,
            required String filePath,
            required int durationMs,
            Value<DateTime> createdAt = const Value.absent(),
            required int fileSize,
            Value<String?> waveformData = const Value.absent(),
            Value<bool> isPlaying = const Value.absent(),
            Value<int> playbackPositionMs = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<String?> remoteUrl = const Value.absent(),
            Value<int?> transcriptionId = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              RecordsTableCompanion.insert(
            id: id,
            title: title,
            filePath: filePath,
            durationMs: durationMs,
            createdAt: createdAt,
            fileSize: fileSize,
            waveformData: waveformData,
            isPlaying: isPlaying,
            playbackPositionMs: playbackPositionMs,
            status: status,
            remoteUrl: remoteUrl,
            transcriptionId: transcriptionId,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$RecordsTableTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $RecordsTableTable,
    RecordsTableData,
    $$RecordsTableTableFilterComposer,
    $$RecordsTableTableOrderingComposer,
    $$RecordsTableTableAnnotationComposer,
    $$RecordsTableTableCreateCompanionBuilder,
    $$RecordsTableTableUpdateCompanionBuilder,
    (
      RecordsTableData,
      BaseReferences<_$AppDatabase, $RecordsTableTable, RecordsTableData>
    ),
    RecordsTableData,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$UsersTableTableTableManager get usersTable =>
      $$UsersTableTableTableManager(_db, _db.usersTable);
  $$RecordsTableTableTableManager get recordsTable =>
      $$RecordsTableTableTableManager(_db, _db.recordsTable);
}
