import 'package:drift/drift.dart';
import 'package:glidic_app/core/data/datasources/database/app_database.dart';
import 'package:glidic_app/core/data/datasources/database/tables/users_table.dart';

part 'users_dao.g.dart';

/// Data Access Object for Users table
/// Provides methods for CRUD operations on user data
@DriftAccessor(tables: [UsersTable])
class UsersDao extends DatabaseAccessor<AppDatabase> with _$UsersDaoMixin {
  /// Constructor
  UsersDao(super.db);

  /// Get all users
  Future<List<UserTableData>> getAllUsers() => select(usersTable).get();

  /// Get user by ID
  Future<UserTableData?> getUserById(String id) {
    return (select(usersTable)..where((tbl) => tbl.id.equals(id)))
        .getSingleOrNull();
  }

  /// Get user by email
  Future<UserTableData?> getUserByEmail(String email) {
    return (select(usersTable)..where((tbl) => tbl.email.equals(email)))
        .getSingleOrNull();
  }

  /// Insert or update user
  Future<int> insertOrUpdateUser(UsersTableCompanion user) {
    return into(usersTable).insertOnConflictUpdate(user);
  }

  /// Insert user
  Future<int> insertUser(UsersTableCompanion user) {
    return into(usersTable).insert(user);
  }

  /// Update user
  Future<bool> updateUser(UserTableData user) {
    return update(usersTable).replace(user);
  }

  /// Update user by ID
  Future<int> updateUserById(String id, UsersTableCompanion user) {
    return (update(usersTable)..where((tbl) => tbl.id.equals(id))).write(user);
  }

  /// Delete user by ID
  Future<int> deleteUserById(String id) {
    return (delete(usersTable)..where((tbl) => tbl.id.equals(id))).go();
  }

  /// Delete all users
  Future<int> deleteAllUsers() {
    return delete(usersTable).go();
  }

  /// Check if user exists by email
  Future<bool> userExistsByEmail(String email) async {
    final user = await getUserByEmail(email);
    return user != null;
  }

  /// Check if user exists by ID
  Future<bool> userExistsById(String id) async {
    final user = await getUserById(id);
    return user != null;
  }

  /// Get users with verified emails
  Future<List<UserTableData>> getVerifiedUsers() {
    return (select(usersTable)
          ..where((tbl) => tbl.isEmailVerified.equals(true)))
        .get();
  }

  /// Get users with unverified emails
  Future<List<UserTableData>> getUnverifiedUsers() {
    return (select(usersTable)
          ..where((tbl) => tbl.isEmailVerified.equals(false)))
        .get();
  }

  /// Update email verification status
  Future<int> updateEmailVerificationStatus(String id, bool isVerified) {
    return (update(usersTable)..where((tbl) => tbl.id.equals(id))).write(
      UsersTableCompanion(
        isEmailVerified: Value(isVerified),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  /// Search users by name or email
  Future<List<UserTableData>> searchUsers(String query) {
    final searchQuery = '%$query%';
    return (select(usersTable)
          ..where(
            (tbl) => tbl.name.like(searchQuery) | tbl.email.like(searchQuery),
          ))
        .get();
  }

  /// Get users created after a specific date
  Future<List<UserTableData>> getUsersCreatedAfter(DateTime date) {
    return (select(usersTable)
          ..where((tbl) => tbl.createdAt.isBiggerThanValue(date)))
        .get();
  }

  /// Get users count
  Future<int> getUsersCount() async {
    final countQuery = selectOnly(usersTable)
      ..addColumns([usersTable.id.count()]);
    final result = await countQuery.getSingle();
    return result.read(usersTable.id.count()) ?? 0;
  }

  // ========== STREAM METHODS FOR REAL-TIME DATA UPDATES ==========

  /// Watch all users - returns a stream that emits whenever users data changes
  /// This stream will automatically emit new data when any user is added, updated, or deleted
  Stream<List<UserTableData>> watchAllUsers() {
    try {
      return select(usersTable).watch();
    } catch (e) {
      // Return an error stream if something goes wrong
      return Stream.error(
        Exception('Failed to watch all users: ${e.toString()}'),
      );
    }
  }

  /// Watch a specific user by ID - returns a stream that emits whenever the user data changes
  /// Returns null if user doesn't exist
  /// [id] The unique identifier of the user to watch
  Stream<UserTableData?> watchUserById(String id) {
    try {
      return (select(usersTable)..where((tbl) => tbl.id.equals(id)))
          .watchSingleOrNull();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch user by ID: ${e.toString()}'),
      );
    }
  }

  /// Watch a specific user by email - returns a stream that emits whenever the user data changes
  /// Returns null if user doesn't exist
  /// [email] The email address of the user to watch
  Stream<UserTableData?> watchUserByEmail(String email) {
    try {
      return (select(usersTable)..where((tbl) => tbl.email.equals(email)))
          .watchSingleOrNull();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch user by email: ${e.toString()}'),
      );
    }
  }

  /// Watch users with verified emails - returns a stream that emits whenever verified users change
  Stream<List<UserTableData>> watchVerifiedUsers() {
    try {
      return (select(usersTable)
            ..where((tbl) => tbl.isEmailVerified.equals(true)))
          .watch();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch verified users: ${e.toString()}'),
      );
    }
  }

  /// Watch users with unverified emails - returns a stream that emits whenever unverified users change
  Stream<List<UserTableData>> watchUnverifiedUsers() {
    try {
      return (select(usersTable)
            ..where((tbl) => tbl.isEmailVerified.equals(false)))
          .watch();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch unverified users: ${e.toString()}'),
      );
    }
  }

  /// Watch users count - returns a stream that emits whenever the total number of users changes
  Stream<int> watchUsersCount() {
    try {
      final countQuery = selectOnly(usersTable)
        ..addColumns([usersTable.id.count()]);

      return countQuery.watchSingle().map((row) {
        return row.read(usersTable.id.count()) ?? 0;
      });
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch users count: ${e.toString()}'),
      );
    }
  }

  /// Watch users created after a specific date - returns a stream that emits when matching users change
  /// [date] The date threshold - only users created after this date will be included
  Stream<List<UserTableData>> watchUsersCreatedAfter(DateTime date) {
    try {
      return (select(usersTable)
            ..where((tbl) => tbl.createdAt.isBiggerThanValue(date)))
          .watch();
    } catch (e) {
      return Stream.error(
        Exception(
          'Failed to watch users created after date: ${e.toString()}',
        ),
      );
    }
  }

  /// Search users by name or email - returns a stream that emits when matching users change
  /// [query] The search query to match against user name or email
  Stream<List<UserTableData>> watchSearchUsers(String query) {
    try {
      final searchQuery = '%$query%';
      return (select(usersTable)
            ..where(
              (tbl) => tbl.name.like(searchQuery) | tbl.email.like(searchQuery),
            ))
          .watch();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch search users: ${e.toString()}'),
      );
    }
  }
}
