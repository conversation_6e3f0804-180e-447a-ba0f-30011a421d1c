import 'dart:convert';
import 'dart:developer';

import 'package:drift/drift.dart';
import 'package:glidic_app/core/data/datasources/database/app_database.dart';
import 'package:glidic_app/core/data/datasources/database/tables/records_table.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_status.dart';

part 'records_dao.g.dart';

/// Data Access Object for Records table
/// Provides methods for CRUD operations on record data
@DriftAccessor(tables: [RecordsTable])
class RecordsDao extends DatabaseAccessor<AppDatabase> with _$RecordsDaoMixin {
  /// Constructor
  RecordsDao(super.db);

  /// Get all records ordered by creation date (newest first)
  Future<List<RecordsTableData>> getAllRecords() {
    return (select(recordsTable)
          ..orderBy([(tbl) => OrderingTerm.desc(tbl.createdAt)]))
        .get();
  }

  /// Get record by ID
  Future<RecordsTableData?> getRecordById(String id) {
    return (select(recordsTable)..where((tbl) => tbl.id.equals(id)))
        .getSingleOrNull();
  }

  /// Insert a new record
  Future<int> insertRecord(RecordsTableData record) {
    return into(recordsTable).insert(record.toCompanion(false));
  }

  /// Insert record with companion
  Future<int> insertRecordCompanion(RecordsTableCompanion record) {
    return into(recordsTable).insert(record);
  }

  /// Update record
  Future<bool> updateRecord(RecordsTableData record) {
    return update(recordsTable).replace(record);
  }

  /// Update record by ID
  Future<int> updateRecordById(
    String id,
    RecordsTableCompanion record,
  ) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id)))
        .write(record);
  }

  /// Delete record by ID
  Future<int> deleteRecordById(String id) {
    return (delete(recordsTable)..where((tbl) => tbl.id.equals(id))).go();
  }

  /// Delete all records
  Future<int> deleteAllRecords() {
    return delete(recordsTable).go();
  }

  /// Search records by title
  Future<List<RecordsTableData>> searchRecords(String query) {
    final searchQuery = '%$query%';
    return (select(recordsTable)
          ..where((tbl) => tbl.title.like(searchQuery))
          ..orderBy([(tbl) => OrderingTerm.desc(tbl.createdAt)]))
        .get();
  }

  /// Get records count
  Future<int> getRecordsCount() async {
    final countQuery = selectOnly(recordsTable)
      ..addColumns([recordsTable.id.count()]);
    final result = await countQuery.getSingle();
    return result.read(recordsTable.id.count()) ?? 0;
  }

  /// Get total file size of all records
  Future<int> getTotalFileSize() async {
    final sumQuery = selectOnly(recordsTable)
      ..addColumns([recordsTable.fileSize.sum()]);
    final result = await sumQuery.getSingle();
    return result.read(recordsTable.fileSize.sum()) ?? 0;
  }

  /// Update playback state
  Future<int> updatePlaybackState(String id, bool isPlaying, int positionMs) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id))).write(
      RecordsTableCompanion(
        isPlaying: Value(isPlaying),
        playbackPositionMs: Value(positionMs),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  /// Clear all playback states (set all records to not playing)
  Future<int> clearAllPlaybackStates() {
    return update(recordsTable).write(
      const RecordsTableCompanion(
        isPlaying: Value(false),
        playbackPositionMs: Value(0),
      ),
    );
  }

  // ========== STREAM METHODS FOR REAL-TIME DATA UPDATES ==========

  /// Watch all records - returns a stream that emits whenever records data changes
  Stream<List<RecordsTableData>> watchAllRecordings() {
    try {
      return (select(recordsTable)
            ..orderBy([(tbl) => OrderingTerm.desc(tbl.createdAt)]))
          .watch();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch all records: ${e.toString()}'),
      );
    }
  }

  /// Watch record by ID
  Stream<RecordsTableData?> watchRecordingById(String id) {
    try {
      return (select(recordsTable)..where((tbl) => tbl.id.equals(id)))
          .watchSingleOrNull();
    } catch (e) {
      return Stream.error(
        Exception('Failed to watch record $id: ${e.toString()}'),
      );
    }
  }

  // ========== CONVERSION METHODS ==========
  /// Convert RecordItem entity to RecordsTableCompanion
  RecordsTableCompanion fromEntity(RecordItem item) {
    String? waveformDataJson;
    if (item.waveformData != null) {
      try {
        waveformDataJson = jsonEncode(item.waveformData);
      } catch (e) {
        log('[RecordingsDao] Error encoding waveform data: $e');
        waveformDataJson = null;
      }
    }

    return RecordsTableCompanion(
      id: Value(item.id),
      title: Value(item.title),
      filePath: Value(item.filePath),
      durationMs: Value(item.duration.inMilliseconds),
      createdAt: Value(item.createdAt),
      fileSize: Value(item.fileSize),
      waveformData: Value(waveformDataJson),
      isPlaying: Value(item.isPlaying),
      playbackPositionMs: Value(item.playbackPosition.inMilliseconds),
      status: Value(item.status.name),
      remoteUrl: Value(item.remoteUrl),
      transcriptionId: Value(item.transcriptionId),
      updatedAt: Value(DateTime.now()),
    );
  }

  // ========== SIMPLIFIED UPLOAD STATUS METHODS ==========

  /// Get records with pending upload status
  Future<List<RecordsTableData>> getPendingUploads() {
    return (select(recordsTable)
          ..where((tbl) => tbl.status.equals('pending'))
          ..orderBy([(tbl) => OrderingTerm.asc(tbl.createdAt)]))
        .get();
  }

  /// Get records currently uploading
  Future<List<RecordsTableData>> getUploadingRecordings() {
    return (select(recordsTable)
          ..where((tbl) => tbl.status.equals('uploading')))
        .get();
  }

  /// Update upload status
  Future<int> updateStatus(String id, UploadStatus status) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id))).write(
      RecordsTableCompanion(
        status: Value(status.name),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  /// Start upload (set status to uploading)
  Future<int> startUpload(String id) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id))).write(
      RecordsTableCompanion(
        status: Value(UploadStatus.uploading.name),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  /// Complete upload (set status to finished and record remote URL)
  Future<int> completeUpload(String id, String remoteUrl) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id))).write(
      RecordsTableCompanion(
        status: Value(UploadStatus.finished.name),
        remoteUrl: Value(remoteUrl),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  /// Fail upload (set status to failed)
  Future<int> failUpload(String id) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id))).write(
      RecordsTableCompanion(
        status: Value(UploadStatus.failed.name),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  /// Reset upload status to pending (for retry)
  Future<int> resetUploadStatus(String id) {
    return (update(recordsTable)..where((tbl) => tbl.id.equals(id))).write(
      RecordsTableCompanion(
        status: Value(UploadStatus.pending.name),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }
}
