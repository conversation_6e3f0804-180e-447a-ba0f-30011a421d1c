import 'package:drift/drift.dart';
import 'package:glidic_app/core/common/constants/database_constants.dart';

/// Users table definition for Drift database
/// Stores user account information and profile data
@DataClassName('UserTableData')
class UsersTable extends Table {
  @override
  String get tableName => 'users';

  /// Primary key - unique identifier for each user
  TextColumn get id => text().named('id')();

  /// User's email address - unique and required
  TextColumn get email => text()
      .named('email')
      .withLength(max: DatabaseConstants.maxEmailLength)
      .unique()();

  /// User's display name
  TextColumn get name =>
      text().named('name').withLength(max: DatabaseConstants.maxNameLength)();

  /// URL to user's profile picture
  TextColumn get profilePicture => text()
      .named('profile_picture')
      .withLength(max: DatabaseConstants.maxUrlLength)
      .nullable()();

  /// Whether the user's email has been verified
  BoolColumn get isEmailVerified =>
      boolean().named('is_email_verified').withDefault(const Constant(false))();

  /// Timestamp when the user record was created
  DateTimeColumn get createdAt =>
      dateTime().named('created_at').withDefault(currentDateAndTime)();

  /// Timestamp when the user record was last updated
  DateTimeColumn get updatedAt =>
      dateTime().named('updated_at').withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {id};
}
