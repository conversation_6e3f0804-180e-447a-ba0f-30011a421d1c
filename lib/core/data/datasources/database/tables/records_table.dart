import 'package:drift/drift.dart';
import 'package:glidic_app/core/common/constants/database_constants.dart';

/// Records table definition for Drift database
/// Stores audio record metadata and waveform data
@DataClassName('RecordsTableData')
class RecordsTable extends Table {
  @override
  String get tableName => 'records';

  /// Primary key - unique identifier for each record
  TextColumn get id => text().named('id')();

  /// Display title of the record
  TextColumn get title =>
      text().named('title').withLength(max: DatabaseConstants.maxTitleLength)();

  /// File path where the record is stored
  TextColumn get filePath => text()
      .named('file_path')
      .withLength(max: DatabaseConstants.maxFilePathLength)();

  /// Duration of the record in milliseconds
  IntColumn get durationMs => integer().named('duration_ms')();

  /// When the record was created
  DateTimeColumn get createdAt =>
      dateTime().named('created_at').withDefault(currentDateAndTime)();

  /// File size in bytes
  IntColumn get fileSize => integer().named('file_size')();

  /// Waveform data for visualization (stored as JSON string)
  TextColumn get waveformData => text().named('waveform_data').nullable()();

  /// Whether the record is currently playing (runtime state)
  BoolColumn get isPlaying =>
      boolean().named('is_playing').withDefault(const Constant(false))();

  /// Current playback position in milliseconds (runtime state)
  IntColumn get playbackPositionMs =>
      integer().named('playback_position_ms').withDefault(const Constant(0))();

  /// Upload status: pending, uploading, finished
  TextColumn get status =>
      text().named('status').withDefault(const Constant('pending'))();

  /// Remote URL after successful upload
  TextColumn get remoteUrl => text().named('remote_url').nullable()();

  /// Transcription ID from transcription service
  IntColumn get transcriptionId =>
      integer().named('transcription_id').nullable()();

  /// Timestamp when the record was last updated
  DateTimeColumn get updatedAt =>
      dateTime().named('updated_at').withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<String> get customConstraints => [
        // Ensure duration is positive
        'CHECK (duration_ms >= 0)',
        // Ensure file size is positive
        'CHECK (file_size >= 0)',
        // Ensure playback position is not negative
        'CHECK (playback_position_ms >= 0)',
        // Ensure status is valid
        'CHECK (status IN (\'pending\', \'uploading\', \'finished\', \'failed\', \'cancelled\'))',
      ];
}
