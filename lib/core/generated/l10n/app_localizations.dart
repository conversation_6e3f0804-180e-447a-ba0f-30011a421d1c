import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ja.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ja')
  ];

  /// Welcome message displayed to users
  ///
  /// In en, this message translates to:
  /// **'Welcome back!'**
  String get welcome;

  /// Section title for quick actions
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// Button text for adding new items
  ///
  /// In en, this message translates to:
  /// **'Add New'**
  String get addNew;

  /// Button text for settings
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Message shown for features under development
  ///
  /// In en, this message translates to:
  /// **'Feature coming soon'**
  String get featureComingSoon;

  /// Retry button text
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Message when no data is available
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noDataAvailable;

  /// Pull to refresh instruction
  ///
  /// In en, this message translates to:
  /// **'Pull to refresh'**
  String get pullToRefresh;

  /// Error message for failed login attempt
  ///
  /// In en, this message translates to:
  /// **'Login failed. Please check your credentials.'**
  String get loginFailed;

  /// Generic error message for unexpected errors
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again.'**
  String get unexpectedError;

  /// Loading message for dashboard data
  ///
  /// In en, this message translates to:
  /// **'Loading dashboard data...'**
  String get loadingDashboardData;

  /// Error message when dashboard fails to load
  ///
  /// In en, this message translates to:
  /// **'Failed to load dashboard'**
  String get failedToLoadDashboard;

  /// Message shown when refreshing data
  ///
  /// In en, this message translates to:
  /// **'Refreshing...'**
  String get refreshing;

  /// Error message when refresh fails
  ///
  /// In en, this message translates to:
  /// **'Failed to refresh: {error}'**
  String failedToRefresh(String error);

  /// Welcome message subtitle on dashboard
  ///
  /// In en, this message translates to:
  /// **'Here\'s what\'s happening with your dashboard today.'**
  String get welcomeMessage;

  /// Message for forgot password feature under development
  ///
  /// In en, this message translates to:
  /// **'Forgot password feature coming soon'**
  String get forgotPasswordFeatureComingSoon;

  /// Placeholder text for email input field
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailPlaceholder;

  /// Placeholder text for password input field
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordPlaceholder;

  /// Text for login button
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginButton;

  /// Text for sign up button
  ///
  /// In en, this message translates to:
  /// **'Create New Account'**
  String get signUpButton;

  /// Text for forgot password link
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPasswordLink;

  /// Message for sign up feature under development
  ///
  /// In en, this message translates to:
  /// **'Sign up feature coming soon'**
  String get signUpFeatureComingSoon;

  /// Action to delete an item.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get deleteAction;

  /// Action to save an item.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get saveAction;

  /// Remaining quantity or time
  ///
  /// In en, this message translates to:
  /// **'rem.'**
  String get remaining;

  /// Minute unit label
  ///
  /// In en, this message translates to:
  /// **'m'**
  String get minute;

  /// Import audio button text
  ///
  /// In en, this message translates to:
  /// **'Import Audio'**
  String get importAudio;

  /// Start recording button text
  ///
  /// In en, this message translates to:
  /// **'Start Recording'**
  String get startRecording;

  /// All files button text
  ///
  /// In en, this message translates to:
  /// **'All Files'**
  String get allFiles;

  /// Button text to cancel recording
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get recordingCancel;

  /// Button text to delete recording
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get recordingDelete;

  /// Title for delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Delete Recording'**
  String get recordingConfirmDelete;

  /// Message for delete confirmation dialog
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this recording? This action cannot be undone.'**
  String get recordingConfirmDeleteMessage;

  /// Error when recording fails to start
  ///
  /// In en, this message translates to:
  /// **'Failed to start recording. Please check your device and try again.'**
  String get errorRecordingStartFailed;

  /// Error when recording fails to save
  ///
  /// In en, this message translates to:
  /// **'Failed to save recording: {error}'**
  String errorRecordingSaveFailed(String error);

  /// Error when recording fails to delete
  ///
  /// In en, this message translates to:
  /// **'Failed to delete recording: {error}'**
  String errorRecordingDeleteFailed(String error);

  /// Error message for microphone permission requirement
  ///
  /// In en, this message translates to:
  /// **'Microphone access is needed to record audio. Please check your device settings.'**
  String get errorRecordingPermissionRequired;

  /// Error when insufficient storage space for recording
  ///
  /// In en, this message translates to:
  /// **'Not enough storage space to record. Please free up some space.'**
  String get errorRecordingStorageSpace;

  /// Error when audio hardware is not available
  ///
  /// In en, this message translates to:
  /// **'Audio recording is not available. Please check your device settings.'**
  String get errorRecordingHardware;

  /// Generic device check error for recording
  ///
  /// In en, this message translates to:
  /// **'Unable to start recording. Please check your device and try again.'**
  String get errorRecordingDeviceCheck;

  /// Button text to open device settings
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get permissionOpenSettings;

  /// Status text when upload is in progress
  ///
  /// In en, this message translates to:
  /// **'Uploading...'**
  String get uploadInProgress;

  /// Status text when upload is completed
  ///
  /// In en, this message translates to:
  /// **'Upload completed'**
  String get uploadCompleted;

  /// Status text when upload fails
  ///
  /// In en, this message translates to:
  /// **'Upload failed'**
  String get uploadFailed;

  /// Status text when upload is cancelled
  ///
  /// In en, this message translates to:
  /// **'Upload cancelled'**
  String get uploadCancelled;

  /// Upload progress display
  ///
  /// In en, this message translates to:
  /// **'Uploading: {progress}%'**
  String uploadProgress(int progress);

  /// Generic upload error
  ///
  /// In en, this message translates to:
  /// **'Upload error occurred'**
  String get errorUploadGeneric;

  /// Error when network is not available for upload
  ///
  /// In en, this message translates to:
  /// **'Network connection not available for upload'**
  String get errorUploadNetworkUnavailable;

  /// Error when upload server is not available
  ///
  /// In en, this message translates to:
  /// **'Upload server is not available'**
  String get errorUploadServerUnavailable;

  /// Error when file is too large for upload
  ///
  /// In en, this message translates to:
  /// **'File size exceeds upload limit'**
  String get errorUploadFileSizeExceeded;

  /// Error when upload authentication fails
  ///
  /// In en, this message translates to:
  /// **'Upload authentication failed'**
  String get errorUploadAuthenticationFailed;

  /// Message when upload worker starts
  ///
  /// In en, this message translates to:
  /// **'Upload worker started'**
  String get uploadWorkerStarted;

  /// Message when upload worker stops
  ///
  /// In en, this message translates to:
  /// **'Upload worker stopped'**
  String get uploadWorkerStopped;

  /// Button text for transcription
  ///
  /// In en, this message translates to:
  /// **'Transcription'**
  String get transcription;

  /// Button text for AI Summary
  ///
  /// In en, this message translates to:
  /// **'AI Summary'**
  String get aiSummary;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'ja'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'ja':
      return AppLocalizationsJa();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
