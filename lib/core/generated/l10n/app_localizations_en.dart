// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get welcome => 'Welcome back!';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get addNew => 'Add New';

  @override
  String get settings => 'Settings';

  @override
  String get featureComingSoon => 'Feature coming soon';

  @override
  String get retry => 'Retry';

  @override
  String get noDataAvailable => 'No data available';

  @override
  String get pullToRefresh => 'Pull to refresh';

  @override
  String get loginFailed => 'Login failed. Please check your credentials.';

  @override
  String get unexpectedError =>
      'An unexpected error occurred. Please try again.';

  @override
  String get loadingDashboardData => 'Loading dashboard data...';

  @override
  String get failedToLoadDashboard => 'Failed to load dashboard';

  @override
  String get refreshing => 'Refreshing...';

  @override
  String failedToRefresh(String error) {
    return 'Failed to refresh: $error';
  }

  @override
  String get welcomeMessage =>
      'Here\'s what\'s happening with your dashboard today.';

  @override
  String get forgotPasswordFeatureComingSoon =>
      'Forgot password feature coming soon';

  @override
  String get emailPlaceholder => 'Email Address';

  @override
  String get passwordPlaceholder => 'Password';

  @override
  String get loginButton => 'Login';

  @override
  String get signUpButton => 'Create New Account';

  @override
  String get forgotPasswordLink => 'Forgot Password?';

  @override
  String get signUpFeatureComingSoon => 'Sign up feature coming soon';

  @override
  String get deleteAction => 'Delete';

  @override
  String get saveAction => 'Save';

  @override
  String get remaining => 'rem.';

  @override
  String get minute => 'm';

  @override
  String get importAudio => 'Import Audio';

  @override
  String get startRecording => 'Start Recording';

  @override
  String get allFiles => 'All Files';

  @override
  String get recordingCancel => 'Cancel';

  @override
  String get recordingDelete => 'Delete';

  @override
  String get recordingConfirmDelete => 'Delete Recording';

  @override
  String get recordingConfirmDeleteMessage =>
      'Are you sure you want to delete this recording? This action cannot be undone.';

  @override
  String get errorRecordingStartFailed =>
      'Failed to start recording. Please check your device and try again.';

  @override
  String errorRecordingSaveFailed(String error) {
    return 'Failed to save recording: $error';
  }

  @override
  String errorRecordingDeleteFailed(String error) {
    return 'Failed to delete recording: $error';
  }

  @override
  String get errorRecordingPermissionRequired =>
      'Microphone access is needed to record audio. Please check your device settings.';

  @override
  String get errorRecordingStorageSpace =>
      'Not enough storage space to record. Please free up some space.';

  @override
  String get errorRecordingHardware =>
      'Audio recording is not available. Please check your device settings.';

  @override
  String get errorRecordingDeviceCheck =>
      'Unable to start recording. Please check your device and try again.';

  @override
  String get permissionOpenSettings => 'Open Settings';

  @override
  String get uploadInProgress => 'Uploading...';

  @override
  String get uploadCompleted => 'Upload completed';

  @override
  String get uploadFailed => 'Upload failed';

  @override
  String get uploadCancelled => 'Upload cancelled';

  @override
  String uploadProgress(int progress) {
    return 'Uploading: $progress%';
  }

  @override
  String get errorUploadGeneric => 'Upload error occurred';

  @override
  String get errorUploadNetworkUnavailable =>
      'Network connection not available for upload';

  @override
  String get errorUploadServerUnavailable => 'Upload server is not available';

  @override
  String get errorUploadFileSizeExceeded => 'File size exceeds upload limit';

  @override
  String get errorUploadAuthenticationFailed => 'Upload authentication failed';

  @override
  String get uploadWorkerStarted => 'Upload worker started';

  @override
  String get uploadWorkerStopped => 'Upload worker stopped';

  @override
  String get transcription => 'Transcription';

  @override
  String get aiSummary => 'AI Summary';
}
