// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get welcome => 'おかえりなさい！';

  @override
  String get quickActions => 'クイックアクション';

  @override
  String get addNew => '新規追加';

  @override
  String get settings => '設定';

  @override
  String get featureComingSoon => '機能は近日公開予定です';

  @override
  String get retry => '再試行';

  @override
  String get noDataAvailable => 'データがありません';

  @override
  String get pullToRefresh => '引っ張って更新';

  @override
  String get loginFailed => 'ログインに失敗しました。認証情報を確認してください。';

  @override
  String get unexpectedError => '予期しないエラーが発生しました。もう一度お試しください。';

  @override
  String get loadingDashboardData => 'ダッシュボードデータを読み込み中...';

  @override
  String get failedToLoadDashboard => 'ダッシュボードの読み込みに失敗しました';

  @override
  String get refreshing => '更新中...';

  @override
  String failedToRefresh(String error) {
    return '更新に失敗しました: $error';
  }

  @override
  String get welcomeMessage => '今日のダッシュボードの状況をご確認ください。';

  @override
  String get forgotPasswordFeatureComingSoon => 'パスワード忘れ機能は近日公開予定です';

  @override
  String get emailPlaceholder => 'メールアドレス';

  @override
  String get passwordPlaceholder => 'パスワード';

  @override
  String get loginButton => 'ログイン';

  @override
  String get signUpButton => '新規アカウント登録';

  @override
  String get forgotPasswordLink => 'パスワードを忘れた場合';

  @override
  String get signUpFeatureComingSoon => '新規登録機能は近日公開予定です';

  @override
  String get deleteAction => '削除';

  @override
  String get saveAction => '保存';

  @override
  String get remaining => '残り';

  @override
  String get minute => '分';

  @override
  String get importAudio => '音声インポート';

  @override
  String get startRecording => '録音開始';

  @override
  String get allFiles => '全てのファイル';

  @override
  String get recordingCancel => 'キャンセル';

  @override
  String get recordingDelete => '削除';

  @override
  String get recordingConfirmDelete => '録音を削除';

  @override
  String get recordingConfirmDeleteMessage => 'この録音を削除してもよろしいですか？この操作は元に戻せません。';

  @override
  String get errorRecordingStartFailed => '録音の開始に失敗しました。デバイスを確認してもう一度お試しください。';

  @override
  String errorRecordingSaveFailed(String error) {
    return '録音の保存に失敗しました: $error';
  }

  @override
  String errorRecordingDeleteFailed(String error) {
    return '録音の削除に失敗しました: $error';
  }

  @override
  String get errorRecordingPermissionRequired =>
      '音声を録音するにはマイクへのアクセスが必要です。デバイス設定を確認してください。';

  @override
  String get errorRecordingStorageSpace => '録音するのに十分なストレージ容量がありません。容量を空けてください。';

  @override
  String get errorRecordingHardware => '音声録音が利用できません。デバイス設定を確認してください。';

  @override
  String get errorRecordingDeviceCheck => '録音を開始できません。デバイスを確認してもう一度お試しください。';

  @override
  String get permissionOpenSettings => '設定を開く';

  @override
  String get uploadInProgress => 'アップロード中...';

  @override
  String get uploadCompleted => 'アップロード完了';

  @override
  String get uploadFailed => 'アップロード失敗';

  @override
  String get uploadCancelled => 'アップロードキャンセル';

  @override
  String uploadProgress(int progress) {
    return 'アップロード中: $progress%';
  }

  @override
  String get errorUploadGeneric => 'アップロードエラーが発生しました';

  @override
  String get errorUploadNetworkUnavailable => 'アップロード用のネットワーク接続が利用できません';

  @override
  String get errorUploadServerUnavailable => 'アップロードサーバーが利用できません';

  @override
  String get errorUploadFileSizeExceeded => 'ファイルサイズがアップロード制限を超えています';

  @override
  String get errorUploadAuthenticationFailed => 'アップロード認証に失敗しました';

  @override
  String get uploadWorkerStarted => 'アップロードワーカー開始';

  @override
  String get uploadWorkerStopped => 'アップロードワーカー停止';

  @override
  String get transcription => '文字起こし';

  @override
  String get aiSummary => 'AI要約';
}
