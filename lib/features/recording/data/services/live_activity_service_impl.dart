import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/recording/data/models/recording_live_activity_model.dart';
import 'package:glidic_app/features/recording/domain/entities/recording_live_activity_entity.dart';
import 'package:glidic_app/features/recording/domain/services/live_activity_service.dart';
import 'package:live_activities/live_activities.dart';

/// Production-ready implementation of LiveActivityServiceInterface
///
/// This service manages iOS Live Activities for recording sessions using the

class LiveActivityServiceImpl implements LiveActivityService {
  /// Live Activities plugin instance for iOS integration
  final LiveActivities _liveActivitiesPlugin;

  /// Stream controller for URL scheme events from Live Activity interactions
  final StreamController<String> _urlSchemeController =
      StreamController<String>.broadcast();

  /// Current active Live Activity ID, null if no activity is active
  String? _currentActivityId;

  /// Initialization state flag to prevent multiple initialization attempts
  bool _isInitialized = false;

  /// Creates a new LiveActivityServiceImpl instance with dependency injection
  ///

  LiveActivityServiceImpl({
    required LiveActivities liveActivitiesPlugin,
  }) : _liveActivitiesPlugin = liveActivitiesPlugin;

  @override
  Future<void> initialize({String urlScheme = Constants.appUrlScheme}) async {
    // Skip initialization on non-iOS platforms
    if (!Platform.isIOS) {
      _isInitialized = true;
      return;
    }

    // Prevent multiple initialization attempts
    if (_isInitialized) {
      return;
    }

    try {
      // Initialize the plugin with App Group configuration
      await _liveActivitiesPlugin.init(
        appGroupId: Constants.appGroupId,
        urlScheme: urlScheme,
      );

      // Set up activity update stream listener for lifecycle management
      _liveActivitiesPlugin.activityUpdateStream.listen(
        _handleActivityUpdate,
        onError: _handleActivityUpdateError,
      );

      // Set up URL scheme stream listener for user interactions
      _liveActivitiesPlugin.urlSchemeStream().listen(
            _handleUrlSchemeEvent,
            onError: _handleUrlSchemeError,
          );

      _isInitialized = true;
    } catch (e, stackTrace) {
      _isInitialized = false;

      log(
        'Failed to initialize Live Activity service: $e',
        name: 'LiveActivityService',
        level: 1000, // SEVERE level
        error: e,
        stackTrace: stackTrace,
      );

      // Determine the appropriate failure type based on the error
      if (e.toString().contains('not supported') ||
          e.toString().contains('disabled')) {
        throw const PlatformFailure(
          'Live Activities are not supported or disabled on this device',
          code: 'LIVE_ACTIVITY_NOT_SUPPORTED',
        );
      }

      throw UnknownFailure(
        'Failed to initialize Live Activity service: ${e.toString()}',
        code: 'LIVE_ACTIVITY_INIT_ERROR',
        originalException: e,
      );
    }
  }

  @override
  Future<bool> areActivitiesEnabled() async {
    // Return false for non-iOS platforms
    if (!Platform.isIOS) {
      return false;
    }

    try {
      // Ensure service is initialized before checking availability
      if (!_isInitialized) {
        await initialize();
      }

      final isEnabled = await _liveActivitiesPlugin.areActivitiesEnabled();

      return isEnabled;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<String?> startRecordingActivity(
    RecordingLiveActivityEntity entity,
  ) async {
    // Skip on non-iOS platforms
    if (!Platform.isIOS) {
      return null;
    }

    try {
      // Ensure service is initialized
      if (!_isInitialized) {
        await initialize();
      }

      // Verify Live Activities are enabled
      final isEnabled = await areActivitiesEnabled();
      if (!isEnabled) {
        throw const PlatformFailure(
          'Live Activities are not enabled on this device',
          code: 'LIVE_ACTIVITY_DISABLED',
        );
      }

      // End any existing activity before starting a new one
      if (_currentActivityId != null) {
        await endRecordingActivity();

        // Brief delay to ensure proper cleanup
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // Convert entity to model and then to map format expected by the plugin
      final model = RecordingLiveActivityModel.fromEntity(entity);
      final modelData = model.toMap();

      // Generate unique activity ID based on timestamp
      final activityId = DateTime.now().millisecondsSinceEpoch.toString();

      // Create the Live Activity
      final createdActivityId = await _liveActivitiesPlugin.createActivity(
        activityId,
        modelData,
      );

      if (createdActivityId != null) {
        _currentActivityId = createdActivityId;
        return createdActivityId;
      } else {
        return null;
      }
    } catch (e) {
      // Re-throw known failure types
      if (e is PlatformFailure) {
        rethrow;
      }

      // Handle validation errors
      if (e.toString().contains('invalid') ||
          e.toString().contains('validation')) {
        throw ValidationFailure(
          'Invalid Live Activity data: ${e.toString()}',
          code: 'INVALID_ACTIVITY_DATA',
        );
      }

      throw UnknownFailure(
        'Failed to start Live Activity: ${e.toString()}',
        code: 'LIVE_ACTIVITY_START_ERROR',
        originalException: e,
      );
    }
  }

  @override
  Future<bool> updateRecordingActivity(
    RecordingLiveActivityEntity entity,
  ) async {
    // Skip on non-iOS platforms
    if (!Platform.isIOS) {
      return false;
    }

    try {
      // Ensure service is initialized
      if (!_isInitialized) {
        await initialize();
      }

      // Check if there's an active activity to update
      if (_currentActivityId == null) {
        // Try to start a new activity if none exists
        final activityId = await startRecordingActivity(entity);
        return activityId != null;
      }

      // Convert entity to model and then to map format
      final model = RecordingLiveActivityModel.fromEntity(entity);
      final modelData = model.toMap();

      // Update the existing activity
      await _liveActivitiesPlugin.updateActivity(
        _currentActivityId!,
        modelData,
      );

      return true;
    } catch (e, stackTrace) {
      log(
        'Error updating Live Activity: $e',
        name: 'LiveActivityService',
        level: 1000,
        error: e,
        stackTrace: stackTrace,
      );

      // Handle specific error cases
      if (e.toString().contains('Activity not found') ||
          e.toString().contains('ACTIVITY_ERROR')) {
        _currentActivityId = null;

        // Try to start a new activity
        try {
          final activityId = await startRecordingActivity(entity);
          return activityId != null;
        } catch (restartError) {
          throw const ValidationFailure(
            'No active Live Activity found and failed to create new one',
            code: 'NO_ACTIVE_LIVE_ACTIVITY',
          );
        }
      }

      // Handle validation errors
      if (e.toString().contains('invalid') ||
          e.toString().contains('validation')) {
        throw ValidationFailure(
          'Invalid Live Activity update data: ${e.toString()}',
          code: 'INVALID_UPDATE_DATA',
        );
      }

      throw UnknownFailure(
        'Failed to update Live Activity: ${e.toString()}',
        code: 'LIVE_ACTIVITY_UPDATE_ERROR',
        originalException: e,
      );
    }
  }

  @override
  Future<bool> endRecordingActivity() async {
    // Skip on non-iOS platforms or if no active activity
    if (!Platform.isIOS || _currentActivityId == null) {
      return false;
    }

    try {
      // Ensure service is initialized
      if (!_isInitialized) {
        await initialize();
      }

      final activityToEnd = _currentActivityId!;
      // End the activity
      await _liveActivitiesPlugin.endActivity(activityToEnd);

      // Clear the current activity ID
      _currentActivityId = null;

      return true;
    } catch (e, stackTrace) {
      log(
        'Error ending Live Activity: $e',
        name: 'LiveActivityService',
        level: 1000,
        error: e,
        stackTrace: stackTrace,
      );

      // Clear the activity ID even if ending failed
      _currentActivityId = null;

      throw UnknownFailure(
        'Failed to end Live Activity: ${e.toString()}',
        code: 'LIVE_ACTIVITY_END_ERROR',
        originalException: e,
      );
    }
  }

  @override
  String? get currentActivityId => _currentActivityId;

  @override
  Stream<String> get urlSchemeStream => _urlSchemeController.stream;

  @override
  void dispose() {
    try {
      // Close the URL scheme stream controller
      if (!_urlSchemeController.isClosed) {
        _urlSchemeController.close();
      }

      // Clear current activity ID
      _currentActivityId = null;

      // Reset initialization state
      _isInitialized = false;

      // Dispose the plugin if needed
      _liveActivitiesPlugin.dispose();
    } catch (e) {
      log(
        'Error disposing Live Activity service: $e',
        name: 'LiveActivityService',
        level: 900,
        error: e,
      );
    }
  }

  /// Handles activity update events from the Live Activities plugin
  void _handleActivityUpdate(dynamic event) {
    try {
      final eventString = event.toString();

      // Handle activity ended events
      if (eventString.contains('EndedActivityUpdate')) {
        // Extract activity ID from the event if possible
        if (_currentActivityId != null &&
            eventString.contains(_currentActivityId!)) {
          _currentActivityId = null;
        }
      }

      // Handle other activity update types as needed
      // This can be extended to handle specific update events
    } catch (e) {
      log(
        'Error processing activity update event: $e',
        name: 'LiveActivityService',
        level: 900,
        error: e,
      );
    }
  }

  /// Handles errors from the activity update stream
  void _handleActivityUpdateError(dynamic error) {
    log(
      'Activity update stream error: $error',
      name: 'LiveActivityService',
      level: 1000,
      error: error,
    );
  }

  /// Handles URL scheme events from Live Activity user interactions
  void _handleUrlSchemeEvent(dynamic schemeData) {
    try {
      final schemeString = schemeData.toString();

      // Forward the event to the stream if controller is not closed
      if (!_urlSchemeController.isClosed) {
        _urlSchemeController.add(schemeString);
      }
    } catch (e) {
      log(
        'Error processing URL scheme event: $e',
        name: 'LiveActivityService',
        level: 900,
        error: e,
      );
    }
  }

  /// Handles errors from the URL scheme stream
  ///
  /// This method logs URL scheme stream errors and ensures
  /// the service remains stable even when the URL scheme stream encounters issues.
  void _handleUrlSchemeError(dynamic error) {
    log(
      'URL scheme stream error: $error',
      name: 'LiveActivityService',
      level: 1000,
      error: error,
    );
  }
}
