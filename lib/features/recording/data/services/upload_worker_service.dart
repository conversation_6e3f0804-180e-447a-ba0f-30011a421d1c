import 'dart:developer';

import 'package:glidic_app/features/recording/data/workers/upload_worker.dart';

/// Service for managing the upload worker lifecycle
///
/// This service provides a clean interface for initializing and managing
/// the upload worker throughout the application lifecycle. It ensures
/// the worker is properly started, monitored, and disposed of.
class UploadWorkerService {
  UploadWorkerService({
    required UploadWorker uploadWorker,
  }) : _uploadWorker = uploadWorker;

  final UploadWorker _uploadWorker;
  bool _isInitialized = false;

  /// Initialize the upload worker service
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      // The upload worker will auto-start if configured to do so
      if (!_uploadWorker.isRunning) {
        await _uploadWorker.start();
      }

      // Listen to worker events for monitoring
      _uploadWorker.eventStream.listen(
        _handleWorkerEvent,
        onError: (error) {
          log(
            'Upload worker event stream error: $error',
            name: 'UploadWorkerService',
          );
        },
      );

      _isInitialized = true;
    } catch (e) {
      rethrow;
    }
  }

  /// Handle upload worker events
  void _handleWorkerEvent(UploadWorkerEvent event) {
    switch (event.type) {
      case UploadWorkerEventType.started:
        break;
      case UploadWorkerEventType.stopped:
        break;
      case UploadWorkerEventType.paused:
        break;
      case UploadWorkerEventType.resumed:
        break;
      case UploadWorkerEventType.uploadStarted:
        break;
      case UploadWorkerEventType.uploadCompleted:
        final results = event.data as List?;
        final count = results?.length ?? 0;
        log(
          '✅ Upload batch completed: $count recordings processed',
          name: 'UploadWorkerService',
        );
        break;
      case UploadWorkerEventType.uploadFailed:
        break;
      case UploadWorkerEventType.error:
        break;
    }
  }

  /// Trigger an immediate upload check
  Future<void> triggerUpload() async {
    if (!_isInitialized) {
      throw StateError('Upload worker service is not initialized');
    }

    await _uploadWorker.triggerUpload();
  }

  /// Notify the service that a new recording has been saved
  Future<void> onRecordingSaved(String recordingId) async {
    if (!_isInitialized) {
      return;
    }
    // Trigger upload with a small delay to ensure database transaction is committed
    Future.delayed(const Duration(seconds: 1), () async {
      try {
        await _uploadWorker.triggerUpload();
      } catch (e) {
        log(
          'Error triggering upload after recording save: $e',
          name: 'UploadWorkerService',
        );
      }
    });
  }

  /// Get the current status of the upload worker
  Map<String, dynamic> getStatus() {
    return {
      'isInitialized': _isInitialized,
      'isRunning': _uploadWorker.isRunning,
      'isUploading': _uploadWorker.isUploading,
      'isPaused': _uploadWorker.isPaused,
      'activeUploads': _uploadWorker.activeUploads,
    };
  }

  /// Dispose of the upload worker service
  void dispose() {
    if (!_isInitialized) return;
    _uploadWorker.dispose();
    _isInitialized = false;
  }
}
