import 'dart:async';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_result.dart';
import 'package:glidic_app/features/recording/domain/usecases/upload_records_usecase.dart';

/// Self-managing upload worker for recordings following clean architecture patterns
///
/// This worker automatically finds recording files with status "pending" in the database,
/// uploads them to the transcription service, and updates the record status to "finished"
/// after successful upload. It handles upload failures gracefully with retry logic.
///
/// Features:
/// - Self-managing lifecycle with app state awareness
/// - Automatic discovery of pending uploads
/// - Retry logic for failed uploads
/// - Comprehensive error handling and logging
/// - Configurable upload intervals
/// - App lifecycle integration (pause/resume)
/// - Foreground-based operation (no background tasks)
/// - Proper resource cleanup
class UploadWorker with WidgetsBindingObserver {
  /// Creates a new self-managing upload worker
  ///
  /// [uploadRecordsUseCase] - Use case for uploading recordings
  /// [uploadInterval] - How often to check for pending uploads (default: 5 minutes)
  /// [maxConcurrentUploads] - Maximum number of concurrent uploads (default: 3)
  /// [autoStart] - Whether to automatically start the worker (default: true)
  UploadWorker({
    required UploadRecordsUseCase uploadRecordsUseCase,
    Duration uploadInterval =
        const Duration(minutes: Constants.uploadWorkerInterval),
    int maxConcurrentUploads = Constants.uploadWorkerMaxConcurrentUploads,
    bool autoStart = true,
  })  : _uploadRecordsUseCase = uploadRecordsUseCase,
        _uploadInterval = uploadInterval,
        _maxConcurrentUploads = maxConcurrentUploads,
        _autoStart = autoStart {
    _initialize();
  }

  final UploadRecordsUseCase _uploadRecordsUseCase;
  final Duration _uploadInterval;
  final int _maxConcurrentUploads;
  final bool _autoStart;

  Timer? _uploadTimer;
  bool _isRunning = false;
  bool _isUploading = false;
  bool _isInitialized = false;
  bool _isPaused = false;
  final int _activeUploads = 0;

  final StreamController<UploadWorkerEvent> _eventController =
      StreamController<UploadWorkerEvent>.broadcast();

  /// Stream of upload worker events
  ///
  /// This stream emits events about the worker's status, upload progress,
  /// and any errors that occur during the upload process.
  ///
  /// Example:
  /// ```dart
  /// uploadWorker.eventStream.listen((event) {
  ///   switch (event.type) {
  ///     case UploadWorkerEventType.started:
  ///       print('Upload worker started');
  ///       break;
  ///     case UploadWorkerEventType.uploadCompleted:
  ///       print('Upload completed: ${event.data}');
  ///       break;
  ///     case UploadWorkerEventType.error:
  ///       print('Upload error: ${event.error}');
  ///       break;
  ///   }
  /// });
  /// ```
  Stream<UploadWorkerEvent> get eventStream => _eventController.stream;

  /// Whether the worker is currently running
  bool get isRunning => _isRunning;

  /// Whether uploads are currently in progress
  bool get isUploading => _isUploading;

  /// Number of active uploads
  int get activeUploads => _activeUploads;

  /// Whether the worker is paused due to app lifecycle
  bool get isPaused => _isPaused;

  /// Initialize the upload worker
  ///
  /// This method sets up the worker and registers for app lifecycle events.
  /// If autoStart is enabled, it will automatically start the worker.
  void _initialize() {
    if (_isInitialized) return;
    // Register for app lifecycle events
    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;

    // Auto-start if enabled
    if (_autoStart) {
      // Use a microtask to ensure the widget binding is ready
      Future.microtask(() => start());
    }
  }

  /// Handle app lifecycle state changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _handleAppPaused();
        break;
      case AppLifecycleState.hidden:
        // Continue running in hidden state
        break;
    }
  }

  /// Handle app resumed state
  void _handleAppResumed() {
    if (_isPaused && _isRunning) {
      _isPaused = false;
      _emitEvent(UploadWorkerEvent.resumed());

      // Trigger immediate upload check when app resumes
      Future.microtask(() => _processUploads());
    }
  }

  /// Handle app paused state
  void _handleAppPaused() {
    if (_isRunning && !_isPaused) {
      _isPaused = true;
      _emitEvent(UploadWorkerEvent.paused());
    }
  }

  /// Start the upload worker
  ///
  /// This method starts the foreground worker that periodically checks
  /// for pending uploads and processes them. The worker respects app
  /// lifecycle states and will pause when the app is not active.
  ///
  /// Example:
  /// ```dart
  /// await uploadWorker.start();
  /// print('Upload worker is now running');
  /// ```
  Future<void> start() async {
    if (_isRunning) {
      log('Upload worker already running', name: 'UploadWorker');
      return;
    }

    _isRunning = true;
    _isPaused = false;
    _emitEvent(UploadWorkerEvent.started());

    // Start the periodic upload check
    _uploadTimer =
        Timer.periodic(_uploadInterval, (_) => _processUploadsIfActive());

    // Process uploads immediately on start
    await _processUploadsIfActive();
  }

  /// Stop the upload worker
  ///
  /// This method stops the worker and cancels any ongoing uploads.
  /// It also unregisters from app lifecycle events.
  ///
  /// Example:
  /// ```dart
  /// await uploadWorker.stop();
  /// print('Upload worker stopped');
  /// ```
  Future<void> stop() async {
    if (!_isRunning) {
      return;
    }

    _isRunning = false;
    _isPaused = false;
    _uploadTimer?.cancel();
    _uploadTimer = null;

    _emitEvent(UploadWorkerEvent.stopped());
  }

  /// Process uploads if the app is active (not paused)
  ///
  /// This method checks the app lifecycle state before processing uploads
  /// to ensure we only upload when the app is actively being used.
  Future<void> _processUploadsIfActive() async {
    if (_isPaused) {
      log('Skipping upload processing - app is paused', name: 'UploadWorker');
      return;
    }

    await _processUploads();
  }

  /// Process pending uploads
  ///
  /// This method is called periodically to check for and process
  /// any recordings that need to be uploaded.
  Future<void> _processUploads() async {
    if (_isUploading || _activeUploads >= _maxConcurrentUploads) {
      return;
    }

    _isUploading = true;
    _emitEvent(UploadWorkerEvent.uploadStarted());

    try {
      final results = await _uploadRecordsUseCase.execute();

      if (results.isNotEmpty) {
        _emitEvent(UploadWorkerEvent.uploadCompleted(results));

        // Log results
        final successful = results.where((r) => r.isSuccess).length;
        final failed = results.where((r) => !r.isSuccess).length;

        log(
          'Upload batch completed: $successful successful, $failed failed',
          name: 'UploadWorker',
        );

        if (failed > 0) {
          log(
            'Some uploads failed in batch: $failed failures',
            name: 'UploadWorker',
          );
          _emitEvent(
            UploadWorkerEvent.uploadFailed(
              'Some uploads failed: $successful successful, $failed failed',
            ),
          );
        }
      } else {
        log('No pending uploads found', name: 'UploadWorker');
      }
    } catch (e, stackTrace) {
      _emitEvent(UploadWorkerEvent.error(e.toString(), stackTrace));
    } finally {
      _isUploading = false;
    }
  }

  /// Trigger an immediate upload check
  ///
  /// This method can be called to immediately check for and process
  /// pending uploads without waiting for the next scheduled interval.
  /// It respects the app lifecycle state and will only process if active.
  ///
  /// Example:
  /// ```dart
  /// await uploadWorker.triggerUpload();
  /// print('Manual upload check completed');
  /// ```
  Future<void> triggerUpload() async {
    if (!_isRunning) {
      throw StateError('Upload worker is not running');
    }

    log('Manual upload trigger requested', name: 'UploadWorker');
    await _processUploadsIfActive();
  }

  /// Force an immediate upload check regardless of app state
  ///
  /// This method bypasses the app lifecycle checks and processes
  /// uploads immediately. Use with caution.
  ///
  /// Example:
  /// ```dart
  /// await uploadWorker.forceUpload();
  /// print('Forced upload check completed');
  /// ```
  Future<void> forceUpload() async {
    if (!_isRunning) {
      throw StateError('Upload worker is not running');
    }

    await _processUploads();
  }

  /// Emit an event to the event stream
  void _emitEvent(UploadWorkerEvent event) {
    if (!_eventController.isClosed) {
      _eventController.add(event);
    }
  }

  /// Dispose of resources and clean up
  ///
  /// This method should be called when the worker is no longer needed
  /// to prevent memory leaks and properly clean up resources.
  /// It unregisters from app lifecycle events and stops the worker.
  void dispose() {
    log('Disposing upload worker', name: 'UploadWorker');

    // Unregister from app lifecycle events
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _isInitialized = false;
    }

    // Stop the worker
    stop();

    // Close the event stream
    _eventController.close();

    log('Upload worker disposed', name: 'UploadWorker');
  }
}

/// Events emitted by the upload worker
class UploadWorkerEvent {
  UploadWorkerEvent({
    required this.type,
    this.data,
    this.error,
    this.stackTrace,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  final UploadWorkerEventType type;
  final dynamic data;
  final String? error;
  final StackTrace? stackTrace;
  final DateTime timestamp;

  /// Worker started event
  factory UploadWorkerEvent.started() {
    return UploadWorkerEvent(type: UploadWorkerEventType.started);
  }

  /// Worker stopped event
  factory UploadWorkerEvent.stopped() {
    return UploadWorkerEvent(type: UploadWorkerEventType.stopped);
  }

  /// Worker paused event
  factory UploadWorkerEvent.paused() {
    return UploadWorkerEvent(type: UploadWorkerEventType.paused);
  }

  /// Worker resumed event
  factory UploadWorkerEvent.resumed() {
    return UploadWorkerEvent(type: UploadWorkerEventType.resumed);
  }

  /// Upload process started event
  factory UploadWorkerEvent.uploadStarted() {
    return UploadWorkerEvent(type: UploadWorkerEventType.uploadStarted);
  }

  /// Upload completed event
  factory UploadWorkerEvent.uploadCompleted(List<UploadResult> results) {
    return UploadWorkerEvent(
      type: UploadWorkerEventType.uploadCompleted,
      data: results,
    );
  }

  /// Upload failed event
  factory UploadWorkerEvent.uploadFailed(String error) {
    return UploadWorkerEvent(
      type: UploadWorkerEventType.uploadFailed,
      error: error,
    );
  }

  /// Error event
  factory UploadWorkerEvent.error(String error, [StackTrace? stackTrace]) {
    return UploadWorkerEvent(
      type: UploadWorkerEventType.error,
      error: error,
      stackTrace: stackTrace,
    );
  }

  @override
  String toString() {
    return 'UploadWorkerEvent(type: $type, error: $error, timestamp: $timestamp)';
  }
}

/// Types of events emitted by the upload worker
enum UploadWorkerEventType {
  started,
  stopped,
  paused,
  resumed,
  uploadStarted,
  uploadCompleted,
  uploadFailed,
  error,
}
