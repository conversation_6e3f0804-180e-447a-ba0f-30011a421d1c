// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcription_upload_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TranscriptionUploadData _$TranscriptionUploadDataFromJson(
        Map<String, dynamic> json) =>
    TranscriptionUploadData(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      path: json['path'] as String,
      filename: json['filename'] as String,
      originalName: json['original_name'] as String,
      size: (json['size'] as num).toInt(),
      mimeType: json['mime_type'] as String,
      status: json['status'] as String,
      storageDisk: json['storage_disk'] as String,
      url: json['url'] as String,
    );

Map<String, dynamic> _$TranscriptionUploadDataToJson(
        TranscriptionUploadData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'path': instance.path,
      'filename': instance.filename,
      'original_name': instance.originalName,
      'size': instance.size,
      'mime_type': instance.mimeType,
      'status': instance.status,
      'storage_disk': instance.storageDisk,
      'url': instance.url,
    };
