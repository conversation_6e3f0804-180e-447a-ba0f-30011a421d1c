// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcriptions_list_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TranscriptionItem _$TranscriptionItemFromJson(Map<String, dynamic> json) =>
    TranscriptionItem(
      id: (json['id'] as num).toInt(),
      originalName: json['original_name'] as String,
      filePath: json['file_path'] as String,
      fileName: json['file_name'] as String,
      mimeType: json['mime_type'] as String,
      fileSize: (json['file_size'] as num).toInt(),
      fileExtension: json['file_extension'] as String,
      userId: (json['user_id'] as num).toInt(),
      uploadSessionId: json['upload_session_id'] as String,
      metadata: TranscriptionMetadata.fromJson(
          json['metadata'] as Map<String, dynamic>),
      status: json['status'] as String,
      processedAt: json['processed_at'] == null
          ? null
          : DateTime.parse(json['processed_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$TranscriptionItemToJson(TranscriptionItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'original_name': instance.originalName,
      'file_path': instance.filePath,
      'file_name': instance.fileName,
      'mime_type': instance.mimeType,
      'file_size': instance.fileSize,
      'file_extension': instance.fileExtension,
      'user_id': instance.userId,
      'upload_session_id': instance.uploadSessionId,
      'metadata': instance.metadata,
      'status': instance.status,
      'processed_at': instance.processedAt?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

TranscriptionMetadata _$TranscriptionMetadataFromJson(
        Map<String, dynamic> json) =>
    TranscriptionMetadata(
      fileUrl: json['file_url'] as String,
      ipAddress: json['ip_address'] as String,
      userAgent: json['user_agent'] as String,
      uploadedAt: DateTime.parse(json['uploaded_at'] as String),
      storageDisk: json['storage_disk'] as String,
    );

Map<String, dynamic> _$TranscriptionMetadataToJson(
        TranscriptionMetadata instance) =>
    <String, dynamic>{
      'file_url': instance.fileUrl,
      'ip_address': instance.ipAddress,
      'user_agent': instance.userAgent,
      'uploaded_at': instance.uploadedAt.toIso8601String(),
      'storage_disk': instance.storageDisk,
    };
