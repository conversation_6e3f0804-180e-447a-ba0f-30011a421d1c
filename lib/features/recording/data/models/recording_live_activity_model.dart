import 'package:glidic_app/features/recording/domain/entities/recording_live_activity_entity.dart';

/// Live Activity model for recording sessions
/// This model handles data conversion for Live Activities and extends the domain entity
class RecordingLiveActivityModel extends RecordingLiveActivityEntity {
  const RecordingLiveActivityModel({
    required super.title,
    required super.duration,
    required super.status,
    super.amplitude,
    super.isRecording,
    super.isPaused,
    super.timestamp,
    super.waveformAmplitudes,
  });

  /// Create from domain entity
  factory RecordingLiveActivityModel.fromEntity(
    RecordingLiveActivityEntity entity,
  ) {
    return RecordingLiveActivityModel(
      title: entity.title,
      duration: entity.duration,
      status: entity.status,
      amplitude: entity.amplitude,
      isRecording: entity.isRecording,
      isPaused: entity.isPaused,
      timestamp: entity.timestamp,
      waveformAmplitudes: entity.waveformAmplitudes,
    );
  }

  /// Convert to domain entity
  RecordingLiveActivityEntity toEntity() {
    return RecordingLiveActivityEntity(
      title: title,
      duration: duration,
      status: status,
      amplitude: amplitude,
      isRecording: isRecording,
      isPaused: isPaused,
      timestamp: timestamp,
      waveformAmplitudes: waveformAmplitudes,
    );
  }

  /// Convert model to Map for Live Activities
  /// According to documentation, all values must be compatible with UserDefaults
  Map<String, String> toMap() {
    final map = <String, String>{
      'recordingTime': formattedDuration,
      'isRecording': isRecording.toString(),
      'startTime':
          (timestamp ?? DateTime.now()).millisecondsSinceEpoch.toString(),
    };

    map['title'] = title;
    map['recordingState'] = status.name;

    if (waveformAmplitudes != null && waveformAmplitudes!.isNotEmpty) {
      // Convert list of amplitudes to JSON string for UserDefaults
      map['waveformAmplitudes'] = waveformAmplitudesToJson(waveformAmplitudes!);
    }

    return map;
  }

  /// Convert amplitude list to JSON string
  String waveformAmplitudesToJson(List<double> amplitudes) {
    final stringValues = amplitudes.map((a) => a.toString()).toList();
    return '[${stringValues.join(',')}]';
  }

  /// Create a copy with updated values
  @override
  RecordingLiveActivityModel copyWith({
    String? title,
    int? duration,
    RecordingActivityStatus? status,
    double? amplitude,
    bool? isRecording,
    bool? isPaused,
    DateTime? timestamp,
    List<double>? waveformAmplitudes,
  }) {
    return RecordingLiveActivityModel(
      title: title ?? this.title,
      duration: duration ?? this.duration,
      status: status ?? this.status,
      amplitude: amplitude ?? this.amplitude,
      isRecording: isRecording ?? this.isRecording,
      isPaused: isPaused ?? this.isPaused,
      timestamp: timestamp ?? this.timestamp,
      waveformAmplitudes: waveformAmplitudes ?? this.waveformAmplitudes,
    );
  }

  /// Factory constructor for starting a recording
  factory RecordingLiveActivityModel.start({
    required String title,
    required DateTime startTime,
  }) {
    return RecordingLiveActivityModel(
      title: title,
      duration: 0,
      status: RecordingActivityStatus.recording,
      amplitude: 0.0,
      isRecording: true,
      isPaused: false,
      timestamp: startTime,
      waveformAmplitudes: const [],
    );
  }

  /// Factory constructor with elapsed time calculation
  factory RecordingLiveActivityModel.withElapsedTime({
    required DateTime startTime,
    required bool isRecording,
    required String title,
    required String recordingState,
    List<double>? waveformAmplitudes,
  }) {
    final now = DateTime.now();
    final duration = now.difference(startTime).inMilliseconds;

    return RecordingLiveActivityModel(
      title: title,
      duration: duration,
      status: RecordingActivityStatus.fromString(recordingState),
      amplitude: waveformAmplitudes?.isNotEmpty == true
          ? waveformAmplitudes!.last
          : 0.0,
      isRecording: isRecording,
      isPaused: false,
      timestamp: now,
      waveformAmplitudes: waveformAmplitudes,
    );
  }

  /// Stop recording
  RecordingLiveActivityModel stop() {
    return copyWith(
      isRecording: false,
      status: RecordingActivityStatus.saved,
    );
  }

  /// Cancel recording
  RecordingLiveActivityModel cancel() {
    return copyWith(
      isRecording: false,
      status: RecordingActivityStatus.cancelled,
    );
  }

  /// Create saved state model
  factory RecordingLiveActivityModel.saved({
    required DateTime startTime,
    required int duration,
    required String title,
  }) {
    return RecordingLiveActivityModel(
      title: title,
      duration: duration,
      status: RecordingActivityStatus.saved,
      amplitude: 0.0,
      isRecording: false,
      isPaused: false,
      timestamp: startTime,
    );
  }
}
