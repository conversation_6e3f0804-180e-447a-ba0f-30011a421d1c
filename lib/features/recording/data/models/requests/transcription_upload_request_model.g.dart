// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcription_upload_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TranscriptionUploadRequestModel _$TranscriptionUploadRequestModelFromJson(
        Map<String, dynamic> json) =>
    TranscriptionUploadRequestModel(
      userId: (json['user_id'] as num).toInt(),
      filename: json['filename'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$TranscriptionUploadRequestModelToJson(
        TranscriptionUploadRequestModel instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'filename': instance.filename,
      'metadata': instance.metadata,
    };
