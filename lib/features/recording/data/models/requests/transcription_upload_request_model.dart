import 'dart:io';

import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transcription_upload_request_model.g.dart';

/// Request model for transcription upload
@JsonSerializable()
class TranscriptionUploadRequestModel {
  const TranscriptionUploadRequestModel({
    required this.userId,
    this.filename,
    this.metadata,
  }) : audioFile = null;

  /// Create a request model with an audio file
  const TranscriptionUploadRequestModel.withFile({
    required this.audioFile,
    required this.userId,
    this.filename,
    this.metadata,
  });

  /// Audio file to be transcribed
  @JsonKey(includeFromJson: false, includeToJson: false)
  final File? audioFile;

  /// ID of the user uploading the file
  @JsonKey(name: 'user_id')
  final int userId;

  /// Optional custom filename for the upload
  final String? filename;

  /// Optional metadata for the transcription
  final Map<String, dynamic>? metadata;

  /// Create TranscriptionUploadRequestModel from JSON
  factory TranscriptionUploadRequestModel.fromJson(Map<String, dynamic> json) =>
      _$TranscriptionUploadRequestModelFromJson(json);

  /// Convert TranscriptionUploadRequestModel to JSON
  Map<String, dynamic> toJson() =>
      _$TranscriptionUploadRequestModelToJson(this);

  /// Get the filename for the upload
  String get uploadFilename {
    if (filename != null && filename!.isNotEmpty) {
      return filename!;
    }
    if (audioFile != null) {
      return audioFile!.path.split('/').last;
    }
    return Constants.defaultUploadFileName;
  }

  /// Get file size in bytes
  Future<int?> get fileSize async => audioFile?.length();

  /// Check if file exists
  Future<bool> get fileExists async => audioFile?.exists() ?? false;

  @override
  String toString() => 'TranscriptionUploadRequestModel('
      'userId: $userId, '
      'filename: $uploadFilename, '
      'hasMetadata: ${metadata != null}'
      ')';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TranscriptionUploadRequestModel &&
        other.audioFile?.path == audioFile?.path &&
        other.userId == userId &&
        other.filename == filename;
  }

  @override
  int get hashCode =>
      (audioFile?.path.hashCode ?? 0) ^
      userId.hashCode ^
      (filename?.hashCode ?? 0);
}
