import 'package:glidic_app/features/recording/domain/entities/transcription_item_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transcription_item_model.g.dart';

/// Item transcription on a transcription audio
@JsonSerializable()
class TranscriptionItemModel {
  TranscriptionItemModel({
    required this.startTime,
    required this.endTime,
    required this.text,
    required this.speaker,
  });

  @Json<PERSON>ey(name: 'start_time')
  final int startTime;
  @<PERSON>son<PERSON><PERSON>(name: 'end_time')
  final int endTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'text')
  final String text;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'speaker')
  final String speaker;


  factory TranscriptionItemModel.fromJson(Map<String, dynamic> json) =>
      _$TranscriptionItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$TranscriptionItemModelToJson(this);

  TranscriptionItemEntity toEntity() {
    return TranscriptionItemEntity(
      startTime: startTime,
      endTime: endTime,
      text: text,
      speaker: speaker,
    );
  }
}