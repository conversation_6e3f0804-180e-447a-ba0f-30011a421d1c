// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecordItemModel _$RecordItemModelFromJson(Map<String, dynamic> json) =>
    RecordItemModel(
      id: json['id'] as String,
      title: json['title'] as String,
      filePath: json['file_path'] as String,
      duration: RecordItemModel._durationFromJson(
          (json['duration_ms'] as num).toInt()),
      createdAt: DateTime.parse(json['created_at'] as String),
      fileSize: (json['file_size'] as num).toInt(),
      waveformData: (json['waveform_data'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      isPlaying: json['is_playing'] as bool? ?? false,
      playbackPosition: json['playback_position_ms'] == null
          ? Duration.zero
          : RecordItemModel._durationFromJson(
              (json['playback_position_ms'] as num).toInt()),
      status: json['status'] == null
          ? UploadStatus.pending
          : RecordItemModel._statusFromJson(json['status'] as String),
      remoteUrl: json['remote_url'] as String?,
      transcriptionId: (json['transcription_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RecordItemModelToJson(RecordItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'file_path': instance.filePath,
      'duration_ms': RecordItemModel._durationToJson(instance.duration),
      'created_at': instance.createdAt.toIso8601String(),
      'file_size': instance.fileSize,
      'waveform_data': instance.waveformData,
      'is_playing': instance.isPlaying,
      'playback_position_ms':
          RecordItemModel._durationToJson(instance.playbackPosition),
      'status': RecordItemModel._statusToJson(instance.status),
      'remote_url': instance.remoteUrl,
      'transcription_id': instance.transcriptionId,
    };
