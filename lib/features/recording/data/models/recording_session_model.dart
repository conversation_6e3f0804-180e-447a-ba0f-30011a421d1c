import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'recording_session_model.g.dart';

/// Data model for recording session
/// This model handles JSON serialization and conversion to domain entities
@JsonSerializable()
class RecorderSessionModel {
  /// Unique identifier for the recording session
  @JsonKey(name: 'id')
  final String id;

  /// File path where the recording is saved
  @<PERSON>sonKey(name: 'file_path')
  final String? filePath;

  /// Duration of the recording in milliseconds
  @JsonKey(name: 'duration_ms')
  final int durationMs;

  /// Recording status
  @JsonKey(name: 'status')
  final RecordingStatus status;

  /// Timestamp when recording started
  @JsonKey(name: 'start_time')
  final DateTime? startTime;

  /// Timestamp when recording ended
  @JsonKey(name: 'end_time')
  final DateTime? endTime;

  /// Title/name of the recording
  @JsonKey(name: 'title')
  final String title;

  /// File size in bytes
  @Json<PERSON><PERSON>(name: 'file_size_bytes')
  final int? fileSizeBytes;

  const RecorderSessionModel({
    required this.id,
    this.filePath,
    required this.durationMs,
    required this.status,
    this.startTime,
    this.endTime,
    required this.title,
    this.fileSizeBytes,
  });

  /// Create from domain entity
  factory RecorderSessionModel.fromEntity(RecorderSessionEntity entity) {
    return RecorderSessionModel(
      id: entity.id,
      filePath: entity.filePath,
      durationMs: entity.durationMs,
      status: entity.status,
      startTime: entity.startTime,
      endTime: entity.endTime,
      title: entity.title,
      fileSizeBytes: entity.fileSizeBytes,
    );
  }

  /// Convert to domain entity
  RecorderSessionEntity toEntity() {
    return RecorderSessionEntity(
      id: id,
      filePath: filePath,
      durationMs: durationMs,
      status: status,
      startTime: startTime,
      endTime: endTime,
      title: title,
      fileSizeBytes: fileSizeBytes,
    );
  }

  /// Create a copy with updated values
  RecorderSessionModel copyWith({
    String? id,
    String? filePath,
    int? durationMs,
    RecordingStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    String? title,
    int? fileSizeBytes,
  }) {
    return RecorderSessionModel(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      durationMs: durationMs ?? this.durationMs,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      title: title ?? this.title,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$RecorderSessionModelToJson(this);

  /// Create from JSON
  factory RecorderSessionModel.fromJson(Map<String, dynamic> json) =>
      _$RecorderSessionModelFromJson(json);
}
