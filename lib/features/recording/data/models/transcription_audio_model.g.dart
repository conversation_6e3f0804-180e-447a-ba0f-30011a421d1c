// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcription_audio_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TranscriptionAudioModel _$TranscriptionAudioModelFromJson(
        Map<String, dynamic> json) =>
    TranscriptionAudioModel(
      id: json['id'] as String,
      language: json['language'] as String,
      durationSeconds: (json['duration_seconds'] as num).toInt(),
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      audioUrl: json['audio_url'] as String,
      speakerCount: (json['speaker_count'] as num).toInt(),
      transcriptions: (json['transcriptions'] as List<dynamic>)
          .map(
              (e) => TranscriptionItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      fullText: json['full_text'] as String,
      speakers: (json['speakers'] as List<dynamic>)
          .map((e) =>
              TranscriptionSpeakerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      confidence: (json['confidence'] as num).toDouble(),
    );

Map<String, dynamic> _$TranscriptionAudioModelToJson(
        TranscriptionAudioModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'language': instance.language,
      'duration_seconds': instance.durationSeconds,
      'status': instance.status,
      'created_at': instance.createdAt.toIso8601String(),
      'audio_url': instance.audioUrl,
      'speaker_count': instance.speakerCount,
      'transcriptions': instance.transcriptions,
      'full_text': instance.fullText,
      'speakers': instance.speakers,
      'confidence': instance.confidence,
    };
