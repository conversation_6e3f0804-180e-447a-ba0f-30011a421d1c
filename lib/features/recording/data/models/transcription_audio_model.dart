import 'package:glidic_app/features/recording/data/models/transcription_item_model.dart';
import 'package:glidic_app/features/recording/data/models/transcription_speaker_model.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_audio_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transcription_audio_model.g.dart';


/// Response model for transcription audio
@JsonSerializable()
class TranscriptionAudioModel {
  @Json<PERSON>ey(name: 'id')
  final String id;
  @JsonKey(name: 'language')
  final String language;
  @<PERSON>son<PERSON>ey(name: 'duration_seconds')
  final int durationSeconds;
  @Json<PERSON>ey(name: 'status')
  final String status;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON>ey(name: 'audio_url')
  final String audioUrl;
  @JsonKey(name: 'speaker_count')
  final int speakerCount;
  @Json<PERSON>ey(name: 'transcriptions')
  final List<TranscriptionItemModel> transcriptions;
  @<PERSON>son<PERSON>ey(name: 'full_text')
  final String fullText;
  @JsonKey(name: 'speakers')
  final List<TranscriptionSpeakerModel> speakers;
  @JsonKey(name: 'confidence')
  final double confidence;

  const TranscriptionAudioModel({
    required this.id,
    required this.language,
    required this.durationSeconds,
    required this.status,
    required this.createdAt,
    required this.audioUrl,
    required this.speakerCount,
    required this.transcriptions,
    required this.fullText,
    required this.speakers,
    required this.confidence,
  });

  factory TranscriptionAudioModel.fromJson(Map<String, dynamic> json) =>
      _$TranscriptionAudioModelFromJson(json);

  Map<String, dynamic> toJson() => _$TranscriptionAudioModelToJson(this);

  TranscriptionAudioEntity toEntity() {
    return TranscriptionAudioEntity(
      id: id,
      language: language,
      durationSeconds: durationSeconds,
      status: status,
      createdAt: createdAt,
      audioUrl: audioUrl,
      speakerCount: speakerCount,
      transcriptions: transcriptions.map((e) => e.toEntity()).toList(),
      fullText: fullText,
      speakers: speakers.map((e) => e.toEntity()).toList(),
      confidence: confidence,
    );
  }
}