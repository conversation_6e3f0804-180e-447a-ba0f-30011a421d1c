// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transcription_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TranscriptionItemModel _$TranscriptionItemModelFromJson(
        Map<String, dynamic> json) =>
    TranscriptionItemModel(
      startTime: (json['start_time'] as num).toInt(),
      endTime: (json['end_time'] as num).toInt(),
      text: json['text'] as String,
      speaker: json['speaker'] as String,
    );

Map<String, dynamic> _$TranscriptionItemModelToJson(
        TranscriptionItemModel instance) =>
    <String, dynamic>{
      'start_time': instance.startTime,
      'end_time': instance.endTime,
      'text': instance.text,
      'speaker': instance.speaker,
    };
