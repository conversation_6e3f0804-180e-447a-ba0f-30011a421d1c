import 'package:glidic_app/features/recording/data/models/transcription_metadata_model.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transcriptions_list_item.g.dart';

/// Data model for transcription item from the API response
/// This model handles JSON serialization and conversion to domain entities
@JsonSerializable()
class TranscriptionItemModel extends Transcription {
  const TranscriptionItemModel({
    required this.id,
    required this.originalName,
    required this.filePath,
    required this.fileName,
    required this.mimeType,
    required this.fileSize,
    required this.fileExtension,
    required this.userId,
    required this.uploadSessionId,
    required this.metadata,
    required this.status,
    this.processedAt,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          id: id,
          originalName: originalName,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          fileExtension: fileExtension,
          userId: userId,
          uploadSessionId: uploadSessionId,
          metadata: metadata,
          status: status,
          processedAt: processedAt,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  /// JSON field mappings for API serialization
  @override
  final int id;

  @JsonKey(name: 'original_name')
  @override
  final String originalName;

  @JsonKey(name: 'file_path')
  @override
  final String filePath;

  @JsonKey(name: 'file_name')
  @override
  final String fileName;

  @JsonKey(name: 'mime_type')
  @override
  final String mimeType;

  @JsonKey(name: 'file_size')
  @override
  final int fileSize;

  @JsonKey(name: 'file_extension')
  @override
  final String fileExtension;

  @JsonKey(name: 'user_id')
  @override
  final int userId;

  @JsonKey(name: 'upload_session_id')
  @override
  final String uploadSessionId;

  @JsonKey(fromJson: _metadataFromJson, toJson: _metadataToJson)
  @override
  final TranscriptionMetadataModel metadata;

  @override
  final String status;

  @JsonKey(name: 'processed_at')
  @override
  final DateTime? processedAt;

  @JsonKey(name: 'created_at')
  @override
  final DateTime createdAt;

  @JsonKey(name: 'updated_at')
  @override
  final DateTime updatedAt;

  /// Create from JSON
  factory TranscriptionItemModel.fromJson(Map<String, dynamic> json) =>
      _$TranscriptionItemModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$TranscriptionItemModelToJson(this);

  /// Create from domain entity
  factory TranscriptionItemModel.fromEntity(Transcription entity) {
    return TranscriptionItemModel(
      id: entity.id,
      originalName: entity.originalName,
      filePath: entity.filePath,
      fileName: entity.fileName,
      mimeType: entity.mimeType,
      fileSize: entity.fileSize,
      fileExtension: entity.fileExtension,
      userId: entity.userId,
      uploadSessionId: entity.uploadSessionId,
      metadata: TranscriptionMetadataModel.fromEntity(entity.metadata),
      status: entity.status,
      processedAt: entity.processedAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to domain entity
  Transcription toEntity() {
    return Transcription(
      id: id,
      originalName: originalName,
      filePath: filePath,
      fileName: fileName,
      mimeType: mimeType,
      fileSize: fileSize,
      fileExtension: fileExtension,
      userId: userId,
      uploadSessionId: uploadSessionId,
      metadata: metadata.toEntity(),
      status: status,
      processedAt: processedAt,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Helper method for JSON serialization of metadata
  static TranscriptionMetadataModel _metadataFromJson(
          Map<String, dynamic> json) =>
      TranscriptionMetadataModel.fromJson(json);

  /// Helper method for JSON deserialization of metadata
  static Map<String, dynamic> _metadataToJson(
          TranscriptionMetadataModel metadata) =>
      metadata.toJson();
}
