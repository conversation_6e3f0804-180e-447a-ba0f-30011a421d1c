// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recording_session_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecorderSessionModel _$RecorderSessionModelFromJson(
        Map<String, dynamic> json) =>
    RecorderSessionModel(
      id: json['id'] as String,
      filePath: json['file_path'] as String?,
      durationMs: (json['duration_ms'] as num).toInt(),
      status: $enumDecode(_$RecordingStatusEnumMap, json['status']),
      startTime: json['start_time'] == null
          ? null
          : DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] == null
          ? null
          : DateTime.parse(json['end_time'] as String),
      title: json['title'] as String,
      fileSizeBytes: (json['file_size_bytes'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RecorderSessionModelToJson(
        RecorderSessionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'file_path': instance.filePath,
      'duration_ms': instance.durationMs,
      'status': _$RecordingStatusEnumMap[instance.status]!,
      'start_time': instance.startTime?.toIso8601String(),
      'end_time': instance.endTime?.toIso8601String(),
      'title': instance.title,
      'file_size_bytes': instance.fileSizeBytes,
    };

const _$RecordingStatusEnumMap = {
  RecordingStatus.recording: 'recording',
  RecordingStatus.paused: 'paused',
  RecordingStatus.completed: 'completed',
  RecordingStatus.stopped: 'stopped',
  RecordingStatus.failed: 'failed',
};
