import 'package:glidic_app/features/recording/domain/entities/transcription_upload_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transcription_upload_data.g.dart';

/// Data model for transcription upload data from API response
/// This model handles JSON serialization and conversion to domain entities
@JsonSerializable()
class TranscriptionUploadDataModel extends TranscriptionUploadData {
  const TranscriptionUploadDataModel({
    required this.id,
    required this.userId,
    required this.path,
    required this.filename,
    required this.originalName,
    required this.size,
    required this.mimeType,
    required this.status,
    required this.storageDisk,
    required this.url,
  }) : super(
          id: id,
          userId: userId,
          path: path,
          filename: filename,
          originalName: originalName,
          size: size,
          mimeType: mimeType,
          status: status,
          storageDisk: storageDisk,
          url: url,
        );

  /// JSON field mappings for API serialization
  @override
  final int id;

  @JsonKey(name: 'user_id')
  @override
  final int userId;

  @override
  final String path;

  @override
  final String filename;

  @Json<PERSON>ey(name: 'original_name')
  @override
  final String originalName;

  @override
  final int size;

  @JsonKey(name: 'mime_type')
  @override
  final String mimeType;

  @override
  final String status;

  @JsonKey(name: 'storage_disk')
  @override
  final String storageDisk;

  @override
  final String url;

  /// Create from JSON
  factory TranscriptionUploadDataModel.fromJson(Map<String, dynamic> json) =>
      _$TranscriptionUploadDataModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$TranscriptionUploadDataModelToJson(this);

  /// Create from domain entity
  factory TranscriptionUploadDataModel.fromEntity(
      TranscriptionUploadData entity) {
    return TranscriptionUploadDataModel(
      id: entity.id,
      userId: entity.userId,
      path: entity.path,
      filename: entity.filename,
      originalName: entity.originalName,
      size: entity.size,
      mimeType: entity.mimeType,
      status: entity.status,
      storageDisk: entity.storageDisk,
      url: entity.url,
    );
  }

  /// Convert to domain entity
  TranscriptionUploadData toEntity() {
    return TranscriptionUploadData(
      id: id,
      userId: userId,
      path: path,
      filename: filename,
      originalName: originalName,
      size: size,
      mimeType: mimeType,
      status: status,
      storageDisk: storageDisk,
      url: url,
    );
  }
}
