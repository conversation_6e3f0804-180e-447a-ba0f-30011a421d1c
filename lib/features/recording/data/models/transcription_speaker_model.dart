import 'package:glidic_app/features/recording/domain/entities/transcription_speaker_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'transcription_speaker_model.g.dart';

/// Speaker on a transcription audio
@JsonSerializable()
class TranscriptionSpeakerModel {
  @<PERSON><PERSON><PERSON>ey(name: 'speaker_id')
  final String speakerId;
  @J<PERSON><PERSON>ey(name: 'label')
  final String label;

  const TranscriptionSpeakerModel({
    required this.speakerId,
    required this.label,
  });

  factory TranscriptionSpeakerModel.fromJson(Map<String, dynamic> json) =>
      _$TranscriptionSpeakerModelFromJson(json);

  Map<String, dynamic> toJson() => _$TranscriptionSpeakerModelToJson(this);

  TranscriptionSpeakerEntity toEntity() {
    return TranscriptionSpeakerEntity(
      speakerId: speakerId,
      label: label,
    );
  }
}