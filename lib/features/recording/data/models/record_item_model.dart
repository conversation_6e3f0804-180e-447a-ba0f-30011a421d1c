import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_status.dart';
import 'package:json_annotation/json_annotation.dart';

part 'record_item_model.g.dart';

/// Data model for recorded audio file
/// This model handles JSON serialization and conversion to domain entities
@JsonSerializable()
class RecordItemModel {
  /// Unique identifier for the recording
  @JsonKey(name: 'id')
  final String id;

  /// Display title of the recording
  @JsonKey(name: 'title')
  final String title;

  /// File path where the recording is stored
  @JsonKey(name: 'file_path')
  final String filePath;

  /// Duration of the recording in milliseconds
  @JsonKey(
    name: 'duration_ms',
    fromJson: _durationFromJson,
    toJson: _durationToJson,
  )
  final Duration duration;

  /// When the recording was created
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  /// File size in bytes
  @JsonKey(name: 'file_size')
  final int fileSize;

  /// Waveform data for visualization
  @JsonKey(name: 'waveform_data')
  final List<double>? waveformData;

  /// Whether the recording is currently playing
  @JsonKey(name: 'is_playing')
  final bool isPlaying;

  /// Current playback position in milliseconds
  @JsonKey(
    name: 'playback_position_ms',
    fromJson: _durationFromJson,
    toJson: _durationToJson,
  )
  final Duration playbackPosition;

  /// Upload status of the recording
  @JsonKey(
    name: 'status',
    fromJson: _statusFromJson,
    toJson: _statusToJson,
  )
  final UploadStatus status;

  /// Remote URL after successful upload
  @JsonKey(name: 'remote_url')
  final String? remoteUrl;

  /// Transcription ID from transcription service
  @JsonKey(name: 'transcription_id')
  final int? transcriptionId;

  const RecordItemModel({
    required this.id,
    required this.title,
    required this.filePath,
    required this.duration,
    required this.createdAt,
    required this.fileSize,
    this.waveformData,
    this.isPlaying = false,
    this.playbackPosition = Duration.zero,
    this.status = UploadStatus.pending,
    this.remoteUrl,
    this.transcriptionId,
  });

  /// Create from domain entity
  factory RecordItemModel.fromEntity(RecordItem entity) {
    return RecordItemModel(
      id: entity.id,
      title: entity.title,
      filePath: entity.filePath,
      duration: entity.duration,
      createdAt: entity.createdAt,
      fileSize: entity.fileSize,
      waveformData: entity.waveformData,
      isPlaying: entity.isPlaying,
      playbackPosition: entity.playbackPosition,
      status: entity.status,
      remoteUrl: entity.remoteUrl,
      transcriptionId: entity.transcriptionId,
    );
  }

  /// Convert to domain entity
  RecordItem toEntity() {
    return RecordItem(
      id: id,
      title: title,
      filePath: filePath,
      duration: duration,
      createdAt: createdAt,
      fileSize: fileSize,
      waveformData: waveformData,
      isPlaying: isPlaying,
      playbackPosition: playbackPosition,
      status: status,
      remoteUrl: remoteUrl,
      transcriptionId: transcriptionId,
    );
  }

  /// Create a copy with updated fields
  RecordItemModel copyWith({
    String? id,
    String? title,
    String? filePath,
    Duration? duration,
    DateTime? createdAt,
    int? fileSize,
    List<double>? waveformData,
    bool? isPlaying,
    Duration? playbackPosition,
    UploadStatus? status,
    String? remoteUrl,
    int? transcriptionId,
  }) {
    return RecordItemModel(
      id: id ?? this.id,
      title: title ?? this.title,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      fileSize: fileSize ?? this.fileSize,
      waveformData: waveformData ?? this.waveformData,
      isPlaying: isPlaying ?? this.isPlaying,
      playbackPosition: playbackPosition ?? this.playbackPosition,
      status: status ?? this.status,
      remoteUrl: remoteUrl ?? this.remoteUrl,
      transcriptionId: transcriptionId ?? this.transcriptionId,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$RecordItemModelToJson(this);

  /// Create from JSON
  factory RecordItemModel.fromJson(Map<String, dynamic> json) =>
      _$RecordItemModelFromJson(json);

  // Custom JSON converters
  static Duration _durationFromJson(int milliseconds) {
    return Duration(milliseconds: milliseconds);
  }

  static int _durationToJson(Duration duration) {
    return duration.inMilliseconds;
  }

  static UploadStatus _statusFromJson(String status) {
    return UploadStatus.fromString(status);
  }

  static String _statusToJson(UploadStatus status) {
    return status.name;
  }
}
