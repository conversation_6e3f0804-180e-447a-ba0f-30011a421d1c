import 'dart:developer';
import 'dart:io';

import 'package:glidic_app/features/recording/data/datasources/interfaces/records_local_data_source.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/repositories/records_repository.dart';
import 'package:share_plus/share_plus.dart';

/// Implementation of RecordsRepository
class RecordsRepositoryImpl implements RecordsRepository {
  const RecordsRepositoryImpl({
    required RecordsLocalDataSource dataSource,
  }) : _dataSource = dataSource;

  final RecordsLocalDataSource _dataSource;

  @override
  Future<List<RecordItem>> getAllRecords() async {
    return _dataSource.getAllRecords();
  }

  @override
  Future<RecordItem?> getRecordById(String id) async {
    return _dataSource.getRecordById(id);
  }

  @override
  Future<bool> deleteRecord(String id) async {
    return _dataSource.deleteRecord(id);
  }

  @override
  Future<bool> updateRecord(RecordItem record) async {
    return _dataSource.updateRecord(record);
  }

  @override
  Future<List<RecordItem>> searchRecords(String query) async {
    return _dataSource.searchRecords(query);
  }

  @override
  Future<List<RecordItem>> getRecordsSorted({
    bool newestFirst = true,
  }) async {
    final records = await _dataSource.getAllRecords();

    records.sort((a, b) {
      return newestFirst
          ? b.createdAt.compareTo(a.createdAt)
          : a.createdAt.compareTo(b.createdAt);
    });

    return records;
  }

  @override
  Future<List<RecordItem>> getRecordsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final allRecords = await _dataSource.getAllRecords();

    return allRecords.where((record) {
      return record.createdAt.isAfter(startDate) &&
          record.createdAt.isBefore(endDate);
    }).toList();
  }

  @override
  Future<int> getTotalStorageUsed() async {
    final records = await _dataSource.getAllRecords();
    return records.fold<int>(0, (total, record) => total + record.fileSize);
  }

  @override
  Future<bool> exportRecord(String id, String destinationPath) async {
    try {
      final record = await _dataSource.getRecordById(id);
      if (record == null) return false;

      final sourceFile = File(record.filePath);
      if (!await sourceFile.exists()) return false;

      final destinationFile = File(destinationPath);
      await sourceFile.copy(destinationFile.path);
      return true;
    } catch (e) {
      log('Error exporting record: $e');
      return false;
    }
  }

  @override
  Future<bool> shareRecord(String id) async {
    try {
      final record = await _dataSource.getRecordById(id);
      if (record == null) return false;

      final file = File(record.filePath);
      if (!await file.exists()) return false;

      await Share.shareXFiles(
        [XFile(record.filePath)],
        text: 'Sharing record: ${record.title}',
      );
      return true;
    } catch (e) {
      log('Error sharing record: $e');
      return false;
    }
  }

  @override
  Future<void> addRecord({
    required String id,
    required String title,
    required String filePath,
    required Duration duration,
    required DateTime createdAt,
    required int fileSize,
    List<double>? waveformData,
  }) {
    return _dataSource.addRecord(
      id: id,
      title: title,
      filePath: filePath,
      duration: duration,
      createdAt: createdAt,
      fileSize: fileSize,
      waveformData: waveformData,
    );
  }
}
