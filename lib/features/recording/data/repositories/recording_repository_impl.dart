import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/recorder_data_source.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:glidic_app/features/recording/domain/repositories/recorder_repository.dart';

/// Implementation of RecorderRepository
class RecorderRepositoryImpl implements RecorderRepository {
  const RecorderRepositoryImpl({
    required this.dataSource,
  });

  final RecorderDataSource dataSource;

  @override
  Future<RecorderSessionEntity> startRecording({required String title}) async {
    try {
      final sessionModel = await dataSource.startRecording(title: title);
      return sessionModel.toEntity();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RecorderSessionEntity> stopRecording({
    required String sessionId,
  }) async {
    try {
      final sessionModel = await dataSource.stopRecording(sessionId: sessionId);
      return sessionModel.toEntity();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<RecorderSessionEntity?> getCurrentSession() async {
    try {
      final sessionModel = await dataSource.getCurrentSession();
      if (sessionModel != null) {
      } else {}
      return sessionModel?.toEntity();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<RecorderSessionEntity>> getAllSessions() async {
    // TODO: Implement when local storage is added
    return [];
  }

  @override
  Future<void> deleteSession({required String sessionId}) async {
    // TODO: Implement when local storage is added
    throw const BusinessFailure(
      'Delete session not implemented yet',
      code: 'NOT_IMPLEMENTED',
    );
  }

  @override
  Future<RecorderSessionEntity> getSessionById({
    required String sessionId,
  }) async {
    // TODO: Implement when local storage is added
    throw const BusinessFailure(
      'Get session by ID not implemented yet',
      code: 'NOT_IMPLEMENTED',
    );
  }

  @override
  void setRecorderController(RecorderController controller) {
    dataSource.setRecorderController(controller);
  }
}
