import 'dart:io';

import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/transcription_remote_datasource.dart';
import 'package:glidic_app/features/recording/data/models/transcription_upload_data.dart';
import 'package:glidic_app/features/recording/data/models/transcriptions_list_item.dart'
    as list_models;
import 'package:glidic_app/features/recording/domain/entities/transcription.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_audio_entity.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_upload_result.dart';
import 'package:glidic_app/features/recording/domain/repositories/transcription_repository.dart';

/// Implementation of TranscriptionRepository
class TranscriptionRepositoryImpl implements TranscriptionRepository {
  const TranscriptionRepositoryImpl({
    required TranscriptionRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  final TranscriptionRemoteDataSource _remoteDataSource;

  @override
  Future<TranscriptionUploadResult> uploadTranscription({
    required File file,
    required int userId,
    void Function(double progress)? onProgress,
  }) async {
    try {
      final response = await _remoteDataSource.uploadTranscription(
        audioFile: file,
        userId: userId,
        onProgress: onProgress,
      );

      if (response.success == true && response.data != null) {
        final transcriptionData = _mapUploadDataToEntity(response.data!);
        return TranscriptionUploadResult.success(
          message: response.message ?? '',
          transcriptionData: transcriptionData,
        );
      } else {
        return TranscriptionUploadResult.failure(
          errorMessage: response.message ?? '',
        );
      }
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<List<Transcription>> getTranscriptions({
    required int userId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _remoteDataSource.getTranscriptions(
        userId: userId,
        page: page,
        limit: limit,
      );

      if (response.success == true && response.data != null) {
        return response.data!
            .map((item) => _mapTranscriptionItemToEntity(item))
            .toList();
      } else {
        return [];
      }
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<Transcription?> getTranscriptionById(int transcriptionId) async {
    throw UnimplementedError(
      'getTranscriptionById is not implemented. '
      'The API does not provide a specific endpoint for getting a single transcription.',
    );
  }

  @override
  Future<bool> isServiceAvailable() async {
    try {
      return await _remoteDataSource.isServiceAvailable();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<TranscriptionAudioEntity?> getTranscriptionAudio(String audioId) async {
    try {
      final response = await _remoteDataSource.getTranscriptionAudio(audioId);
      return response.data?.toEntity();
      
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  /// Map upload data model to domain entity
  TranscriptionData _mapUploadDataToEntity(TranscriptionUploadData data) {
    return TranscriptionData(
      id: data.id,
      userId: data.userId,
      path: data.path,
      filename: data.filename,
      originalName: data.originalName,
      size: data.size,
      mimeType: data.mimeType,
      status: data.status,
      storageDisk: data.storageDisk,
      url: data.url,
    );
  }

  /// Map transcription item model to domain entity
  Transcription _mapTranscriptionItemToEntity(
    list_models.TranscriptionItem item,
  ) {
    return Transcription(
      id: item.id,
      originalName: item.originalName,
      filePath: item.filePath,
      fileName: item.fileName,
      mimeType: item.mimeType,
      fileSize: item.fileSize,
      fileExtension: item.fileExtension,
      userId: item.userId,
      uploadSessionId: item.uploadSessionId,
      metadata: _mapMetadataToEntity(item.metadata),
      status: item.status,
      processedAt: item.processedAt,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    );
  }

  /// Map metadata model to domain entity
  TranscriptionMetadata _mapMetadataToEntity(
    list_models.TranscriptionMetadata metadata,
  ) {
    return TranscriptionMetadata(
      fileUrl: metadata.fileUrl,
      ipAddress: metadata.ipAddress,
      userAgent: metadata.userAgent,
      uploadedAt: metadata.uploadedAt,
      storageDisk: metadata.storageDisk,
    );
  }
}
