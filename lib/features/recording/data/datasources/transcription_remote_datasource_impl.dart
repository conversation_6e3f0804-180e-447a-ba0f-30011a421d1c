import 'dart:io';

import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/core/data/models/base_response.dart';
import 'package:glidic_app/features/recording/data/datasources/api_services/transcription_api_service.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/transcription_remote_datasource.dart';
import 'package:glidic_app/features/recording/data/models/transcription_audio_model.dart';
import 'package:glidic_app/features/recording/data/models/transcription_upload_data.dart';
import 'package:glidic_app/features/recording/data/models/transcriptions_list_item.dart';
// Implementation of remote transcription data source
class TranscriptionRemoteDataSourceImpl
    implements TranscriptionRemoteDataSource {
  TranscriptionRemoteDataSourceImpl(
    this._transcriptionApiService,
  );

  final TranscriptionApiService _transcriptionApiService;

  @override
  Future<BaseResponse<TranscriptionUploadData>> uploadTranscription({
    required File audioFile,
    required int userId,
    void Function(double progress)? onProgress,
    CancelToken? cancelToken,
  }) async {
    try {
      // Use the upload service for progress tracking and advanced features
      return await _transcriptionApiService.uploadTranscription(
        audioFile: audioFile,
        userId: userId,
        cancelToken: cancelToken,
        onProgress: (sent, total) {
          if (total > 0) {
            final progress = sent / total;
            onProgress?.call(progress);
          }
        },
      );
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<BaseResponse<List<TranscriptionItem>>> getTranscriptions({
    required int userId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      return await _transcriptionApiService.getTranscriptions(
        userId,
        page: page > 1 ? page : null,
        limit: limit != 20 ? limit : null,
      );
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<BaseResponse<TranscriptionAudioModel>> getTranscriptionAudio(
    String audioId,
  ) async {
    return _transcriptionApiService.getTranscriptionAudio(audioId);
  }

  @override
  Future<bool> isServiceAvailable() async {
    try {
      return true;
    } catch (e) {
      // Service health check failed
      return false;
    }
  }

  @override
  Future<bool> cancelUpload(CancelToken cancelToken) async {
    try {
      cancelToken.cancel('Upload cancelled');
      return false;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }
}
