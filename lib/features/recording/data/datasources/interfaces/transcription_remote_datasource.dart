import 'dart:io';

import 'package:dio/dio.dart';

import 'package:glidic_app/core/data/models/base_response.dart';import 'package:glidic_app/features/recording/data/models/transcription_audio_model.dart';
import 'package:glidic_app/features/recording/data/models/transcription_upload_data.dart';
import 'package:glidic_app/features/recording/data/models/transcriptions_list_item.dart';

/// Abstract interface for transcription remote data source
abstract class TranscriptionRemoteDataSource {
  /// Upload an audio file for transcription
  Future<BaseResponse<TranscriptionUploadData>> uploadTranscription({
    required File audioFile,
    required int userId,
    void Function(double progress)? onProgress,
    CancelToken? cancelToken,
  });

  /// Get list of transcriptions for a specific user
  Future<BaseResponse<List<TranscriptionItem>>> getTranscriptions({
    required int userId,
    int page = 1,
    int limit = 20,
  });

  /// Get transcription audio for a specific audio id
  Future<BaseResponse<TranscriptionAudioModel>> getTranscriptionAudio(
    String audioId,
  );

  /// Check if the transcription service is available
  Future<bool> isServiceAvailable();

  /// Cancel an ongoing upload
  Future<bool> cancelUpload(CancelToken cancelToken);
}