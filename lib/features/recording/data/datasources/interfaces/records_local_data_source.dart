import 'package:glidic_app/features/recording/domain/entities/record_item.dart';

/// Data source for managing recorded audio files
abstract class RecordsLocalDataSource {
  Future<List<RecordItem>> getAllRecords();
  Future<RecordItem?> getRecordById(String id);
  Future<bool> deleteRecord(String id);
  Future<bool> updateRecord(RecordItem record);
  Future<List<RecordItem>> searchRecords(String query);

  /// Add a new record to metadata
  Future<void> addRecord({
    required String id,
    required String title,
    required String filePath,
    required Duration duration,
    required DateTime createdAt,
    required int fileSize,
    List<double>? waveformData,
  });
}
