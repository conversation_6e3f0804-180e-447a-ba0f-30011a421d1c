import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:glidic_app/features/recording/data/models/recording_session_model.dart';

/// Data source for recording operations
abstract class RecorderDataSource {
  Future<RecorderSessionModel> startRecording({required String title});
  Future<RecorderSessionModel> stopRecording({required String sessionId});
  Future<RecorderSessionModel?> getCurrentSession();
  void setRecorderController(RecorderController controller);
}
