import 'dart:io';

import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/data/datasources/database/daos/records_dao.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/records_local_data_source.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';

/// Local data source for managing recorded audio files
class RecordsLocalDataSourceImpl implements RecordsLocalDataSource {
  final RecordsDao _recordsDao;

  RecordsLocalDataSourceImpl(this._recordsDao);

  @override
  Future<List<RecordItem>> getAllRecords() async {
    try {
      final recordings = await _recordsDao.getAllRecords();
      final validRecordings = <RecordItem>[];

      for (final recording in recordings) {
        final item = RecordItem.fromDataTable(recording);
        validRecordings.add(item);
      }
      return validRecordings;
    } catch (e) {
      throw AppFailureConstructors.database(
        'Failed to retrieve all records: $e',
      );
    }
  }

  @override
  Future<RecordItem?> getRecordById(String id) async {
    try {
      final recording = await _recordsDao.getRecordById(id);
      if (recording == null) {
        return null;
      }

      // Check if file still exists
      if (await File(recording.filePath).exists()) {
        final item = RecordItem.fromDataTable(recording);
        return item;
      } else {
        return null;
      }
    } catch (e) {
      throw AppFailureConstructors.database(
        'Failed to retrieve record by ID: $e',
      );
    }
  }

  @override
  Future<bool> deleteRecord(String id) async {
    try {
      // Get recording info before deletion
      final recording = await _recordsDao.getRecordById(id);
      if (recording == null) {
        return false;
      }

      // Delete the audio file
      final file = File(recording.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Delete from database
      final deletedCount = await _recordsDao.deleteRecordById(id);
      final success = deletedCount > 0;

      return success;
    } catch (e) {
      throw AppFailureConstructors.database(
        'Failed to delete record: $e',
      );
    }
  }

  @override
  Future<bool> updateRecord(RecordItem record) async {
    try {
      // Convert entity to companion for database update
      final companion = _recordsDao.fromEntity(record);
      final rowsAffected = await _recordsDao.updateRecordById(
        record.id,
        companion,
      );

      final success = rowsAffected > 0;

      return success;
    } catch (e) {
      throw AppFailureConstructors.database(
        'Failed to update record: $e',
      );
    }
  }

  @override
  Future<List<RecordItem>> searchRecords(String query) async {
    try {
      final recordings = await _recordsDao.searchRecords(query);
      final validRecordings = <RecordItem>[];

      for (final recording in recordings) {
        // Check if file still exists
        if (await File(recording.filePath).exists()) {
          final item = RecordItem.fromDataTable(recording);
          validRecordings.add(item);
        } else {
          // Remove invalid recording from database
          await _recordsDao.deleteRecordById(recording.id);
        }
      }

      return validRecordings;
    } catch (e) {
      throw AppFailureConstructors.database(
        'Failed to search records: $e',
      );
    }
  }

  @override
  Future<void> addRecord({
    required String id,
    required String title,
    required String filePath,
    required Duration duration,
    required DateTime createdAt,
    required int fileSize,
    List<double>? waveformData,
  }) async {
    try {
      final recordingItem = RecordItem(
        id: id,
        title: title,
        filePath: filePath,
        duration: duration,
        createdAt: createdAt,
        fileSize: fileSize,
        waveformData: waveformData,
      );

      final companion = _recordsDao.fromEntity(recordingItem);
      await _recordsDao.insertRecordCompanion(companion);
    } catch (e) {
      throw AppFailureConstructors.database(
        'Failed to add record: $e',
      );
    }
  }

  /// Get total number of recordings
  Future<int> getRecordsCount() async {
    try {
      return await _recordsDao.getRecordsCount();
    } catch (e) {
      return 0;
    }
  }

  /// Get total file size of all recordings
  Future<int> getTotalFileSize() async {
    try {
      return await _recordsDao.getTotalFileSize();
    } catch (e) {
      return 0;
    }
  }

  /// Watch all recordings (stream)
  Stream<List<RecordItem>> watchAllRecordings() {
    return _recordsDao.watchAllRecordings().map((recordings) {
      return RecordItem.fromTableDataList(recordings);
    });
  }

  /// Watch recording by ID (stream)
  Stream<RecordItem?> watchRecordingById(String id) {
    return _recordsDao.watchRecordingById(id).map((recording) {
      return recording != null ? RecordItem.fromDataTable(recording) : null;
    });
  }
}
