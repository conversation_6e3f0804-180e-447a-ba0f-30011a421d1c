import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/recorder_data_source.dart';
import 'package:glidic_app/features/recording/data/models/recording_session_model.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

class RecorderDataSourceImpl implements RecorderDataSource {
  RecorderController? _recorder;
  final Uuid _uuid = const Uuid();

  RecorderSessionModel? _currentSession;

  bool _isInitialized = false;
  DateTime? _recordingStartTime;

  @override
  void setRecorderController(RecorderController controller) {
    _recorder = controller;
    _isInitialized = true;
  }

  Future<void> _initializeRecorder() async {
    if (!_isInitialized || _recorder == null) {
      throw Exception(
        'RecorderController not set. Call setRecorderController first.',
      );
    }
  }

  @override
  Future<RecorderSessionModel> startRecording({required String title}) async {
    try {
      // Initialize recorder
      await _initializeRecorder();

      // Get documents directory
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${_uuid.v4()}${Constants.defaultAudioExtension}';
      final filePath = '${directory.path}/$fileName';

      // Start recording with audio_waveforms
      await _recorder!.record(path: filePath);

      _recordingStartTime = DateTime.now();
      // Create session
      final sessionId = _uuid.v4();
      _currentSession = RecorderSessionModel(
        id: sessionId,
        filePath: filePath,
        durationMs: 0,
        status: RecordingStatus.recording,
        startTime: _recordingStartTime,
        title: title,
      );

      return _currentSession!;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<RecorderSessionModel> stopRecording({
    required String sessionId,
  }) async {
    try {
      if (_currentSession?.id != sessionId) {
        throw Exception('No active recording session found');
      }

      // Stop recording
      final path = await _recorder!.stop();

      // Get file size
      int? fileSize;
      if (path != null) {
        final file = File(path);
        if (await file.exists()) {
          fileSize = await file.length();
        }
      }

      final endTime = DateTime.now();
      final totalDuration =
          endTime.difference(_recordingStartTime!).inMilliseconds;
      // Update session
      _currentSession = _currentSession!.copyWith(
        status: RecordingStatus.completed,
        endTime: endTime,
        durationMs: totalDuration,
        fileSizeBytes: fileSize,
        filePath: path,
      );

      final completedSession = _currentSession!;
      _currentSession = null;

      return completedSession;
    } catch (e) {
      throw AppFailureConstructors.recordingStopFailed(
        'Failed to stop recording: $e',
      );
    }
  }

  @override
  Future<RecorderSessionModel?> getCurrentSession() async {
    return _currentSession;
  }

  void dispose() {
    _recorder = null;
    _isInitialized = false;
  }
}
