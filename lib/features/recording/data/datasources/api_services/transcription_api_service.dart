import 'dart:io';

import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/data/models/base_response.dart';

import 'package:glidic_app/features/recording/data/models/transcription_audio_model.dart';
import 'package:glidic_app/features/recording/data/models/transcription_upload_data.dart';
import 'package:glidic_app/features/recording/data/models/transcriptions_list_item.dart';
import 'package:retrofit/retrofit.dart';

part 'transcription_api_service.g.dart';

/// Retrofit API service for transcription endpoints
@RestApi()
abstract class TranscriptionApiService {
  factory TranscriptionApiService(Dio dio) = _TranscriptionApiService;

  /// Upload an audio file for transcription
  @POST(NetworkConstants.transcriptionUpload)
  @MultiPart()
  Future<BaseResponse<TranscriptionUploadData>> uploadTranscription({
    @Part(name: 'audio_file') required File audioFile,
    @Part(name: 'user_id') required int userId,
    CancelToken? cancelToken,
    ProgressCallback? onProgress,
  });

  /// Get list of transcriptions for a specific user
  @GET(NetworkConstants.transcriptionList)
  Future<BaseResponse<List<TranscriptionItem>>> getTranscriptions(
    @Path('userId') int userId, {
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @GET(NetworkConstants.transcriptionAudio)
  Future<BaseResponse<TranscriptionAudioModel>> getTranscriptionAudio(
    @Path('audioId') String audioId,
  );
}
