import 'package:glidic_app/core/common/errors/failures.dart';

/// Recording-specific failure classes following clean architecture patterns
/// These failures provide detailed error information for recording operations

/// Base class for all recording-related failures
abstract class RecordingFailure extends AppFailure {
  const RecordingFailure(
    String message, {
    super.code,
    super.details,
  }) : super(message: message);

  @override
  String get category => 'recording';
}

/// Failure when microphone permission is denied
class MicrophonePermissionFailure extends RecordingFailure {
  const MicrophonePermissionFailure({
    String message = 'Microphone permission is required to start recording',
    String code = 'MICROPHONE_PERMISSION_DENIED',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => true;
}

/// Failure when recording hardware is not available
class RecordingHardwareFailure extends RecordingFailure {
  const RecordingHardwareFailure({
    String message = 'Audio recording hardware is not available',
    String code = 'RECORDING_HARDWARE_UNAVAILABLE',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => false;
}

/// Failure when insufficient storage space for recording
class InsufficientStorageFailure extends RecordingFailure {
  const InsufficientStorageFailure({
    String message = 'Insufficient storage space for recording',
    String code = 'INSUFFICIENT_STORAGE_SPACE',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => true;
}

/// Failure when recording session is not found
class RecordingSessionNotFoundFailure extends RecordingFailure {
  const RecordingSessionNotFoundFailure({
    String message = 'Recording session not found',
    String code = 'RECORDING_SESSION_NOT_FOUND',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => false;
}

/// Failure when recording file operations fail
class RecordingFileFailure extends RecordingFailure {
  const RecordingFileFailure({
    String message = 'Recording file operation failed',
    String code = 'RECORDING_FILE_ERROR',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => true;
}

/// Failure when recording state is invalid for the requested operation
class InvalidRecordingStateFailure extends RecordingFailure {
  const InvalidRecordingStateFailure({
    String message = 'Invalid recording state for this operation',
    String code = 'INVALID_RECORDING_STATE',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => false;
}

/// Failure when recording duration exceeds maximum allowed
class RecordingDurationExceededFailure extends RecordingFailure {
  const RecordingDurationExceededFailure({
    String message = 'Recording duration exceeded maximum allowed time',
    String code = 'RECORDING_DURATION_EXCEEDED',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => false;
}

/// Failure when recording codec is not supported
class UnsupportedCodecFailure extends RecordingFailure {
  const UnsupportedCodecFailure({
    String message = 'Recording codec is not supported on this device',
    String code = 'UNSUPPORTED_CODEC',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => false;
}

/// Failure when recording save operation fails
class RecordingSaveFailure extends RecordingFailure {
  const RecordingSaveFailure({
    String message = 'Failed to save recording',
    String code = 'RECORDING_SAVE_FAILED',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => true;
}

/// Failure when recording delete operation fails
class RecordingDeleteFailure extends RecordingFailure {
  const RecordingDeleteFailure({
    String message = 'Failed to delete recording',
    String code = 'RECORDING_DELETE_FAILED',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => true;
}

// =============================================================================
// UPLOAD FAILURES
// =============================================================================

/// Failure when upload operation fails
class UploadFailure extends RecordingFailure {
  const UploadFailure({
    String message = 'Failed to upload recording',
    String code = 'UPLOAD_FAILED',
    Map<String, dynamic>? details,
  }) : super(message, code: code, details: details);

  @override
  bool get canRetry => true;
}

/// Failure when network connection is not available for upload
class NetworkFailure extends UploadFailure {
  const NetworkFailure({
    super.message = 'Network connection not available for upload',
    super.code = 'NETWORK_UNAVAILABLE',
    super.details,
  });

  @override
  bool get canRetry => true;
}

/// Failure when upload server is not available
class ServerFailure extends UploadFailure {
  const ServerFailure({
    super.message = 'Upload server is not available',
    super.code = 'SERVER_UNAVAILABLE',
    super.details,
  });

  @override
  bool get canRetry => true;
}

/// Failure when file is too large for upload
class FileSizeExceededFailure extends UploadFailure {
  const FileSizeExceededFailure({
    super.message = 'File size exceeds upload limit',
    super.code = 'FILE_SIZE_EXCEEDED',
    super.details,
  });

  @override
  bool get canRetry => false;
}

/// Failure when upload authentication fails
class UploadAuthenticationFailure extends UploadFailure {
  const UploadAuthenticationFailure({
    super.message = 'Upload authentication failed',
    super.code = 'UPLOAD_AUTH_FAILED',
    super.details,
  });

  @override
  bool get canRetry => false;
}
