import 'package:equatable/equatable.dart';

/// Domain entity representing a transcription
class Transcription extends Equatable {
  const Transcription({
    required this.id,
    required this.originalName,
    required this.filePath,
    required this.fileName,
    required this.mimeType,
    required this.fileSize,
    required this.fileExtension,
    required this.userId,
    required this.uploadSessionId,
    required this.metadata,
    required this.status,
    this.processedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Unique identifier for the transcription
  final int id;

  /// Original filename from the client
  final String originalName;

  /// File path on the server
  final String filePath;

  /// Generated filename on the server
  final String fileName;

  /// MIME type of the file
  final String mimeType;

  /// File size in bytes
  final int fileSize;

  /// File extension
  final String fileExtension;

  /// User ID who uploaded the file
  final int userId;

  /// Upload session identifier
  final String uploadSessionId;

  /// Additional metadata
  final TranscriptionMetadata metadata;

  /// Processing status (pending, processing, completed, failed)
  final String status;

  /// When the transcription was processed
  final DateTime? processedAt;

  /// When the transcription was created
  final DateTime createdAt;

  /// When the transcription was last updated
  final DateTime updatedAt;

  /// Check if transcription is completed
  bool get isCompleted => status.toLowerCase() == 'completed';

  /// Check if transcription is pending
  bool get isPending => status.toLowerCase() == 'pending';

  /// Check if transcription is processing
  bool get isProcessing => status.toLowerCase() == 'processing';

  /// Check if transcription failed
  bool get isFailed => status.toLowerCase() == 'failed';

  /// Get file URL from metadata
  String get fileUrl => metadata.fileUrl;

  @override
  List<Object?> get props => [
        id,
        originalName,
        filePath,
        fileName,
        mimeType,
        fileSize,
        fileExtension,
        userId,
        uploadSessionId,
        metadata,
        status,
        processedAt,
        createdAt,
        updatedAt,
      ];
}

/// Domain entity representing transcription metadata
class TranscriptionMetadata extends Equatable {
  const TranscriptionMetadata({
    required this.fileUrl,
    required this.ipAddress,
    required this.userAgent,
    required this.uploadedAt,
    required this.storageDisk,
  });

  /// Public URL to access the file
  final String fileUrl;

  /// IP address of the uploader
  final String ipAddress;

  /// User agent of the uploader
  final String userAgent;

  /// When the file was uploaded
  final DateTime uploadedAt;

  /// Storage disk type (s3, local, etc.)
  final String storageDisk;

  @override
  List<Object?> get props => [
        fileUrl,
        ipAddress,
        userAgent,
        uploadedAt,
        storageDisk,
      ];
}
