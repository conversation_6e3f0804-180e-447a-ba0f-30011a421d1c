import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/extensions/duration_extensions.dart';

/// Recording session entity representing a recording session
/// This is a domain entity that contains the business logic and rules
class RecorderSessionEntity extends Equatable {
  /// Unique identifier for the recording session
  final String id;

  /// File path where the recording is saved
  final String? filePath;

  /// Duration of the recording in milliseconds
  final int durationMs;

  /// Recording status
  final RecordingStatus status;

  /// Timestamp when recording started
  final DateTime? startTime;

  /// Timestamp when recording ended
  final DateTime? endTime;

  /// Title/name of the recording
  final String title;

  /// File size in bytes
  final int? fileSizeBytes;

  const RecorderSessionEntity({
    required this.id,
    this.filePath,
    required this.durationMs,
    required this.status,
    this.startTime,
    this.endTime,
    required this.title,
    this.fileSizeBytes,
  });

  /// Create a copy of this recording session with updated values
  RecorderSessionEntity copyWith({
    String? id,
    String? filePath,
    int? durationMs,
    RecordingStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    String? title,
    int? fileSizeBytes,
  }) {
    return RecorderSessionEntity(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      durationMs: durationMs ?? this.durationMs,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      title: title ?? this.title,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
    );
  }

  /// Get formatted duration string
  String get formattedDuration {
    final duration = Duration(milliseconds: durationMs);
    return duration.format('mm:ss');
  }

  /// Check if recording is active
  bool get isActive => status == RecordingStatus.recording;

  /// Check if recording is paused
  bool get isPaused => status == RecordingStatus.paused;

  /// Check if recording is completed
  bool get isCompleted => status == RecordingStatus.completed;

  @override
  List<Object?> get props => [
        id,
        filePath,
        durationMs,
        status,
        startTime,
        endTime,
        title,
        fileSizeBytes,
      ];
}

/// Enum representing different recording statuses
enum RecordingStatus {
  /// Recording is currently active
  recording,

  /// Recording is paused
  paused,

  /// Recording is completed
  completed,

  /// Recording is stopped
  stopped,

  /// Recording failed
  failed,
}

/// Extension to get display text for recording status
extension RecordingStatusExtension on RecordingStatus {
  String get displayText {
    switch (this) {
      case RecordingStatus.recording:
        return 'Recording';
      case RecordingStatus.paused:
        return 'Paused';
      case RecordingStatus.completed:
        return 'Completed';
      case RecordingStatus.stopped:
        return 'Stopped';
      case RecordingStatus.failed:
        return 'Failed';
    }
  }
}
