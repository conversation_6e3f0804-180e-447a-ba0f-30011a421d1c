/// Enumeration of possible upload statuses for recordings
///
/// This enum defines all possible states that a recording upload
/// can be in, which affects how the upload worker processes them.
///
/// Simplified upload flow: pending -> uploading -> finished
enum UploadStatus {
  /// Recording is saved locally but not yet uploaded
  pending,

  /// Recording is currently being uploaded
  uploading,

  /// Upload process is complete and file is available remotely
  finished,

  /// Recording upload failed
  failed,

  /// Recording upload was cancelled
  cancelled;

  /// Create status from string representation
  ///
  /// This factory method allows creating enum values from string
  /// representations, useful when deserializing from database.
  static UploadStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return UploadStatus.pending;
      case 'uploading':
        return UploadStatus.uploading;
      case 'finished':
        return UploadStatus.finished;
      case 'failed':
        return UploadStatus.failed;
      case 'cancelled':
        return UploadStatus.cancelled;
      // Legacy support
      case 'created':
      case 'uploaded': // Map old 'uploaded' status to 'finished'
        return UploadStatus.finished;
      default:
        return UploadStatus.pending;
    }
  }

  /// Get string representation of the status
  String get name {
    switch (this) {
      case UploadStatus.pending:
        return 'pending';
      case UploadStatus.uploading:
        return 'uploading';
      case UploadStatus.finished:
        return 'finished';
      case UploadStatus.failed:
        return 'failed';
      case UploadStatus.cancelled:
        return 'cancelled';
    }
  }

  /// Check if the status indicates the upload can be retried
  bool get canRetry {
    switch (this) {
      case UploadStatus.pending:
      case UploadStatus.failed:
        return true;
      case UploadStatus.uploading:
      case UploadStatus.finished:
      case UploadStatus.cancelled:
        return false;
    }
  }

  /// Check if the status indicates the upload is in progress
  bool get isUploading {
    return this == UploadStatus.uploading;
  }

  /// Check if the status indicates the upload is complete
  bool get isComplete {
    return this == UploadStatus.finished;
  }

  /// Check if upload can be started
  bool get canStartUpload => this == UploadStatus.pending;

  /// Check if the recording is uploaded to server
  bool get isUploaded => this == UploadStatus.finished;

  /// Check if upload is completed
  bool get isCompleted => this == UploadStatus.finished;

  /// Check if the status indicates the upload has failed
  bool get hasFailed {
    return this == UploadStatus.failed;
  }
}
