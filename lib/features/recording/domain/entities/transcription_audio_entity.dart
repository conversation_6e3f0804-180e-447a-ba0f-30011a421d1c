import 'package:flutter/material.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_item_entity.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_speaker_entity.dart';

/// Entity representing a transcription audio
class TranscriptionAudioEntity {
  final String id;
  final String language;
  final int durationSeconds;
  final String status;
  final DateTime createdAt;
  final String audioUrl;
  final int speakerCount;
  final List<TranscriptionItemEntity> transcriptions;
  final String fullText;
  final List<TranscriptionSpeakerEntity> speakers;
  final double confidence;

  const TranscriptionAudioEntity({
    required this.id,
    required this.language,
    required this.durationSeconds,
    required this.status,
    required this.createdAt,
    required this.audioUrl,
    required this.speakerCount,
    required this.transcriptions,
    required this.fullText,
    required this.speakers,
    required this.confidence,
  });

  /// Generates a list of colors for each speaker gets a unique color distributed evenly across the hue spectrum
  // TODO: change following spect
  List<int> get colors {
    if (speakers.isEmpty) {
      return const <int>[];
    }

    const double maxHue = 360.0;
    const double saturation = 0.6;
    const double value = 0.8;
    const double alpha = 1.0;

    final double hueStep = maxHue / speakers.length;
    
    return List.generate(
      speakers.length,
      (final int index) {
        final double hue = (index * hueStep) % maxHue;
        final HSVColor hsvColor = HSVColor.fromAHSV(
          alpha,
          hue,
          saturation,
          value,
        );
        return hsvColor.toColor().toARGB32();
      },
    );
  }

  /// Returns the color of a speaker by its label
  int getColorSpeaker(String speakerLabel) {
    final index = speakers.indexWhere((speaker) => speaker.label == speakerLabel);
    return colors[index];
  }

  /// Returns the index of a transcription by its time
  int findTranscriptionIndexByTime(int seconds) {
    return transcriptions.indexWhere((transcription) => transcription.startTime <= seconds && transcription.endTime > seconds);
  }

  TranscriptionAudioEntity copyWith({
    String? id,
    String? language,
    int? durationSeconds,
    String? status,
    DateTime? createdAt,
    String? audioUrl,
    int? speakerCount,
    List<TranscriptionItemEntity>? transcriptions,
    String? fullText,
    List<TranscriptionSpeakerEntity>? speakers,
    double? confidence,
  }) {
    return TranscriptionAudioEntity(
      id: id ?? this.id,
      language: language ?? this.language,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      audioUrl: audioUrl ?? this.audioUrl,
      speakerCount: speakerCount ?? this.speakerCount,
      transcriptions: transcriptions ?? this.transcriptions,
      fullText: fullText ?? this.fullText,
      speakers: speakers ?? this.speakers,
      confidence: confidence ?? this.confidence,
    );
  }
}