import 'package:equatable/equatable.dart';

/// Domain entity representing the result of a transcription upload
class TranscriptionUpload<PERSON><PERSON>ult extends Equatable {
  const TranscriptionUploadResult({
    required this.success,
    required this.message,
    this.transcriptionData,
    this.errorMessage,
  });

  /// Whether the upload was successful
  final bool success;

  /// Response message from server
  final String message;

  /// Upload data details (null if upload failed)
  final TranscriptionData? transcriptionData;

  /// Error message if upload failed
  final String? errorMessage;

  /// Factory constructor for successful upload
  factory TranscriptionUploadResult.success({
    required String message,
    required TranscriptionData transcriptionData,
  }) {
    return TranscriptionUploadResult(
      success: true,
      message: message,
      transcriptionData: transcriptionData,
    );
  }

  /// Factory constructor for failed upload
  factory TranscriptionUploadResult.failure({
    required String errorMessage,
  }) {
    return TranscriptionUploadResult(
      success: false,
      message: errorMessage,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props =>
      [success, message, transcriptionData, errorMessage];
}

/// Domain entity representing transcription data
class TranscriptionData extends Equatable {
  const TranscriptionData({
    required this.id,
    required this.userId,
    required this.path,
    required this.filename,
    required this.originalName,
    required this.size,
    required this.mimeType,
    required this.status,
    required this.storageDisk,
    required this.url,
  });

  /// Unique identifier for the uploaded file
  final int id;

  /// User ID who uploaded the file
  final int userId;

  /// File path on the server
  final String path;

  /// Generated filename on the server
  final String filename;

  /// Original filename from the client
  final String originalName;

  /// File size in bytes
  final int size;

  /// MIME type of the file
  final String mimeType;

  /// Processing status (pending, processing, completed, failed)
  final String status;

  /// Storage disk type (s3, local, etc.)
  final String storageDisk;

  /// Public URL to access the file
  final String url;

  @override
  List<Object?> get props => [
        id,
        userId,
        path,
        filename,
        originalName,
        size,
        mimeType,
        status,
        storageDisk,
        url,
      ];
}
