import 'package:equatable/equatable.dart';
import 'package:glidic_app/features/recording/data/models/transcription_item_model.dart';

/// Entity representing a transcription on a list of transcriptions 
/// on transcription audio
class TranscriptionItemEntity extends Equatable {
  const TranscriptionItemEntity({
    required this.startTime,
    required this.endTime,
    required this.text,
    required this.speaker,
  });

  final int startTime;
  final int endTime;
  final String text;
  final String speaker;

  factory TranscriptionItemEntity.fromModel(TranscriptionItemModel model) {
    return TranscriptionItemEntity(
      startTime: model.startTime.toInt(),
      endTime: model.endTime.toInt(),
      text: model.text,
      speaker: model.speaker,
    );
  }

  @override
  List<Object?> get props => [
        startTime,
        endTime,
        text,
        speaker,
      ];
}