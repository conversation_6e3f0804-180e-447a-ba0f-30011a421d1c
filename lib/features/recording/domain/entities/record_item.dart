import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';
import 'package:glidic_app/core/data/datasources/database/app_database.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_status.dart';

/// Entity representing a recorded audio file
class RecordItem extends Equatable {
  const RecordItem({
    required this.id,
    required this.title,
    required this.filePath,
    required this.duration,
    required this.createdAt,
    required this.fileSize,
    this.waveformData,
    this.isPlaying = false,
    this.playbackPosition = Duration.zero,
    this.status = UploadStatus.pending,
    this.remoteUrl,
    this.transcriptionId,
  });

  /// Unique identifier for the recording
  final String id;

  /// Display title of the recording
  final String title;

  /// File path where the recording is stored
  final String filePath;

  /// Duration of the recording
  final Duration duration;

  /// When the recording was created
  final DateTime createdAt;

  /// File size in bytes
  final int fileSize;

  /// Waveform data for visualization
  final List<double>? waveformData;

  /// Whether the recording is currently playing
  final bool isPlaying;

  /// Current playback position
  final Duration playbackPosition;

  /// Upload status of the recording
  final UploadStatus status;

  /// Remote URL after successful upload
  final String? remoteUrl;

  /// Transcription ID from transcription service
  final int? transcriptionId;

  /// Create a copy with updated fields
  RecordItem copyWith({
    String? id,
    String? title,
    String? filePath,
    Duration? duration,
    DateTime? createdAt,
    int? fileSize,
    List<double>? waveformData,
    bool? isPlaying,
    Duration? playbackPosition,
    UploadStatus? status,
    String? remoteUrl,
    int? transcriptionId,
  }) {
    return RecordItem(
      id: id ?? this.id,
      title: title ?? this.title,
      filePath: filePath ?? this.filePath,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      fileSize: fileSize ?? this.fileSize,
      waveformData: waveformData ?? this.waveformData,
      isPlaying: isPlaying ?? this.isPlaying,
      playbackPosition: playbackPosition ?? this.playbackPosition,
      status: status ?? this.status,
      remoteUrl: remoteUrl ?? this.remoteUrl,
      transcriptionId: transcriptionId ?? this.transcriptionId,
    );
  }

  static RecordItem fromDataTable(RecordsTableData data) {
    List<double>? waveformData;
    if (data.waveformData != null) {
      try {
        final List<dynamic> decoded = jsonDecode(data.waveformData!);
        waveformData = decoded.map((e) => (e as num).toDouble()).toList();
      } catch (e) {
        waveformData = null;
      }
    }

    return RecordItem(
      id: data.id,
      title: data.title,
      filePath: data.filePath,
      duration: Duration(milliseconds: data.durationMs),
      createdAt: data.createdAt,
      fileSize: data.fileSize,
      waveformData: waveformData,
      isPlaying: data.isPlaying,
      playbackPosition: Duration(milliseconds: data.playbackPositionMs),
      status: UploadStatus.fromString(data.status),
      remoteUrl: data.remoteUrl,
      transcriptionId: data.transcriptionId,
    );
  }

  /// Convert list of RecordsTableData to list of RecordItem entities
  static List<RecordItem> fromTableDataList(
    List<RecordsTableData> dataList,
  ) {
    return dataList.map((data) => RecordItem.fromDataTable(data)).toList();
  }

  /// Format duration as HH:MM:SS
  String get formattedDuration {
    return duration.format('hh:mm:ss');
  }

  /// Format file size in human readable format
  String get formattedFileSize {
    return fileSize.readableFileSize;
  }

  /// Format creation date
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays == 0) {
      return 'Today ${_formatTime(createdAt)}';
    } else if (difference.inDays == 1) {
      return 'Yesterday ${_formatTime(createdAt)}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get upload status display text
  String get statusText {
    switch (status) {
      case UploadStatus.pending:
        return 'Pending Upload';
      case UploadStatus.uploading:
        return 'Uploading...';
      case UploadStatus.finished:
        return 'Completed';
      case UploadStatus.failed:
        return 'Upload Failed';
      case UploadStatus.cancelled:
        return 'Upload Cancelled';
    }
  }

  /// Check if recording can be uploaded
  bool get canUpload => status.canStartUpload;

  /// Check if upload is in progress
  bool get isUploading => status.isUploading;

  /// Check if upload is completed
  bool get isUploadCompleted => status.isCompleted;

  /// Check if recording has remote URL
  bool get hasRemoteUrl => remoteUrl != null && remoteUrl!.isNotEmpty;

  /// Check if recording has transcription ID
  bool get hasTranscriptionId => transcriptionId != null;

  @override
  List<Object?> get props => [
        id,
        title,
        filePath,
        duration,
        createdAt,
        fileSize,
        waveformData,
        isPlaying,
        playbackPosition,
        status,
        remoteUrl,
        transcriptionId,
      ];
}
