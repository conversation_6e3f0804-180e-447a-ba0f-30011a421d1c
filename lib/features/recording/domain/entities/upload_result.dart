import 'package:equatable/equatable.dart';

/// Domain entity representing the result of an upload operation
///
/// This entity contains information about the upload result including
/// success status, remote URL, error information, and metadata.
class UploadResult extends Equatable {
  /// Creates a new upload result
  ///
  /// [isSuccess] - Whether the upload was successful
  /// [remoteUrl] - The URL where the file was uploaded (if successful)
  /// [errorMessage] - Error message if upload failed
  /// [uploadedAt] - When the upload was completed
  /// [fileSize] - Size of the uploaded file in bytes
  /// [uploadDuration] - How long the upload took
  /// [retryCount] - Number of retry attempts made
  const UploadResult({
    required this.isSuccess,
    this.remoteUrl,
    this.errorMessage,
    this.uploadedAt,
    this.fileSize,
    this.uploadDuration,
    this.retryCount = 0,
  });

  /// Whether the upload was successful
  final bool isSuccess;

  /// The URL where the file was uploaded (if successful)
  final String? remoteUrl;

  /// Error message if upload failed
  final String? errorMessage;

  /// When the upload was completed
  final DateTime? uploadedAt;

  /// Size of the uploaded file in bytes
  final int? fileSize;

  /// How long the upload took
  final Duration? uploadDuration;

  /// Number of retry attempts made
  final int retryCount;

  /// Create a successful upload result
  ///
  /// [remoteUrl] - The URL where the file was uploaded
  /// [uploadedAt] - When the upload was completed (defaults to now)
  /// [fileSize] - Size of the uploaded file in bytes
  /// [uploadDuration] - How long the upload took
  factory UploadResult.success({
    required String remoteUrl,
    DateTime? uploadedAt,
    int? fileSize,
    Duration? uploadDuration,
    int retryCount = 0,
  }) {
    return UploadResult(
      isSuccess: true,
      remoteUrl: remoteUrl,
      uploadedAt: uploadedAt ?? DateTime.now(),
      fileSize: fileSize,
      uploadDuration: uploadDuration,
      retryCount: retryCount,
    );
  }

  /// Create a failed upload result
  ///
  /// [errorMessage] - Error message describing the failure
  /// [retryCount] - Number of retry attempts made
  factory UploadResult.failure({
    required String errorMessage,
    int retryCount = 0,
  }) {
    return UploadResult(
      isSuccess: false,
      errorMessage: errorMessage,
      retryCount: retryCount,
    );
  }

  /// Create a copy of this result with updated values
  UploadResult copyWith({
    bool? isSuccess,
    String? remoteUrl,
    String? errorMessage,
    DateTime? uploadedAt,
    int? fileSize,
    Duration? uploadDuration,
    int? retryCount,
  }) {
    return UploadResult(
      isSuccess: isSuccess ?? this.isSuccess,
      remoteUrl: remoteUrl ?? this.remoteUrl,
      errorMessage: errorMessage ?? this.errorMessage,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      fileSize: fileSize ?? this.fileSize,
      uploadDuration: uploadDuration ?? this.uploadDuration,
      retryCount: retryCount ?? this.retryCount,
    );
  }

  /// Convert this result to a Map for storage
  Map<String, dynamic> toMap() {
    return {
      'isSuccess': isSuccess,
      'remoteUrl': remoteUrl,
      'errorMessage': errorMessage,
      'uploadedAt': uploadedAt?.millisecondsSinceEpoch,
      'fileSize': fileSize,
      'uploadDuration': uploadDuration?.inMilliseconds,
      'retryCount': retryCount,
    };
  }

  /// Create a result from a Map
  factory UploadResult.fromMap(Map<String, dynamic> map) {
    return UploadResult(
      isSuccess: map['isSuccess'] as bool? ?? false,
      remoteUrl: map['remoteUrl'] as String?,
      errorMessage: map['errorMessage'] as String?,
      uploadedAt: map['uploadedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['uploadedAt'] as int)
          : null,
      fileSize: map['fileSize'] as int?,
      uploadDuration: map['uploadDuration'] != null
          ? Duration(milliseconds: map['uploadDuration'] as int)
          : null,
      retryCount: map['retryCount'] as int? ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        isSuccess,
        remoteUrl,
        errorMessage,
        uploadedAt,
        fileSize,
        uploadDuration,
        retryCount,
      ];

  @override
  String toString() {
    if (isSuccess) {
      return 'UploadResult.success(url: $remoteUrl, size: $fileSize bytes, duration: ${uploadDuration?.inSeconds}s)';
    } else {
      return 'UploadResult.failure(error: $errorMessage, retries: $retryCount)';
    }
  }
}
