/// Entity representing a speaker on a transcription audio
class TranscriptionSpeakerEntity {
  final String speakerId;
  final String label;

  const TranscriptionSpeakerEntity({
    required this.speakerId,
    required this.label,
  });

  TranscriptionSpeakerEntity copyWith({
    String? speakerId,
    String? label,
  }) {
    return TranscriptionSpeakerEntity(
      speakerId: speakerId ?? this.speakerId,
      label: label ?? this.label,
    );
  }
}