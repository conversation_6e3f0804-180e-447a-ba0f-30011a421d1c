import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/extensions/duration_extensions.dart';

/// Domain entity for Live Activity recording data
class RecordingLiveActivityEntity extends Equatable {
  /// Creates a new recording Live Activity entity
  const RecordingLiveActivityEntity({
    required this.title,
    required this.duration,
    required this.status,
    this.amplitude = 0.0,
    this.isRecording = false,
    this.isPaused = false,
    this.timestamp,
    this.waveformAmplitudes,
  });

  /// The title/name of the recording
  final String title;

  /// Current recording duration in milliseconds
  final int duration;

  /// Current recording status
  final RecordingActivityStatus status;

  /// Current audio amplitude (0.0 to 1.0) for waveform visualization
  final double amplitude;

  /// Whether recording is currently active
  final bool isRecording;

  /// Whether recording is currently paused
  final bool isPaused;

  /// When this model was created/updated
  final DateTime? timestamp;

  /// Waveform amplitudes for Live Activity visualization
  final List<double>? waveformAmplitudes;

  /// Create an entity for starting a recording
  ///
  /// This factory constructor creates an entity suitable for starting
  /// a new recording session with initial values.
  factory RecordingLiveActivityEntity.start({
    required String title,
    required DateTime startTime,
  }) {
    return RecordingLiveActivityEntity(
      title: title,
      duration: 0,
      status: RecordingActivityStatus.recording,
      amplitude: 0.0,
      isRecording: true,
      isPaused: false,
      timestamp: startTime,
      waveformAmplitudes: const [],
    );
  }

  /// Create an entity with elapsed time calculation
  factory RecordingLiveActivityEntity.withElapsedTime({
    required DateTime startTime,
    required bool isRecording,
    required String title,
    required String recordingState,
    List<double>? waveformAmplitudes,
  }) {
    final now = DateTime.now();
    final duration = now.difference(startTime).inMilliseconds;

    return RecordingLiveActivityEntity(
      title: title,
      duration: duration,
      status: RecordingActivityStatus.fromString(recordingState),
      amplitude: waveformAmplitudes?.isNotEmpty == true
          ? waveformAmplitudes!.last
          : 0.0,
      isRecording: isRecording,
      isPaused: false,
      timestamp: now,
      waveformAmplitudes: waveformAmplitudes,
    );
  }

  /// Create a copy of this entity with updated values
  RecordingLiveActivityEntity copyWith({
    String? title,
    int? duration,
    RecordingActivityStatus? status,
    double? amplitude,
    bool? isRecording,
    bool? isPaused,
    DateTime? timestamp,
    List<double>? waveformAmplitudes,
  }) {
    return RecordingLiveActivityEntity(
      title: title ?? this.title,
      duration: duration ?? this.duration,
      status: status ?? this.status,
      amplitude: amplitude ?? this.amplitude,
      isRecording: isRecording ?? this.isRecording,
      isPaused: isPaused ?? this.isPaused,
      timestamp: timestamp ?? this.timestamp,
      waveformAmplitudes: waveformAmplitudes ?? this.waveformAmplitudes,
    );
  }

  /// Get formatted duration string for display
  String get formattedDuration {
    final durationValue = Duration(milliseconds: duration);
    return durationValue.format('mm:ss');
  }

  /// Get display text for the current status
  String get statusDisplayText {
    switch (status) {
      case RecordingActivityStatus.recording:
        return 'Recording...';
      case RecordingActivityStatus.paused:
        return 'Paused';
      case RecordingActivityStatus.stopped:
        return 'Stopped';
      case RecordingActivityStatus.saved:
        return 'Saved';
      case RecordingActivityStatus.cancelled:
        return 'Cancelled';
      case RecordingActivityStatus.idle:
        return 'Ready';
    }
  }

  @override
  List<Object?> get props => [
        title,
        duration,
        status,
        amplitude,
        isRecording,
        isPaused,
        timestamp,
        waveformAmplitudes,
      ];

  @override
  String toString() {
    return 'RecordingLiveActivityEntity('
        'title: $title, '
        'duration: $formattedDuration, '
        'status: ${status.name}, '
        'amplitude: ${amplitude.toStringAsFixed(2)}, '
        'isRecording: $isRecording, '
        'isPaused: $isPaused'
        ')';
  }
}

/// Enumeration of possible recording activity statuses
enum RecordingActivityStatus {
  /// Recording is ready but not started
  idle,

  /// Recording is currently active
  recording,

  /// Recording is paused
  paused,

  /// Recording has been stopped
  stopped,

  /// Recording has been saved
  saved,

  /// Recording has been cancelled
  cancelled;

  /// Create status from string representation
  static RecordingActivityStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'recording':
        return RecordingActivityStatus.recording;
      case 'paused':
        return RecordingActivityStatus.paused;
      case 'stopped':
        return RecordingActivityStatus.stopped;
      case 'saved':
        return RecordingActivityStatus.saved;
      case 'cancelled':
        return RecordingActivityStatus.cancelled;
      case 'idle':
      default:
        return RecordingActivityStatus.idle;
    }
  }

  /// Get string representation of the status
  String get name {
    switch (this) {
      case RecordingActivityStatus.idle:
        return 'idle';
      case RecordingActivityStatus.recording:
        return 'recording';
      case RecordingActivityStatus.paused:
        return 'paused';
      case RecordingActivityStatus.stopped:
        return 'stopped';
      case RecordingActivityStatus.saved:
        return 'saved';
      case RecordingActivityStatus.cancelled:
        return 'cancelled';
    }
  }
}
