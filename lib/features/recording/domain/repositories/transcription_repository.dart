import 'dart:io';

import 'package:glidic_app/features/recording/domain/entities/transcription.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_audio_entity.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_upload_result.dart';

/// Repository interface for transcription operations
abstract class TranscriptionRepository {
  /// Upload an audio file for transcription
  Future<TranscriptionUploadResult> uploadTranscription({
    required File file,
    required int userId,
    void Function(double progress)? onProgress,
  });

  /// Get list of transcriptions for a specific user
  Future<List<Transcription>> getTranscriptions({
    required int userId,
    int page = 1,
    int limit = 20,
  });

  /// Get transcription audio by ID
  Future<TranscriptionAudioEntity?> getTranscriptionAudio(String audioId);

  /// Get a specific transcription by ID
  Future<Transcription?> getTranscriptionById(int transcriptionId);

  /// Check if the transcription service is available
  Future<bool> isServiceAvailable();
}
