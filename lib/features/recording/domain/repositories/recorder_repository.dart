import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';

/// Abstract repository for recording operations
abstract class RecorderRepository {
  /// Start a new recording session
  Future<RecorderSessionEntity> startRecording({
    required String title,
  });

  /// Stop the current recording session
  Future<RecorderSessionEntity> stopRecording({
    required String sessionId,
  });

  /// Get the current recording session
  Future<RecorderSessionEntity?> getCurrentSession();

  /// Get all recording sessions
  Future<List<RecorderSessionEntity>> getAllSessions();

  /// Delete a recording session
  Future<void> deleteSession({
    required String sessionId,
  });

  /// Get recording session by ID
  Future<RecorderSessionEntity> getSessionById({
    required String sessionId,
  });

  /// Set the recorder controller for recording operations
  void setRecorderController(RecorderController controller);
}
