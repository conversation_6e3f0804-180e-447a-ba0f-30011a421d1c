import 'package:glidic_app/features/recording/domain/entities/record_item.dart';

/// Repository interface for managing recorded audio files
abstract class RecordsRepository {
  /// Get all recorded audio files
  Future<List<RecordItem>> getAllRecords();

  /// Get a specific recording by ID
  Future<RecordItem?> getRecordById(String id);

  /// Delete a recording by ID
  Future<bool> deleteRecord(String id);

  /// Update a recording
  Future<bool> updateRecord(RecordItem recording);

  /// Search recordings by title
  Future<List<RecordItem>> searchRecords(String query);

  /// Get recordings sorted by date (newest first by default)
  Future<List<RecordItem>> getRecordsSorted({
    bool newestFirst = true,
  });

  /// Get recordings filtered by date range
  Future<List<RecordItem>> getRecordsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get total storage used by recordings
  Future<int> getTotalStorageUsed();

  /// Export recording to external storage
  Future<bool> exportRecord(String id, String destinationPath);

  /// Share recording file
  Future<bool> shareRecord(String id);

  Future<void> addRecord({
    required String id,
    required String title,
    required String filePath,
    required Duration duration,
    required DateTime createdAt,
    required int fileSize,
    List<double>? waveformData,
  }) async {}
}
