import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_audio_entity.dart';
import 'package:glidic_app/features/recording/domain/repositories/transcription_repository.dart';

class GetTranscriptionAudioUseCase {
  final TranscriptionRepository _transcriptionRepository;

  GetTranscriptionAudioUseCase({
    required TranscriptionRepository transcriptionRepository,
  }) : _transcriptionRepository = transcriptionRepository;

  Future<TranscriptionAudioEntity> execute(String audioId) async {
    final transcriptionAudio = await _transcriptionRepository.getTranscriptionAudio(audioId);
    if (transcriptionAudio == null) {
      throw const NotFoundFailure('Transcription audio not found');
    }
    return transcriptionAudio;
  }
}