import 'package:equatable/equatable.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:glidic_app/features/recording/domain/repositories/recorder_repository.dart';
import 'package:glidic_app/features/recording/domain/services/live_activity_service.dart';

/// Use case for stopping a recording session
class StopRecordingUseCase {
  const StopRecordingUseCase({
    required this.repository,
    required this.liveActivityService,
  });

  final RecorderRepository repository;
  final LiveActivityService liveActivityService;

  /// Execute the use case with Live Activity termination
  Future<RecorderSessionEntity> execute(StopRecordingParams params) async {
    try {
      // Stop recording
      final session =
          await repository.stopRecording(sessionId: params.sessionId);

      // End Live Activity
      await liveActivityService.endRecordingActivity();
      return session;
    } catch (e) {
      rethrow;
    }
  }
}

/// Parameters for stopping a recording
class StopRecordingParams extends Equatable {
  const StopRecordingParams({required this.sessionId});

  final String sessionId;

  @override
  List<Object> get props => [sessionId];
}
