import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/features/recording/domain/entities/recording_live_activity_entity.dart';
import 'package:glidic_app/features/recording/domain/services/live_activity_service.dart';

/// Callback function for recording updates
typedef RecordingUpdateCallback = void Function(
  int durationMs,
  double amplitude,
);

/// Use case for managing Live Activity updates during recording
class UpdateLiveActivityUseCase {
  const UpdateLiveActivityUseCase({
    required this.liveActivityService,
  });

  final LiveActivityService liveActivityService;

  /// Execute Live Activity updates with unified timer
  Future<StreamSubscription> execute(StartRecordingUpdatesParams params) async {
    try {
      // Initialize amplitude buffer
      final amplitudeBuffer = <double>[];
      var currentDuration = 0;
      final updateInterval = Constants.liveActivityUpdateInterval;
      final subscription = Stream.periodic(
        Duration(milliseconds: updateInterval),
      ).listen((_) async {
        try {
          // Update duration
          currentDuration += updateInterval;

          // Process amplitude
          final amplitude = _processAmplitudeFromWaveData(
            params.recorderController?.waveData,
            amplitudeBuffer,
          );

          // Notify callback with both duration and amplitude
          params.onUpdate(currentDuration, amplitude);

          // Update Live Activity every second (every 2 iterations)
          if (currentDuration % 1000 == 0) {
            await _updateLiveActivity(
              title: params.title,
              durationMs: currentDuration,
              amplitudeBuffer: amplitudeBuffer,
            );
          }
        } catch (e) {
          log(
            'Error in recording updates: $e',
            name: 'UpdateLiveActivityUseCase',
          );
        }
      });

      return subscription;
    } catch (e) {
      log(
        'Error starting recording updates: $e',
        name: 'UpdateLiveActivityUseCase',
      );
      rethrow;
    }
  }

  /// Update Live Activity with current recording status
  Future<void> _updateLiveActivity({
    required String title,
    required int durationMs,
    required List<double> amplitudeBuffer,
  }) async {
    if (!Platform.isIOS) return;

    try {
      final waveformData = _getWaveformForLiveActivity(amplitudeBuffer);
      final startTime =
          DateTime.now().subtract(Duration(milliseconds: durationMs));

      final entity = RecordingLiveActivityEntity.withElapsedTime(
        startTime: startTime,
        isRecording: true,
        title: title,
        recordingState: Constants.recordingStatusRecording,
        waveformAmplitudes: waveformData,
      );

      await liveActivityService.updateRecordingActivity(entity);
    } catch (e) {
      log(
        'Failed to update Live Activity: $e',
        name: 'UpdateLiveActivityUseCase',
      );
    }
  }

  /// Process amplitude from waveform data
  double _processAmplitudeFromWaveData(
    dynamic rawWaveData,
    List<double> amplitudeBuffer,
  ) {
    try {
      double amplitude;

      if (rawWaveData != null && rawWaveData.isNotEmpty) {
        amplitude = rawWaveData.last.abs() as double;
      } else {
        amplitude = 0;
      }

      // Add to buffer with size management
      amplitudeBuffer.add(amplitude);
      if (amplitudeBuffer.length > Constants.maxAmplitudeBufferSize) {
        amplitudeBuffer.removeAt(0);
      }

      return amplitude;
    } catch (e) {
      return 0;
    }
  }

  /// Get waveform data for Live Activity display
  List<double>? _getWaveformForLiveActivity(List<double> amplitudeBuffer) {
    if (amplitudeBuffer.isEmpty) return null;

    final targetSize = Constants.maxAmplitudeBufferSize;

    if (amplitudeBuffer.length <= targetSize) {
      return List<double>.from(amplitudeBuffer);
    }

    final step = amplitudeBuffer.length / targetSize;
    final sampledData = <double>[];

    for (int i = 0; i < targetSize; i++) {
      final index = (i * step).floor();
      if (index < amplitudeBuffer.length) {
        sampledData.add(amplitudeBuffer[index]);
      }
    }

    return sampledData;
  }
}

/// Parameters for starting recording updates
class StartRecordingUpdatesParams extends Equatable {
  const StartRecordingUpdatesParams({
    required this.title,
    required this.recorderController,
    required this.onUpdate,
  });

  final String title;
  final dynamic recorderController;
  final RecordingUpdateCallback onUpdate;

  @override
  List<Object?> get props => [title, recorderController, onUpdate];
}
