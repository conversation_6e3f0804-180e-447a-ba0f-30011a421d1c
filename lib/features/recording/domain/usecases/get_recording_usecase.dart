import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/repositories/records_repository.dart';

class GetRecordingUseCase {
  final RecordsRepository _recordsRepository;

  GetRecordingUseCase({
    required RecordsRepository recordsRepository,
  }) : _recordsRepository = recordsRepository;

  Future<RecordItem> execute(String id) async {
    final record = await _recordsRepository.getRecordById(id);
    if (record == null) {
      throw const NotFoundFailure('Record not found');
    }
    return record;
  }
}
