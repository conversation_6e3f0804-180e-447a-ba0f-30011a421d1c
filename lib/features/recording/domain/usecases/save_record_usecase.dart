import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/recording/data/services/upload_worker_service.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:glidic_app/features/recording/domain/repositories/records_repository.dart';

/// Use case for saving a recording session to the database and triggering upload
/// This consolidates the save and upload triggering operations
class SaveRecordUseCase {
  const SaveRecordUseCase({
    required RecordsRepository recordsRepository,
    required UploadWorkerService uploadWorkerService,
  })  : _recordsRepository = recordsRepository,
        _uploadWorkerService = uploadWorkerService;

  final RecordsRepository _recordsRepository;
  final UploadWorkerService _uploadWorkerService;

  /// Execute the use case to save recording and trigger upload
  Future<void> execute(SaveRecordParams params) async {
    try {
      final filePath = params.session.filePath;
      final startTime = params.session.startTime;

      // Validate required fields
      if (filePath == null || filePath.isEmpty) {
        throw AppFailureConstructors.recordingFileFailure(
          'Recording file path is required',
        );
      }

      if (startTime == null) {
        throw AppFailureConstructors.recordingInvalidState(
          'Recording start time is required',
        );
      }

      // Save to database
      await _recordsRepository.addRecord(
        id: params.session.id,
        title: params.session.title,
        filePath: filePath,
        duration: Duration(milliseconds: params.session.durationMs),
        createdAt: startTime,
        fileSize: params.session.fileSizeBytes ?? 0,
        waveformData: params.waveformData,
      );

      // Trigger upload for the newly saved recording
      await _triggerUpload(params.session.id);
    } catch (e) {
      // Re-throw AppFailure types (including RecordingFailure subclasses)
      if (e is AppFailure) {
        rethrow;
      }

      // Convert generic exceptions to specific recording failures
      throw AppFailureConstructors.recordingSaveFailed(
        'Failed to save recording: ${e.toString()}',
      );
    }
  }

  /// Trigger upload for a newly saved recording
  Future<void> _triggerUpload(String recordingId) async {
    try {
      await _uploadWorkerService.onRecordingSaved(recordingId);
    } catch (e) {
      // Don't throw - upload triggering failure shouldn't affect save operation
    }
  }
}

/// Parameters for saving recording
class SaveRecordParams extends Equatable {
  const SaveRecordParams({
    required this.session,
    this.waveformData,
  });

  final RecorderSessionEntity session;
  final List<double>? waveformData;

  @override
  List<Object?> get props => [session, waveformData];
}
