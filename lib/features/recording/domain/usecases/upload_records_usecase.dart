import 'dart:developer';
import 'dart:io';

import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_upload_result.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_result.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_status.dart';
import 'package:glidic_app/features/recording/domain/repositories/records_repository.dart';
import 'package:glidic_app/features/recording/domain/repositories/transcription_repository.dart';

/// Use case for uploading recordings following clean architecture patterns
///
/// This use case handles the business logic for finding recordings that need
/// to be uploaded and processing them through the transcription API service.
///
/// Simplified Upload Flow:
/// 1. Find recordings with "pending" status
/// 2. Update status to "uploading" when starting upload
/// 3. Upload file to transcription server and get remote URL
/// 4. Update status to "finished" with remote URL to complete the process
///
/// Features:
/// - Finds recordings with "pending" status
/// - Uploads files to transcription service for processing
/// - Updates record status and remote URL after upload
/// - Handles upload failures with retry logic
/// - Comprehensive error handling and logging
/// - Integration with transcription API for audio processing
class UploadRecordsUseCase {
  /// Creates a new upload recordings use case
  ///
  /// [recordsRepository] - Repository for accessing record data
  /// [transcriptionRepository] - Repository for transcription API operations
  /// [authRepository] - Repository for authentication and user data
  const UploadRecordsUseCase({
    required RecordsRepository recordsRepository,
    required TranscriptionRepository transcriptionRepository,
  })  : _recordsRepository = recordsRepository,
        _transcriptionRepository = transcriptionRepository;

  final RecordsRepository _recordsRepository;
  final TranscriptionRepository _transcriptionRepository;

  /// Maximum number of retry attempts for failed uploads
  static const int maxRetryAttempts = 3;

  /// Execute the upload process for all pending recordings
  ///
  /// This method finds all recordings with "pending" status and attempts
  /// to upload them to remote storage. It handles failures gracefully
  /// and updates the record status accordingly.
  ///
  /// Returns a list of upload results for all processed recordings

  Future<List<UploadResult>> execute() async {
    // Check if transcription service is available
    final isAvailable = await _transcriptionRepository.isServiceAvailable();
    if (!isAvailable) {
      throw AppFailureConstructors.noConnection();
    }

    // Get all recordings that need to be uploaded
    final pendingRecords = await _getPendingRecords();

    if (pendingRecords.isEmpty) {
      return [];
    }

    final results = <UploadResult>[];

    // Process each recording
    for (final record in pendingRecords) {
      try {
        final result = await _uploadRecord(record);
        results.add(result);
      } catch (e) {
        // Create a failure result for this record
        final failureResult = UploadResult.failure(
          errorMessage: e.toString(),
        );
        results.add(failureResult);

        // Update record status to failed
        await _updateRecordStatus(record, UploadStatus.failed);
      }
    }

    return results;
  }

  /// Upload a single recording
  Future<UploadResult> _uploadRecord(RecordItem record) async {
    // Validate file exists
    final file = File(record.filePath);
    if (!await file.exists()) {
      throw AppFailureConstructors.recordingFileFailure(
        'Recording file not found: ${record.filePath}',
      );
    }

    final maxFileSize = Constants.maxUploadFileSize;
    final fileSize = await file.length();
    if (fileSize > maxFileSize) {
      throw AppFailureConstructors.recordingFileSizeExceeded(
        'File size exceeds upload limit: $fileSize bytes (max: $maxFileSize bytes)',
      );
    }

    // Update status to uploading
    await _updateRecordStatus(record, UploadStatus.uploading);

    try {
      /* TODO: Fake user ID for testing
      // Get current user ID from authentication service
      final currentUser = await _authRepository.getCurrentUser();
      if (currentUser == null) {
        throw const recording_failures.UploadAuthenticationFailure(
          message:
              'User not authenticated. Please log in to upload recordings.',
          code: 'USER_NOT_AUTHENTICATED',
        );
      }

      final userId = int.tryParse(currentUser.id);
      if (userId == null) {
        throw const ValidationFailure(
          'Invalid user ID format',
          code: 'INVALID_USER_ID',
        );
      }
      */
      final userId = 1;
      // Perform the upload using transcription API
      final transcriptionResult =
          await _transcriptionRepository.uploadTranscription(
        file: file,
        userId: userId,
        onProgress: (progress) {},
      );

      // Convert transcription result to upload result for compatibility
      final result =
          _convertTranscriptionResultToUploadResult(transcriptionResult);

      if (result.isSuccess) {
        await _updateRecordAfterSuccessfulTranscriptionUpload(
          record,
          transcriptionResult,
        );
        await _updateRecordStatus(record, UploadStatus.finished);
      } else {
        await _handleUploadFailure(record, result);
      }

      return result;
    } catch (e) {
      // Handle upload exception
      await _updateRecordStatus(record, UploadStatus.failed);
      rethrow;
    }
  }

  /// Get all records that need to be uploaded
  Future<List<RecordItem>> _getPendingRecords() async {
    try {
      final allRecords = await _recordsRepository.getAllRecords();

      // Filter records that need to be uploaded (status = pending)
      return allRecords.where((record) {
        return record.status == UploadStatus.pending;
      }).toList();
    } catch (e) {
      throw AppFailureConstructors.recordingFileFailure(
        'Failed to retrieve pending records: $e',
      );
    }
  }

  /// Update record status after upload operation
  Future<void> _updateRecordStatus(
    RecordItem record,
    UploadStatus status,
  ) async {
    try {
      // Update the record with new status using repository
      final updatedRecord = record.copyWith(status: status);
      await _recordsRepository.updateRecord(updatedRecord);
    } catch (e) {
      // Log error but don't throw - status update failure shouldn't stop upload
    }
  }

  /// Update record after successful transcription upload
  Future<void> _updateRecordAfterSuccessfulTranscriptionUpload(
    RecordItem record,
    TranscriptionUploadResult transcriptionResult,
  ) async {
    try {
      // Extract transcription ID and URL from the result
      final transcriptionId = transcriptionResult.transcriptionData?.id;
      final remoteUrl = transcriptionResult.transcriptionData?.url;

      // Update record with transcription information
      final updatedRecord = record.copyWith(
        transcriptionId: transcriptionId,
        remoteUrl: remoteUrl,
      );

      await _recordsRepository.updateRecord(updatedRecord);

      log(
        'Updated record ${record.id} with transcription ID: $transcriptionId and URL: $remoteUrl',
        name: 'UploadRecordsUseCase',
      );
    } catch (e) {
      log(
        'Failed to update record ${record.id} after transcription upload: $e',
        name: 'UploadRecordsUseCase',
      );
      rethrow;
    }
  }

  /// Handle upload failure with retry logic
  ///
  Future<void> _handleUploadFailure(
    RecordItem record,
    UploadResult result,
  ) async {
    final retryCount = result.retryCount;

    if (retryCount < maxRetryAttempts) {
      // Mark for retry by setting status back to pending
      await _updateRecordStatus(record, UploadStatus.pending);

      log(
        'Upload failed for record ${record.id}, will retry (attempt ${retryCount + 1}/$maxRetryAttempts)',
        name: 'UploadRecordsUseCase',
      );
    } else {
      // Max retries exceeded, mark as failed
      final updatedRecord = record.copyWith(
        status: UploadStatus.failed,
      );

      await _recordsRepository.updateRecord(updatedRecord);

      log(
        'Upload failed permanently for record ${record.id} after $maxRetryAttempts attempts: ${result.errorMessage}',
        name: 'UploadRecordsUseCase',
      );
    }
  }

  /// Convert TranscriptionUploadResult to UploadResult for compatibility
  UploadResult _convertTranscriptionResultToUploadResult(
    TranscriptionUploadResult transcriptionResult,
  ) {
    if (transcriptionResult.success) {
      // Extract URL from transcription data
      final remoteUrl = transcriptionResult.transcriptionData?.url ?? '';

      return UploadResult.success(
        remoteUrl: remoteUrl,
        fileSize: transcriptionResult.transcriptionData?.size,
        uploadedAt: DateTime.now(),
      );
    } else {
      return UploadResult.failure(
        errorMessage: transcriptionResult.errorMessage ??
            'Unknown transcription upload error',
      );
    }
  }
}
