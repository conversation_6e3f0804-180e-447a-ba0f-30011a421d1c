import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:glidic_app/features/recording/domain/entities/recording_live_activity_entity.dart';
import 'package:glidic_app/features/recording/domain/repositories/recorder_repository.dart';
import 'package:glidic_app/features/recording/domain/services/live_activity_service.dart';

/// Use case for starting a recording session
class StartRecordingUseCase {
  const StartRecordingUseCase({
    required this.repository,
    required this.liveActivityService,
  });

  final RecorderRepository repository;
  final LiveActivityService liveActivityService;

  /// Execute start recording operation with permission checking and Live Activity
  /// Throws AppFailure on error, returns success value directly
  Future<RecorderSessionEntity> execute(StartRecordingParams params) async {
    try {
      // Validate parameters
      if (!params.isValid) {
        throw AppFailureConstructors.recordingInvalidState(
          'Invalid recording parameters: ${params.sanitizedTitle}',
        );
      }

      // Check microphone permission using audio_waveforms built-in method
      final hasPermission =
          await _checkMicrophonePermission(params.recorderController);

      if (!hasPermission) {
        throw AppFailureConstructors.recordingPermissionDenied();
      }

      // Start the recording session
      final session = await repository.startRecording(title: params.title);

      // Start Live Activity if on iOS
      await _startLiveActivity(params.title);

      return session;
    } catch (e) {
      // Re-throw AppFailure types (including RecordingFailure subclasses)
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.recordingStartFailed(e.toString());
    }
  }

  /// Check microphone permission using audio_waveforms built-in method
  Future<bool> _checkMicrophonePermission(dynamic recorderController) async {
    try {
      if (recorderController == null) {
        return false;
      }

      final hasPermission = await recorderController.checkPermission();
      return hasPermission;
    } catch (e) {
      return false;
    }
  }

  Future<void> _startLiveActivity(String title) async {
    if (!Platform.isIOS) return;

    try {
      // Ensure service is initialized first
      await liveActivityService.initialize();

      // Check if an activity is already active
      final isActive = await liveActivityService.areActivitiesEnabled();
      if (isActive && liveActivityService.currentActivityId != null) {
        return; // Skip starting a new one
      }

      // Create live activity entity for recording start
      final entity = RecordingLiveActivityEntity.start(
        title: title,
        startTime: DateTime.now(),
      );

      await liveActivityService.startRecordingActivity(entity);
    } catch (e) {
      // Don't throw error - live activity is optional feature
    }
  }
}

/// Parameters for starting a recording
class StartRecordingParams extends Equatable {
  const StartRecordingParams({
    required this.title,
    required this.recorderController,
  });

  final String title;
  final dynamic recorderController;

  /// Validate parameters for business logic
  bool get isValid {
    return title.trim().isNotEmpty && recorderController != null;
  }

  /// Get sanitized title for safe usage
  String get sanitizedTitle {
    return title.trim().isEmpty ? 'Untitled Recording' : title.trim();
  }

  @override
  List<Object?> get props => [title, recorderController];
}
