import 'dart:developer';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';

/// Use case for deleting a recording file from storage with Live Activity handling
class DeleteRecordFileUseCase {
  /// Execute the use case to delete the recording file with Live Activity updates
  Future<bool> execute(DeleteRecordFileParams params) async {
    try {
      final filePath = params.session.filePath;

      // Check if file path exists
      if (filePath == null || filePath.isEmpty) {
        log('No file path to delete', name: 'DeleteRecordFileUseCase');
      } else {
        final file = File(filePath);

        if (await file.exists()) {
          await file.delete();
        }
      }

      return true;
    } catch (e) {
      throw Exception('Failed to delete recording file: $e');
    }
  }
}

/// Parameters for deleting recording file
class DeleteRecordFileParams extends Equatable {
  const DeleteRecordFileParams({
    required this.session,
  });

  final RecorderSessionEntity session;

  @override
  List<Object?> get props => [session];
}
