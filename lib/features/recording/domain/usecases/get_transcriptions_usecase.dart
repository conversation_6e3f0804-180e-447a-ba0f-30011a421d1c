import 'dart:developer';

import 'package:glidic_app/core/common/constants/network_constants.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription.dart';
import 'package:glidic_app/features/recording/domain/repositories/transcription_repository.dart';

/// Use case for retrieving transcriptions list
class GetTranscriptionsUseCase {
  const GetTranscriptionsUseCase({
    required TranscriptionRepository transcriptionRepository,
  }) : _transcriptionRepository = transcriptionRepository;

  final TranscriptionRepository _transcriptionRepository;

  /// Execute the get transcriptions process
  Future<List<Transcription>> execute({
    required int userId,
    int page = 1,
    int limit = NetworkConstants.defaultPerPage,
  }) async {
    try {
      // Validate parameters
      if (userId <= 0) {
        return [];
      }

      if (page <= 0) {
        page = 1;
      }

      if (limit <= 0) {
        limit = NetworkConstants.defaultPerPage;
      }

      // Check if service is available
      final isServiceAvailable =
          await _transcriptionRepository.isServiceAvailable();
      if (!isServiceAvailable) {
        return [];
      }

      // Get transcriptions from repository
      return await _transcriptionRepository.getTranscriptions(
        userId: userId,
        page: page,
        limit: limit,
      );
    } catch (e, stackTrace) {
      log(
        'Error getting transcriptions for user $userId: $e',
        name: 'GetTranscriptionsUseCase',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Get transcriptions with filtering options
  Future<List<Transcription>> executeWithFilter({
    required int userId,
    String? status,
    int page = 1,
    int limit = NetworkConstants.defaultPerPage,
  }) async {
    try {
      // Get all transcriptions first
      final allTranscriptions = await execute(
        userId: userId,
        page: page,
        limit: limit,
      );

      // Apply status filter if provided
      if (status != null && status.isNotEmpty) {
        return allTranscriptions
            .where(
              (transcription) =>
                  transcription.status.toLowerCase() == status.toLowerCase(),
            )
            .toList();
      }

      return allTranscriptions;
    } catch (e, stackTrace) {
      log(
        'Error getting filtered transcriptions for user $userId: $e',
        name: 'GetTranscriptionsUseCase',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Get transcriptions sorted by creation date
  Future<List<Transcription>> executeWithSorting({
    required int userId,
    bool ascending = false,
    int page = 1,
    int limit = NetworkConstants.defaultPerPage,
  }) async {
    try {
      // Get all transcriptions first
      final transcriptions = await execute(
        userId: userId,
        page: page,
        limit: limit,
      );

      // Sort by creation date
      transcriptions.sort((a, b) {
        if (ascending) {
          return a.createdAt.compareTo(b.createdAt);
        } else {
          return b.createdAt.compareTo(a.createdAt);
        }
      });

      return transcriptions;
    } catch (e, stackTrace) {
      log(
        'Error getting sorted transcriptions for user $userId: $e',
        name: 'GetTranscriptionsUseCase',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Get transcription statistics for a user
  Future<Map<String, int>> getTranscriptionStats({
    required int userId,
  }) async {
    try {
      // Get all transcriptions for the user
      final transcriptions = await execute(userId: userId, limit: 1000);

      // Calculate statistics
      return <String, int>{
        'total': transcriptions.length,
        'pending': transcriptions.where((t) => t.isPending).length,
        'processing': transcriptions.where((t) => t.isProcessing).length,
        'completed': transcriptions.where((t) => t.isCompleted).length,
        'failed': transcriptions.where((t) => t.isFailed).length,
      };
    } catch (e, stackTrace) {
      log(
        'Error getting transcription statistics for user $userId: $e',
        name: 'GetTranscriptionsUseCase',
        error: e,
        stackTrace: stackTrace,
      );
      return <String, int>{};
    }
  }
}
