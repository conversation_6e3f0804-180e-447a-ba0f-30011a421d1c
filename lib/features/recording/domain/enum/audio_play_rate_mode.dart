enum AudioPlayRateMode {
  x0_5,
  x1,
  x1_5,
  x2;

  AudioPlayRateMode get next {
    return AudioPlayRateMode.values[
        (AudioPlayRateMode.values.indexOf(this) + 1) %
            AudioPlayRateMode.values.length
      ];
  }

  double get value => switch (this) {
        x0_5 => 0.5,
        x1 => 1,
        x1_5 => 1.5,
        x2 => 2,
      };

  String get label => switch (this) {
        x0_5 => '0.5x',
        x1 => '1.0x',
        x1_5 => '1.5x',
        x2 => '2.0x',
      };
}
