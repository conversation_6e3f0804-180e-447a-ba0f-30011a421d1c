import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/features/recording/domain/entities/recording_live_activity_entity.dart';

/// Interface for Live Activity service following clean architecture patterns
/// This defines the contract for Live Activity operations in the domain layer
abstract class LiveActivityService {
  /// Initialize the Live Activity service
  ///
  /// [urlScheme] - Custom URL scheme for handling Live Activity interactions
  Future<void> initialize({
    String urlScheme = Constants.appUrlScheme,
  });

  /// Check if Live Activities are enabled on the device
  ///
  /// Returns [true] if Live Activities are supported and enabled,
  /// [false] otherwise or if an error occurs
  Future<bool> areActivitiesEnabled();

  /// Start a new recording Live Activity
  ///
  /// [entity] - The recording data entity to display in the Live Activity
  ///
  /// Returns the activity ID on success
  Future<String?> startRecordingActivity(
    RecordingLiveActivityEntity entity,
  );

  /// Update an existing recording Live Activity
  ///
  /// [entity] - The updated recording data entity
  ///
  /// Returns [true] on success
  Future<bool> updateRecordingActivity(
    RecordingLiveActivityEntity entity,
  );

  /// End the current recording Live Activity
  ///
  /// Returns [true] on success

  Future<bool> endRecordingActivity();

  /// Get the current active Live Activity ID
  ///
  /// Returns the activity ID if an activity is active, null otherwise
  String? get currentActivityId;

  /// Stream of URL scheme events from Live Activity interactions
  ///
  /// This stream emits events when users interact with buttons or
  /// other interactive elements in the Live Activity
  Stream<String> get urlSchemeStream;

  /// Dispose of resources and clean up
  void dispose();
}
