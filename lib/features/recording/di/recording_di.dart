import 'package:glidic_app/core/data/datasources/database/app_database.dart';
import 'package:glidic_app/core/data/datasources/database/daos/records_dao.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/recording/data/datasources/api_services/transcription_api_service.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/recorder_data_source.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/records_local_data_source.dart';
import 'package:glidic_app/features/recording/data/datasources/interfaces/transcription_remote_datasource.dart';
import 'package:glidic_app/features/recording/data/datasources/recorder_data_source_impl.dart';
import 'package:glidic_app/features/recording/data/datasources/records_local_data_source_impl.dart';
import 'package:glidic_app/features/recording/data/datasources/transcription_remote_datasource_impl.dart';
import 'package:glidic_app/features/recording/data/repositories/recording_repository_impl.dart';
import 'package:glidic_app/features/recording/data/repositories/records_repository_impl.dart';
import 'package:glidic_app/features/recording/data/repositories/transcription_repository_impl.dart';
import 'package:glidic_app/features/recording/data/services/live_activity_service_impl.dart';
import 'package:glidic_app/features/recording/data/services/upload_worker_service.dart';
import 'package:glidic_app/features/recording/data/workers/upload_worker.dart';
import 'package:glidic_app/features/recording/domain/repositories/recorder_repository.dart';
import 'package:glidic_app/features/recording/domain/repositories/records_repository.dart';
import 'package:glidic_app/features/recording/domain/repositories/transcription_repository.dart';
import 'package:glidic_app/features/recording/domain/services/live_activity_service.dart';
import 'package:glidic_app/features/recording/domain/usecases/delete_record_file_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/get_transcriptions_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/save_record_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/start_recording_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/stop_recording_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/update_live_activity_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/upload_records_usecase.dart';
import 'package:glidic_app/features/recording/presentation/screens/recording/recording_cubit.dart';
import 'package:glidic_app/features/recording/presentation/screens/records_list/records_list_cubit.dart';
import 'package:live_activities/live_activities.dart';

/// Dependency injection configuration for Recording feature
/// This class encapsulates all Recording-related dependency registrations
class RecordingDI {
  /// Initialize Recording feature dependencies
  static Future<void> init() async {
    // Database
    sl.registerLazySingleton<RecordsDao>(
      () => RecordsDao(sl<AppDatabase>()),
    );

    // Services
    sl.registerLazySingleton<LiveActivityService>(
      () => LiveActivityServiceImpl(
        liveActivitiesPlugin: LiveActivities(),
      ),
    );

    sl.registerLazySingleton<TranscriptionApiService>(
      () => TranscriptionApiService(sl()),
    );

    sl.registerLazySingleton<TranscriptionRemoteDataSource>(
      () => TranscriptionRemoteDataSourceImpl(
        sl<TranscriptionApiService>(),
      ),
    );

    // Data Sources
    sl.registerLazySingleton<RecorderDataSource>(
      () => RecorderDataSourceImpl(),
    );

    sl.registerLazySingleton<RecordsLocalDataSource>(
      () => RecordsLocalDataSourceImpl(sl<RecordsDao>()),
    );

    // Repositories
    sl.registerLazySingleton<RecorderRepository>(
      () => RecorderRepositoryImpl(
        dataSource: sl<RecorderDataSource>(),
      ),
    );

    sl.registerLazySingleton<RecordsRepository>(
      () => RecordsRepositoryImpl(
        dataSource: sl<RecordsLocalDataSource>(),
      ),
    );

    sl.registerLazySingleton<TranscriptionRepository>(
      () => TranscriptionRepositoryImpl(
        remoteDataSource: sl<TranscriptionRemoteDataSource>(),
      ),
    );

    // Use Cases
    // Use Cases - using factory registration following project patterns
    sl.registerFactory(
      () => UpdateLiveActivityUseCase(liveActivityService: sl()),
    );
    sl.registerFactory(
      () => StartRecordingUseCase(repository: sl(), liveActivityService: sl()),
    );
    sl.registerFactory(
      () => StopRecordingUseCase(repository: sl(), liveActivityService: sl()),
    );
    sl.registerFactory(
      () => SaveRecordUseCase(
        recordsRepository: sl(),
        uploadWorkerService: sl(),
      ),
    );
    sl.registerFactory(() => DeleteRecordFileUseCase());
    sl.registerFactory(
      () => UploadRecordsUseCase(
        recordsRepository: sl(),
        transcriptionRepository: sl(),
      ),
    );
    sl.registerFactory(
      () => GetTranscriptionsUseCase(transcriptionRepository: sl()),
    );

    // Workers
    sl.registerLazySingleton<UploadWorker>(
      () => UploadWorker(
        uploadRecordsUseCase: sl<UploadRecordsUseCase>(),
        autoStart: true,
      ),
    );
    sl.registerLazySingleton<UploadWorkerService>(
      () => UploadWorkerService(
        uploadWorker: sl<UploadWorker>(),
      ),
    );

    // Presentation Layer - following project patterns
    sl.registerFactory(
      () => RecordingCubit(
        startRecordingUseCase: sl(),
        stopRecordingUseCase: sl(),
        recorderRepository: sl(),
        saveRecordUseCase: sl(),
        deleteRecordFileUseCase: sl(),
        updateLiveActivityUseCase: sl(),
      ),
    );
    sl.registerFactory(() => RecordsListCubit(recordsRepository: sl()));
  }
}
