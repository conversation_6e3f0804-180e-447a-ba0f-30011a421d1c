import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';

const int _kMaxLinesTitle = 2;
const double _kItemHeight = 100;
const EdgeInsets _kPaddingItem = EdgeInsets.symmetric(
  horizontal: 12,
  vertical: 9,
);

class InfoRecordItemWidget extends StatelessWidget {
  const InfoRecordItemWidget({
    super.key,
    required this.createdAt,
    required this.duration,
    required this.title,
    required this.type,
    this.onTap,
  });

  final DateTime createdAt;
  final Duration duration;
  final String title;
  final String type;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: _kItemHeight,
        decoration: BoxDecoration(
          color: ColorConstants.primaryContainer,
          borderRadius: BorderRadius.circular(Dimensions.radiusMd),
        ),
        padding: _kPaddingItem,
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    createdAt.toDateAndTimeDetailedJapaneseFormat(),
                    style: TextStyleConstants.bodySmall.secondaryColor,
                  ),
                  Text(
                    title,
                    style: TextStyleConstants.body.w700.primaryColor.copyWith(
                      overflow: TextOverflow.ellipsis,
                    ),
                    maxLines: _kMaxLinesTitle,
                  ),
                  Row(
                    children: [
                      SvgPicture.asset(
                        PathConstants.clockIcon,
                      ),
                      const SizedBox(width: Dimensions.gapXS),
                      Text(
                        duration.toFormattedString(),
                        style: TextStyleConstants.bodySmall.secondaryColor,
                      ),
                      const SizedBox(width: Dimensions.gapLg),
                      _buildTypeTag(type),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: Dimensions.gapSm),
            SvgPicture.asset(
              PathConstants.starIcon,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeTag(String type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: Dimensions.gapXS),
      decoration: BoxDecoration(
        color: ColorConstants.onPrimaryContainer,
        borderRadius: BorderRadius.circular(Dimensions.radiusSm),
      ),
      child: Center(
        child: Text(
          type,
          style: TextStyleConstants.bodySmall.secondaryColor,
        ),
      ),
    );
  }
}
