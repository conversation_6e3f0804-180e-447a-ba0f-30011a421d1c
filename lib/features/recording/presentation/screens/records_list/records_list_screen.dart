import 'dart:async';
import 'dart:developer';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/core/presentation/router/app_router.gr.dart';
import 'package:glidic_app/core/presentation/router/route_paths.dart';
import 'package:glidic_app/core/presentation/widgets/app_bar/gd_progress_app_bar.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/outline_primary_button.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/core/presentation/widgets/indicators/gd_refresh_indicator.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription.dart';
import 'package:glidic_app/features/recording/domain/entities/upload_status.dart';
import 'package:glidic_app/features/recording/domain/usecases/get_transcriptions_usecase.dart';
import 'package:glidic_app/features/recording/presentation/screens/records_list/records_list_cubit.dart';
import 'package:glidic_app/features/recording/presentation/screens/records_list/widgets/info_record_item_widget.dart';

const _kIconEmptyItemSize = 150.0;
const _kPaddingActionButtons =
    EdgeInsets.symmetric(horizontal: 18, vertical: 24);
const _kPaddingLeftHeader = 21.0;
const _kSizeViewRefreshIndicator = 36.0;

/// Screen for displaying list of recorded audio files
@RoutePage()
class RecordsListScreen extends StatefulWidget implements AutoRouteWrapper {
  const RecordsListScreen({super.key});

  @override
  State<RecordsListScreen> createState() => _RecordsListScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<RecordsListCubit>()..loadRecordings(),
      child: this,
    );
  }
}

class _RecordsListScreenState extends State<RecordsListScreen> {
  AppLocalizations get _l10n => AppLocalizations.of(context);
  Timer? _transcriptionStatusTimer;

  @override
  void initState() {
    super.initState();
    _startTranscriptionStatusChecking();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh recordings when returning to this screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<RecordsListCubit>().refreshRecordings();
      }
    });
  }

  @override
  void dispose() {
    _transcriptionStatusTimer?.cancel();
    super.dispose();
  }

  /// Start periodic transcription status checking
  void _startTranscriptionStatusChecking() {
    _transcriptionStatusTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkTranscriptionStatus(),
    );
  }

  /// Check transcription status for records with transcription IDs
  Future<void> _checkTranscriptionStatus() async {
    try {
      if (!mounted) return;

      final cubit = context.read<RecordsListCubit>();
      final records = cubit.state.recordings;

      // Find records with transcription IDs that need status updates
      final recordsWithTranscriptionIds = records
          .where((record) => record.hasTranscriptionId)
          .where((record) => !record.isUploadCompleted)
          .toList();

      if (recordsWithTranscriptionIds.isEmpty) {
        return;
      }

      log(
        '[RecordsList] Checking transcription status for ${recordsWithTranscriptionIds.length} records',
      );

      // Get transcriptions from API
      final getTranscriptionsUseCase = sl<GetTranscriptionsUseCase>();
      final transcriptions = await getTranscriptionsUseCase.execute(
        userId: 1, // TODO: Get actual user ID from auth
        limit: 100,
      );

      // Update record status based on transcription status
      for (final record in recordsWithTranscriptionIds) {
        try {
          final transcription = transcriptions.firstWhere(
            (t) => t.id == record.transcriptionId,
          );

          await _updateRecordStatusFromTranscription(record, transcription);
        } catch (e) {
          // Transcription not found in API response - this is normal for new uploads
          log(
            '[RecordsList] Transcription ${record.transcriptionId} not found in API response',
          );
        }
      }
    } catch (e) {
      log(
        '[RecordsList] Error checking transcription status: $e',
      );
      // Don't show error to user for background status checks
    }
  }

  /// Update record status based on transcription status
  Future<void> _updateRecordStatusFromTranscription(
    RecordItem record,
    Transcription transcription,
  ) async {
    try {
      if (!mounted) return;

      final cubit = context.read<RecordsListCubit>();
      final transcriptionStatus = transcription.status.toLowerCase();

      log(
        '[RecordsList] Transcription ${transcription.id} status: $transcriptionStatus',
      );

      // Update record status based on transcription status
      if (transcriptionStatus == 'completed') {
        // Update record to finished status
        final updatedRecord = record.copyWith(
          status: UploadStatus.finished,
        );
        await cubit.updateRecord(updatedRecord);

        log(
          '[RecordsList] Updated record ${record.id} to finished status',
        );
      } else if (transcriptionStatus == 'failed') {
        // Update record to failed status
        final updatedRecord = record.copyWith(
          status: UploadStatus.failed,
        );
        await cubit.updateRecord(updatedRecord);

        log(
          '[RecordsList] Updated record ${record.id} to failed status',
        );
      }
      // For 'processing' or 'pending' status, keep current status
    } catch (e) {
      log(
        '[RecordsList] Error updating record ${record.id} status: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RecordsListCubit, RecordsListState>(
      listener: (context, state) {
        if (state.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.error!),
              action: SnackBarAction(
                label: 'Dismiss',
                onPressed: () => context.read<RecordsListCubit>().clearError(),
              ),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: ColorConstants.backgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: Dimensions.gapMd),
              // TODO: change to actual percentage, remaining, total
              const GDProgressAppBar(
                percentage: 50,
                remaining: 100,
                total: 600,
                icEarbudConnected: true,
                icNoteDeviceConnected: false,
              ),
              _buildHeader(),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.defaultPadding,
                  ),
                  child: BlocBuilder<RecordsListCubit, RecordsListState>(
                    builder: (context, state) {
                      final recordings = state.displayRecordings;

                      return CustomScrollView(
                        physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics(),
                        ),
                        slivers: [
                          CupertinoSliverRefreshControl(
                            refreshTriggerPullDistance:
                                _kSizeViewRefreshIndicator,
                            refreshIndicatorExtent: _kSizeViewRefreshIndicator,
                            onRefresh: () async {
                              await context
                                  .read<RecordsListCubit>()
                                  .loadRecordings(isShowLoading: false);
                            },
                            builder: buildRefreshIndicator,
                          ),
                          SliverToBoxAdapter(
                            child: _buildActionButtons(),
                          ),
                          _buildRecordingsList(recordings, state.isLoading),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build header with title according to Figma
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.only(
        left: _kPaddingLeftHeader,
        right: Dimensions.defaultPadding,
      ),
      child: Row(
        children: [
          Text(
            _l10n.allFiles,
            style: TextStyleConstants.title,
          ),
          const Spacer(),
          // TODO: change to actual sort by
          _buildSortButton('作成日時'),
        ],
      ),
    );
  }

  // sort button
  Widget _buildSortButton(String sortBy) {
    return IconButton(
      onPressed: () {},
      icon: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Icon(Icons.arrow_downward_sharp, size: Dimensions.iconSm),
          Text(sortBy),
        ],
      ),
    );
  }

  /// Build action buttons according to Figma design
  Widget _buildActionButtons() {
    return Container(
      padding: _kPaddingActionButtons,
      margin: const EdgeInsets.only(bottom: Dimensions.gapLg),
      decoration: BoxDecoration(
        color: ColorConstants.primaryContainer,
        borderRadius: BorderRadius.circular(Dimensions.gapMd),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: OutlinePrimaryButton(
              backgroundColor: ColorConstants.onPrimary,
              text: _l10n.importAudio,
              onPressed: () {},
            ),
          ),
          const SizedBox(width: Dimensions.gapLg),
          Expanded(
            child: PrimaryButton(
              backgroundColor: ColorConstants.tertiaryColor,
              text: _l10n.startRecording,
              onPressed: () async {
                // Navigate to recording screen and handle result
                final result =
                    await context.router.pushPath(RoutePaths.recording);

                // If a recording was saved, refresh the list
                if (result is Map<String, dynamic> &&
                    result['recordingSaved'] == true &&
                    mounted) {
                  context
                      .read<RecordsListCubit>()
                      .loadRecordings(isShowLoading: false);
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRefreshIndicator(
    BuildContext context,
    RefreshIndicatorMode refreshState,
    double pulledExtent,
    double refreshTriggerPullDistance,
    double refreshIndicatorExtent,
  ) {
    final double percentageComplete = clampDouble(
      pulledExtent / refreshTriggerPullDistance,
      0.0,
      1.0,
    );

    return Center(
      child: _buildIndicatorForRefreshState(
        refreshState,
        Dimensions.radiusXL,
        percentageComplete,
      ),
    );
  }

  Widget _buildIndicatorForRefreshState(
    RefreshIndicatorMode refreshState,
    double radius,
    double percentageComplete,
  ) {
    switch (refreshState) {
      case RefreshIndicatorMode.drag:
        const Curve opacityCurve = Interval(0.0, 0.35, curve: Curves.easeInOut);
        return Opacity(
          opacity: opacityCurve.transform(percentageComplete),
          child: GDRefreshIndicator(
            text: _l10n.refreshing,
            percentSize: percentageComplete,
            isAnimating: false,
          ),
        );
      case RefreshIndicatorMode.armed:
      case RefreshIndicatorMode.refresh:
        return GDRefreshIndicator(
          text: _l10n.refreshing,
          percentSize: 1,
        );
      case RefreshIndicatorMode.done:
        return GDRefreshIndicator(
          text: _l10n.refreshing,
          percentSize: percentageComplete,
        );
      case RefreshIndicatorMode.inactive:
        return const SizedBox.shrink();
    }
  }

  Widget _buildRecordingsList(List<RecordItem> recordings, bool isLoading) {
    if (isLoading) {
      return const SliverFillRemaining(
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (recordings.isEmpty) {
      return const SliverFillRemaining(
        child: Icon(
          Icons.folder_off_outlined,
          size: _kIconEmptyItemSize,
        ),
      );
    }

    return SliverList.separated(
      itemCount: recordings.length,
      itemBuilder: (context, index) {
        return InfoRecordItemWidget(
          createdAt: recordings[index].createdAt,
          duration: recordings[index].duration,
          title: recordings[index].title,
          // TODO: change to actual type
          type: 'レコーダー',
          onTap: () => _onRecordItemTap(recordings[index]),
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(height: Dimensions.gapLg);
      },
    );
  }

  void _onRecordItemTap(RecordItem record) {
    if (record.transcriptionId == null ||
        record.status != UploadStatus.finished) {
      // Show message record is processing
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Record is processing'),
        ),
      );
      return;
    }
    context.router.push(
      AudioAnalysisRoute(
        audioId: record.transcriptionId!.toString(),
      ),
    );
  }
}
