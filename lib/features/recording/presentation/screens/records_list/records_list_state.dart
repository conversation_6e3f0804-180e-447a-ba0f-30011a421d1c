part of 'records_list_cubit.dart';

/// State for the Records List screen
class RecordsListState extends Equatable {
  const RecordsListState({
    this.recordings = const [],
    this.filteredRecordings = const [],
    this.isLoading = false,
    this.isSearching = false,
    this.searchQuery = '',
    this.selectedRecordings = const {},
    this.isSelectionMode = false,
    this.currentlyPlayingId,
    this.error,
    this.sortBy = RecordingSortBy.dateNewest,
  });

  /// All recordings
  final List<RecordItem> recordings;

  /// Filtered recordings based on search
  final List<RecordItem> filteredRecordings;

  /// Whether data is being loaded
  final bool isLoading;

  /// Whether search is in progress
  final bool isSearching;

  /// Current search query
  final String searchQuery;

  /// Selected recordings for batch operations
  final Set<String> selectedRecordings;

  /// Whether in selection mode
  final bool isSelectionMode;

  /// ID of currently playing recording
  final String? currentlyPlayingId;

  /// Error message if any
  final String? error;

  /// Current sort criteria
  final RecordingSortBy sortBy;

  /// Get the list to display (filtered or all)
  List<RecordItem> get displayRecordings {
    return searchQuery.isNotEmpty ? filteredRecordings : recordings;
  }

  /// Whether there are any recordings
  bool get hasRecordings => recordings.isNotEmpty;

  /// Whether there are any filtered results
  bool get hasFilteredResults => filteredRecordings.isNotEmpty;

  /// Whether to show empty state
  bool get showEmptyState => !isLoading && !hasRecordings;

  /// Whether to show no search results
  bool get showNoSearchResults =>
      !isLoading && searchQuery.isNotEmpty && !hasFilteredResults;

  /// Whether any recordings are selected
  bool get hasSelectedRecordings => selectedRecordings.isNotEmpty;

  /// Number of selected recordings
  int get selectedCount => selectedRecordings.length;

  /// Copy with updated fields
  RecordsListState copyWith({
    List<RecordItem>? recordings,
    List<RecordItem>? filteredRecordings,
    bool? isLoading,
    bool? isSearching,
    String? searchQuery,
    Set<String>? selectedRecordings,
    bool? isSelectionMode,
    String? currentlyPlayingId,
    String? error,
    RecordingSortBy? sortBy,
  }) {
    return RecordsListState(
      recordings: recordings ?? this.recordings,
      filteredRecordings: filteredRecordings ?? this.filteredRecordings,
      isLoading: isLoading ?? this.isLoading,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedRecordings: selectedRecordings ?? this.selectedRecordings,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
      currentlyPlayingId: currentlyPlayingId ?? this.currentlyPlayingId,
      error: error ?? this.error,
      sortBy: sortBy ?? this.sortBy,
    );
  }

  /// Clear error
  RecordsListState clearError() {
    return copyWith(error: null);
  }

  /// Clear search
  RecordsListState clearSearch() {
    return copyWith(
      searchQuery: '',
      filteredRecordings: [],
      isSearching: false,
    );
  }

  /// Clear selection
  RecordsListState clearSelection() {
    return copyWith(
      selectedRecordings: const {},
      isSelectionMode: false,
    );
  }

  @override
  List<Object?> get props => [
        recordings,
        filteredRecordings,
        isLoading,
        isSearching,
        searchQuery,
        selectedRecordings,
        isSelectionMode,
        currentlyPlayingId,
        error,
        sortBy,
      ];
}

/// Sorting options for recordings
enum RecordingSortBy {
  dateNewest,
  dateOldest,
  titleAZ,
  titleZA,
  durationLongest,
  durationShortest,
  sizeLargest,
  sizeSmallest,
}

extension RecordingSortByExtension on RecordingSortBy {
  String get displayName {
    switch (this) {
      case RecordingSortBy.dateNewest:
        return 'Date (Newest)';
      case RecordingSortBy.dateOldest:
        return 'Date (Oldest)';
      case RecordingSortBy.titleAZ:
        return 'Title (A-Z)';
      case RecordingSortBy.titleZA:
        return 'Title (Z-A)';
      case RecordingSortBy.durationLongest:
        return 'Duration (Longest)';
      case RecordingSortBy.durationShortest:
        return 'Duration (Shortest)';
      case RecordingSortBy.sizeLargest:
        return 'Size (Largest)';
      case RecordingSortBy.sizeSmallest:
        return 'Size (Smallest)';
    }
  }

  IconData get icon {
    switch (this) {
      case RecordingSortBy.dateNewest:
      case RecordingSortBy.dateOldest:
        return Icons.calendar_today;
      case RecordingSortBy.titleAZ:
      case RecordingSortBy.titleZA:
        return Icons.sort_by_alpha;
      case RecordingSortBy.durationLongest:
      case RecordingSortBy.durationShortest:
        return Icons.timer;
      case RecordingSortBy.sizeLargest:
      case RecordingSortBy.sizeSmallest:
        return Icons.storage;
    }
  }
}
