import 'dart:async';
import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/repositories/records_repository.dart';

part 'records_list_state.dart';

/// Cubit for managing Records List screen state
class RecordsListCubit extends Cubit<RecordsListState> {
  RecordsListCubit({
    required RecordsRepository recordsRepository,
  })  : _recordsRepository = recordsRepository,
        super(const RecordsListState());

  final RecordsRepository _recordsRepository;

  Timer? _searchDebounceTimer;

  /// Load all recordings
  Future<void> loadRecordings({bool isShowLoading = true}) async {
    try {
      emit(state.copyWith(isLoading: isShowLoading, error: null));
      final recordings = await _recordsRepository.getAllRecords();
      final sortedRecordings = _sortRecordings(recordings, state.sortBy);

      emit(
        state.copyWith(
          recordings: sortedRecordings,
          isLoading: false,
        ),
      );
    } catch (e) {
      log('[RecordsListCubit] ❌ Error loading recordings: $e');
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load recordings: ${e.toString()}',
        ),
      );
    }
  }

  /// Refresh recordings
  Future<void> refreshRecordings() async {
    await loadRecordings();
  }

  /// Search recordings with debouncing
  void searchRecordings(String query) {
    _searchDebounceTimer?.cancel();

    if (query.trim().isEmpty) {
      emit(state.clearSearch());
      return;
    }

    emit(
      state.copyWith(
        searchQuery: query,
        isSearching: true,
      ),
    );
  }

  /// Sort recordings by criteria
  void sortRecordings(RecordingSortBy sortBy) {
    final sortedRecordings = _sortRecordings(state.recordings, sortBy);
    final sortedFilteredRecordings = state.searchQuery.isNotEmpty
        ? _sortRecordings(state.filteredRecordings, sortBy)
        : <RecordItem>[];

    emit(
      state.copyWith(
        recordings: sortedRecordings,
        filteredRecordings: sortedFilteredRecordings,
        sortBy: sortBy,
      ),
    );
  }

  /// Helper method to sort recordings
  List<RecordItem> _sortRecordings(
    List<RecordItem> recordings,
    RecordingSortBy sortBy,
  ) {
    final sortedList = List<RecordItem>.from(recordings);

    switch (sortBy) {
      case RecordingSortBy.dateNewest:
        sortedList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case RecordingSortBy.dateOldest:
        sortedList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case RecordingSortBy.titleAZ:
        sortedList.sort((a, b) => a.title.compareTo(b.title));
        break;
      case RecordingSortBy.titleZA:
        sortedList.sort((a, b) => b.title.compareTo(a.title));
        break;
      case RecordingSortBy.durationLongest:
        sortedList.sort((a, b) => b.duration.compareTo(a.duration));
        break;
      case RecordingSortBy.durationShortest:
        sortedList.sort((a, b) => a.duration.compareTo(b.duration));
        break;
      case RecordingSortBy.sizeLargest:
        sortedList.sort((a, b) => b.fileSize.compareTo(a.fileSize));
        break;
      case RecordingSortBy.sizeSmallest:
        sortedList.sort((a, b) => a.fileSize.compareTo(b.fileSize));
        break;
    }

    return sortedList;
  }

  /// Toggle selection mode
  void toggleSelectionMode() {
    if (state.isSelectionMode) {
      emit(state.clearSelection());
    } else {
      emit(state.copyWith(isSelectionMode: true));
    }
  }

  /// Toggle recording selection
  void toggleRecordingSelection(String recordingId) {
    final selectedRecordings = Set<String>.from(state.selectedRecordings);

    if (selectedRecordings.contains(recordingId)) {
      selectedRecordings.remove(recordingId);
    } else {
      selectedRecordings.add(recordingId);
    }

    emit(
      state.copyWith(
        selectedRecordings: selectedRecordings,
        isSelectionMode: selectedRecordings.isNotEmpty,
      ),
    );
  }

  /// Select all recordings
  void selectAllRecordings() {
    final allIds = state.displayRecordings.map((r) => r.id).toSet();
    emit(
      state.copyWith(
        selectedRecordings: allIds,
        isSelectionMode: true,
      ),
    );
  }

  /// Clear all selections
  void clearSelection() {
    emit(state.clearSelection());
  }

  /// Set currently playing recording
  void setCurrentlyPlaying(String? recordingId) {
    emit(state.copyWith(currentlyPlayingId: recordingId));
  }

  /// Update a single recording
  Future<void> updateRecord(RecordItem record) async {
    try {
      await _recordsRepository.updateRecord(record);

      // Update the record in the current state
      final updatedRecordings = state.recordings.map((r) {
        return r.id == record.id ? record : r;
      }).toList();

      final updatedFilteredRecordings = state.filteredRecordings.map((r) {
        return r.id == record.id ? record : r;
      }).toList();

      emit(
        state.copyWith(
          recordings: updatedRecordings,
          filteredRecordings: updatedFilteredRecordings,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          error: 'Failed to update recording: ${e.toString()}',
        ),
      );
    }
  }

  /// Clear error
  void clearError() {
    emit(state.clearError());
  }

  @override
  Future<void> close() {
    _searchDebounceTimer?.cancel();
    return super.close();
  }
}
