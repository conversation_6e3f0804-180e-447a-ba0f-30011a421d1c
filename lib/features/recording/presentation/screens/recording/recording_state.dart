part of 'recording_cubit.dart';

/// Recording screen state definitions following sealed class pattern
sealed class RecordingState extends Equatable {
  const RecordingState();

  String get formattedDuration;

  String _formattedDuration(int durationMs) {
    final hhmmssPart = Duration(milliseconds: durationMs).toFormattedString(
      format: DurationFormatConfig.digital,
      isShowFull: true,
    );
    final centisecondPart =
        ((durationMs ~/ 10) % 100).toString().padLeft(2, '0');
    return '$hhmmssPart.$centisecondPart';
  }

  @override
  List<Object?> get props => [];
}

/// Initial state when screen is first loaded
class RecordingInitial extends RecordingState {
  const RecordingInitial();

  @override
  String get formattedDuration => _formattedDuration(0);
}

/// Loading state during recording operations
class RecordingLoading extends RecordingState {
  const RecordingLoading();

  @override
  String get formattedDuration => _formattedDuration(0);
}

/// State when recording is active
class RecordingActive extends RecordingState {
  const RecordingActive({
    required this.session,
    required this.duration,
    required this.amplitude,
  });

  final RecorderSessionEntity session;
  final int duration; // Duration in milliseconds
  final double amplitude; // Amplitude value (0.0 to 1.0)

  @override
  String get formattedDuration => _formattedDuration(duration);

  @override
  List<Object?> get props => [session, duration, amplitude];

  /// Check if recording is active
  bool get isRecording => session.status == RecordingStatus.recording;
}

/// State when recording is paused
class RecordingPaused extends RecordingState {
  const RecordingPaused({
    required this.session,
    required this.duration,
    required this.amplitude,
  });

  final RecorderSessionEntity session;
  final int duration;
  final double amplitude;

  @override
  String get formattedDuration => _formattedDuration(duration);

  @override
  List<Object?> get props => [session, duration, amplitude];
}

/// State when recording is stopped but not yet saved to database
class RecordingStopped extends RecordingState {
  const RecordingStopped({required this.session});

  final RecorderSessionEntity session;

  @override
  List<Object?> get props => [session];

  @override
  String get formattedDuration => _formattedDuration(session.durationMs);

  /// Get formatted file size
  String get formattedFileSize {
    if (session.fileSizeBytes == null) return 'Unknown size';

    final bytes = session.fileSizeBytes!;
    return bytes.readableFileSize;
  }
}

/// State when recording is completed and saved to database
class RecordingCompleted extends RecordingState {
  const RecordingCompleted({required this.session});

  final RecorderSessionEntity session;

  @override
  List<Object?> get props => [session];

  /// Get formatted duration string
  @override
  String get formattedDuration => _formattedDuration(session.durationMs);

  /// Get formatted file size
  String get formattedFileSize {
    if (session.fileSizeBytes == null) return 'Unknown size';

    final bytes = session.fileSizeBytes!;
    return bytes.readableFileSize;
  }
}

/// Error state when recording operations fail
class RecordingError extends RecordingState {
  const RecordingError({
    required this.failure,
    this.canRetry = true,
    this.retryAction,
  });

  final AppFailure failure;
  final bool canRetry;
  final VoidCallback? retryAction;

  @override
  String get formattedDuration => _formattedDuration(0);

  @override
  List<Object?> get props => [failure, canRetry, retryAction];

  /// Get user-friendly error message
  String get errorMessage => failure.message;

  /// Get error code for debugging
  String? get errorCode => failure.code;
}
