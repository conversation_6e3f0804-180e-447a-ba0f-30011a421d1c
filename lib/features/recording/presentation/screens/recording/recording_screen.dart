import 'dart:developer';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/features/recording/domain/failures/recording_failures.dart';
import 'package:glidic_app/features/recording/presentation/screens/recording/recording_cubit.dart';
import 'package:glidic_app/features/recording/presentation/screens/recording/widgets/middle_line_waveform_painter.dart';
import 'package:glidic_app/features/recording/presentation/screens/recording/widgets/recording_audio_waveforms.dart';
import 'package:permission_handler/permission_handler.dart';

const _kRecordingScreenBackgroundColor = Color(0xFFF9FBFD);
const _kLargeDurationTextSize = 40.0;
const _kSmallDurationTextSize = 20.0;
const _kSpacingToRecordWaveform = 90.0;
const _kSpacingDurationWithRecordButton = 40.0;
const _kSpacingBetweenRecordButtonAndBottom = 44.0;
const _kRecordingButtonSize = 74.0;
const _kBorderWidthRecordingButton = 3.0;
const _kBorderColorRecordingButton = Color(0xFFB1B1B1);
const _kInnerCircleRecordingButtonSize = 35.0;
const _kInnerRectangleRecordingButtonSize = 50.0;

/// Recording screen for audio recording functionality
///
/// This screen provides a complete recording interface with:
/// - Start/stop/pause/resume recording controls
/// - Real-time waveform visualization
/// - Recording duration display
/// - Save/delete recording options
/// - Live Activity integration for iOS
/// - Comprehensive error handling with user-friendly messages
///
/// The screen follows clean architecture patterns and uses
/// internationalization for all user-facing text.
@RoutePage()
class RecordingScreen extends StatefulWidget implements AutoRouteWrapper {
  const RecordingScreen({super.key});

  @override
  State<RecordingScreen> createState() => _RecordingScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<RecordingCubit>(),
      child: this,
    );
  }
}

class _RecordingScreenState extends State<RecordingScreen> {
  late final RecorderController recorderController;
  final DateTime startTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    final cubit = context.read<RecordingCubit>();
    recorderController = RecorderController()
      ..androidEncoder = AndroidEncoder.aac
      ..androidOutputFormat = AndroidOutputFormat.mpeg4
      ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
      ..sampleRate = Constants.defaultSampleRate
      ..bitRate = Constants.defaultAudioBitrate;
    Future.microtask(() {
      cubit.setRecorderController(recorderController);
    });
  }

  @override
  void dispose() {
    recorderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: _kRecordingScreenBackgroundColor,
      body: BlocListener<RecordingCubit, RecordingState>(
        listener: (context, state) {
          if (state is RecordingError) {
            _handleRecordingError(context, state);
          }
        },
        child: SafeArea(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  left: Dimensions.defaultPadding,
                  right: Dimensions.defaultPadding,
                  bottom: _kSpacingToRecordWaveform,
                ),
                child: _buildAppBar(context),
              ),
              Expanded(
                child: Container(
                  color: ColorConstants.backgroundColor,
                  child: BlocBuilder<RecordingCubit, RecordingState>(
                    builder: (context, state) {
                      return Stack(
                        children: [
                          if (state is RecordingActive ||
                              state is RecordingPaused)
                            LayoutBuilder(
                              builder: (context, constraints) {
                                final screenWidth = constraints.maxWidth;
                                return RecordingAudioWaveforms(
                                  size: Size(
                                    screenWidth,
                                    constraints.maxHeight,
                                  ),
                                  recorderController: recorderController,
                                );
                              },
                            ),
                          _buildWaveformPlaceholder(),
                        ],
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: _kSpacingToRecordWaveform),
              BlocBuilder<RecordingCubit, RecordingState>(
                buildWhen: (previous, current) {
                  return current.formattedDuration !=
                      previous.formattedDuration;
                },
                builder: (context, state) {
                  return RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: state.formattedDuration.split('.').first,
                          style: TextStyleConstants.title.copyWith(
                            fontSize: _kLargeDurationTextSize,
                          ),
                        ),
                        TextSpan(
                          text: '.${state.formattedDuration.split('.').last}',
                          style: TextStyleConstants.title.copyWith(
                            fontSize: _kSmallDurationTextSize,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              const SizedBox(
                height: _kSpacingDurationWithRecordButton,
              ),
              BlocBuilder<RecordingCubit, RecordingState>(
                builder: (context, state) {
                  final showSaveDeleteButtons = state is RecordingStopped;
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      showSaveDeleteButtons
                          ? Expanded(
                              child: Center(
                                child: TextButton(
                                  onPressed: () =>
                                      _deleteRecordingFile(context),
                                  child: Text(
                                    l10n.deleteAction,
                                    style: TextStyleConstants.body.w700,
                                  ),
                                ),
                              ),
                            )
                          : const Spacer(),
                      _buildRecordingButton(context, state),
                      showSaveDeleteButtons
                          ? Expanded(
                              child: Center(
                                child: TextButton(
                                  onPressed: () =>
                                      _saveRecordingToDatabase(context),
                                  child: Text(
                                    l10n.saveAction,
                                    style: TextStyleConstants.body.w700,
                                  ),
                                ),
                              ),
                            )
                          : const Spacer(),
                    ],
                  );
                },
              ),
              const SizedBox(
                height: _kSpacingBetweenRecordButtonAndBottom,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build recording button with proper state changes according to Figma
  Widget _buildRecordingButton(BuildContext context, RecordingState state) {
    final isRecording = state is RecordingActive || state is RecordingPaused;
    final isRecordingStopped = state is RecordingStopped;

    return GestureDetector(
      onTap: isRecordingStopped
          ? null
          : () {
              if (state is RecordingInitial || state is RecordingCompleted) {
                _startRecording(context);
              } else if (state is RecordingActive) {
                _stopRecording(context);
              } else {}
            },
      child: Container(
        width: _kRecordingButtonSize,
        height: _kRecordingButtonSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: _kBorderColorRecordingButton,
            width: _kBorderWidthRecordingButton,
          ),
          color: Colors.transparent,
        ),
        child: Center(
          child: Container(
            // Figma specifications: 50x50 circle when not recording, 35x35 square when recording
            width: isRecording
                ? _kInnerCircleRecordingButtonSize
                : _kInnerRectangleRecordingButtonSize,
            height: isRecording
                ? _kInnerCircleRecordingButtonSize
                : _kInnerRectangleRecordingButtonSize,
            decoration: BoxDecoration(
              color: const Color(0xFFFF0000),
              borderRadius: BorderRadius.circular(
                isRecording ? Dimensions.radiusMd : Dimensions.circleRadius,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Start recording and waveform visualization
  Future<void> _startRecording(BuildContext context) async {
    try {
      // Make sure the recorder controller is set in the cubit
      final cubit = context.read<RecordingCubit>();
      cubit.setRecorderController(recorderController);

      // Start recording in the cubit
      final defaultTitle = startTime.toDateAndTimeDetailedJapaneseFormat(
        withSeconds: true,
      );
      cubit.startRecording(defaultTitle);
    } catch (e) {
      log('Failed to start recording: $e', name: 'RecordingScreen');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to start recording: $e')),
      );
    }
  }

  /// Stop recording (without saving to database)
  Future<void> _stopRecording(BuildContext context) async {
    final cubit = context.read<RecordingCubit>();
    try {
      await recorderController.stop();
      cubit.stopRecording();
    } catch (e) {
      log('Error stopping recorder controller: $e', name: 'RecordingScreen');
      cubit.stopRecording();
    }
  }

  /// Handle recording errors with optimized UX and internationalization
  void _handleRecordingError(BuildContext context, RecordingError state) {
    final l10n = AppLocalizations.of(context);

    // Handle specific failure types with appropriate user messages
    final failure = state.failure;

    if (failure is MicrophonePermissionFailure) {
      _showPermissionSnackBar(context, l10n);
    } else if (failure is RecordingHardwareFailure) {
      _showErrorSnackBar(context, l10n.errorRecordingHardware);
    } else if (failure is InsufficientStorageFailure) {
      _showErrorSnackBar(context, l10n.errorRecordingStorageSpace);
    } else if (failure is RecordingFileFailure) {
      _showErrorSnackBar(context, l10n.errorRecordingStartFailed);
    } else {
      // For other recording errors, show generic error with specific message
      _showErrorSnackBar(context, l10n.errorRecordingDeviceCheck);
    }
  }

  /// Show permission error with gentle UX using internationalized messages
  void _showPermissionSnackBar(BuildContext context, AppLocalizations l10n) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(l10n.errorRecordingPermissionRequired),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: l10n.permissionOpenSettings,
          textColor: Colors.white,
          onPressed: () => _openAppSettings(),
        ),
      ),
    );
  }

  /// Show generic error message via SnackBar with internationalization
  void _showErrorSnackBar(BuildContext context, String message) {
    final l10n = AppLocalizations.of(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: l10n.retry,
          textColor: Colors.white,
          onPressed: () {
            // Simple retry - user can tap record button again
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Open device app settings for permission management
  Future<void> _openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      log(
        'Failed to open app settings: $e',
        name: 'RecordingScreen',
      );
    }
  }

  /// Save recording to database with internationalized error handling
  Future<void> _saveRecordingToDatabase(BuildContext context) async {
    final l10n = AppLocalizations.of(context);

    // Double-check the state before proceeding
    final currentState = context.read<RecordingCubit>().state;

    if (currentState is! RecordingStopped) {
      _showErrorSnackBar(context, 'Recording must be stopped before saving.');
      return;
    }

    try {
      await context.read<RecordingCubit>().saveRecordingToDatabase();
      // Back to records list after successful save with refresh signal
      if (context.mounted) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (context.mounted) {
            // Pop with result to indicate successful save
            context.router.pop({'recordingSaved': true});
          }
        });
      }
    } catch (e) {
      // Error handling is done in the cubit, but show user-friendly message
      if (context.mounted) {
        _showErrorSnackBar(
          context,
          l10n.errorRecordingSaveFailed(e.toString()),
        );
      }
    }
  }

  /// Delete recording file with internationalized confirmation and error handling
  Future<void> _deleteRecordingFile(BuildContext context) async {
    final l10n = AppLocalizations.of(context);

    // Show confirmation dialog with localized text
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.recordingConfirmDelete),
        content: Text(l10n.recordingConfirmDeleteMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(l10n.recordingCancel),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(l10n.recordingDelete),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      try {
        await context.read<RecordingCubit>().deleteRecordingFile();
      } catch (e) {
        // Error handling is done in the cubit, but show user-friendly message
        if (context.mounted) {
          _showErrorSnackBar(
            context,
            l10n.errorRecordingDeleteFailed(e.toString()),
          );
        }
      }
    }
  }

  /// Build waveform placeholder when not recording
  Widget _buildWaveformPlaceholder() {
    return CustomPaint(
      painter: MiddleLineWaveformPainter(),
      size: const Size(double.infinity, double.infinity),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () => context.router.pop(),
          child: SvgPicture.asset(
            PathConstants.backIcon,
          ),
        ),
        Text(
          startTime.toDateAndTimeDetailedJapaneseFormat(
            withSeconds: true,
          ),
          style: TextStyleConstants.body.w700,
        ),
        Opacity(
          opacity: 0,
          child: SvgPicture.asset(PathConstants.backIcon),
        ),
      ],
    );
  }
}
