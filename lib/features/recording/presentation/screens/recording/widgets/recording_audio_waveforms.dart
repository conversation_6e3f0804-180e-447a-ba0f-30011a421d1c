import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

const _kWaveformSpacing = 1.8;
const _kWaveCap = StrokeCap.round;
const _kWaveThickness = 2.5;

// CustomAudioWaveforms modified from audio_waveforms.dart
class RecordingAudioWaveforms extends StatefulWidget {
  final Size size;
  final RecorderController recorderController;
  final WaveStyle waveStyle;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BoxDecoration? decoration;
  final Color? backgroundColor;

  const RecordingAudioWaveforms({
    super.key,
    required this.size,
    required this.recorderController,
    this.waveStyle = const WaveStyle(),
    this.padding,
    this.margin,
    this.decoration,
    this.backgroundColor,
  });

  @override
  State<RecordingAudioWaveforms> createState() =>
      _RecordingAudioWaveformsState();
}

class _RecordingAudioWaveformsState extends State<RecordingAudioWaveforms> {
  Offset _totalBackDistance = Offset.zero;

  final double _initialPosition = 0.0;

  @override
  void initState() {
    super.initState();
    widget.recorderController.addListener(_recorderControllerListener);
  }

  @override
  void dispose() {
    widget.recorderController.removeListener(_recorderControllerListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate the shift to keep the latest wave at the center
    _updateTotalBackDistance();
 
    return Container(
      padding: widget.padding,
      margin: widget.margin,
      color: widget.backgroundColor,
      decoration: widget.decoration,
      child: RepaintBoundary(
        child: CustomPaint(
          size: widget.size,
          painter: RecorderWavePainter(
            waveThickness: _kWaveThickness,
            waveData: widget.recorderController.waveData,
            // make waveform draw in center of the screen
            bottomPadding: widget.size.height / 2,
            spacing: _kWaveformSpacing + _kWaveThickness,
            waveCap: _kWaveCap,
            waveColor: ColorConstants.recordWaveForm,
            totalBackDistance: _totalBackDistance,
            initialPosition: _initialPosition,
            scaleFactor: widget.size.height / 3,
          ),
        ),
      ),
    );
  }

  void _recorderControllerListener() {
    if (mounted) {
      setState(() {});
    }
  }

  void _updateTotalBackDistance() {
    if (widget.recorderController.waveData.isNotEmpty) {
      final length = widget.recorderController.waveData.length;
      final latestPosition =
          (_kWaveformSpacing + _kWaveThickness) * (length - 1);
      _totalBackDistance = Offset(latestPosition - (widget.size.width / 2), 0);
    } else {
      _totalBackDistance = Offset.zero;
    }
  }
}

// RecorderWavePainter from recorder_wave_painter.dart, modified to remove pushback
class RecorderWavePainter extends CustomPainter {
  final List<double> waveData;
  final Color waveColor;
  final double spacing;
  final double initialPosition;
  final double bottomPadding;
  final StrokeCap waveCap;
  final Offset totalBackDistance;
  final double waveThickness;
  final Paint _wavePaint;
  final Shader? gradient;
  final double scaleFactor;

  RecorderWavePainter({
    required this.waveData,
    required this.waveColor,
    required this.spacing,
    required this.initialPosition,
    required this.bottomPadding,
    required this.waveCap,
    required this.totalBackDistance,
    required this.waveThickness,
    this.gradient,
    required this.scaleFactor,
  }) : _wavePaint = Paint()
          ..color = waveColor
          ..strokeWidth = waveThickness
          ..strokeCap = waveCap
          ..shader = gradient ?? Paint().shader;

  @override
  void paint(Canvas canvas, Size size) {
    for (var i = 0; i < waveData.length; i++) {
      if (gradient != null) _waveGradient();

      _drawWave(canvas, size, i);
    }
  }

  @override
  bool shouldRepaint(RecorderWavePainter oldDelegate) => true;

  void _drawWave(Canvas canvas, Size size, int i) {
    final halfWidth = size.width * 0.5;
    final height = size.height;
    final dx = -totalBackDistance.dx + (spacing * i) - initialPosition;
    final scaledWaveHeight = waveData[i] * scaleFactor;
    final upperDy = height - scaledWaveHeight - bottomPadding;
    final lowerDy = height + scaledWaveHeight - bottomPadding;

    if (dx > -halfWidth && dx < halfWidth * 2) {
      canvas.drawLine(Offset(dx, upperDy), Offset(dx, lowerDy), _wavePaint);
    }
  }

  void _waveGradient() {
    _wavePaint.shader = gradient;
  }
}
