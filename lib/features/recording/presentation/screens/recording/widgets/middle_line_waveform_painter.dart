import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

const _circleRadius = 4.0;
const _strokeWidth = 1.5;

/// Middle line waveform painter
class MiddleLineWaveformPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = ColorConstants.tertiaryColor
      ..strokeWidth = _strokeWidth
      ..strokeCap = StrokeCap.round;

    final centerX = size.width / 2;

    // Draw the middle line
    canvas.drawLine(
      Offset(centerX, 0),
      Offset(centerX, size.height),
      paint,
    );

    // Draw the top circle
    canvas.drawCircle(
      Offset(centerX, -_circleRadius / 2),
      _circleRadius,
      paint,
    );

    // Draw the bottom circle
    canvas.drawCircle(
      Offset(centerX, size.height + _circleRadius / 2),
      _circleRadius,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
