import 'dart:async';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';
import 'package:glidic_app/features/recording/domain/entities/recorder_session_entity.dart';
import 'package:glidic_app/features/recording/domain/repositories/recorder_repository.dart';
import 'package:glidic_app/features/recording/domain/usecases/delete_record_file_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/save_record_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/start_recording_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/stop_recording_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/update_live_activity_usecase.dart';

part 'recording_state.dart';

class RecordingCubit extends Cubit<RecordingState> {
  final StartRecordingUseCase _startRecordingUseCase;
  final StopRecordingUseCase _stopRecordingUseCase;
  final RecorderRepository _recorderRepository;
  final SaveRecordUseCase _saveRecordUseCase;
  final DeleteRecordFileUseCase _deleteRecordingFileUseCase;
  final UpdateLiveActivityUseCase _updateLiveActivityUseCase;

  // Reference to the recorder controller for accessing real waveform data
  dynamic _recorderController;

  // Recording updates subscription
  StreamSubscription? _recordingUpdatesSubscription;

  // Amplitude buffer for waveform visualization
  final List<double> _amplitudeBuffer = [];

  /// Set the recorder controller instance to access real waveform data
  void setRecorderController(dynamic recorderController) {
    _recorderController = recorderController;
    // Also set it in the repository for recording operations
    if (recorderController is RecorderController) {
      _recorderRepository.setRecorderController(recorderController);
    }
  }

  RecordingCubit({
    required StartRecordingUseCase startRecordingUseCase,
    required StopRecordingUseCase stopRecordingUseCase,
    required RecorderRepository recorderRepository,
    required SaveRecordUseCase saveRecordUseCase,
    required DeleteRecordFileUseCase deleteRecordFileUseCase,
    required UpdateLiveActivityUseCase updateLiveActivityUseCase,
  })  : _startRecordingUseCase = startRecordingUseCase,
        _stopRecordingUseCase = stopRecordingUseCase,
        _recorderRepository = recorderRepository,
        _saveRecordUseCase = saveRecordUseCase,
        _deleteRecordingFileUseCase = deleteRecordFileUseCase,
        _updateLiveActivityUseCase = updateLiveActivityUseCase,
        super(const RecordingInitial());

  // Permission request logic moved to Records List screen
  // This ensures users grant permission before entering the recording flow

  /// Start recording with the given title
  Future<void> startRecording(String title) async {
    if (state is RecordingActive || state is RecordingLoading) return;

    try {
      emit(const RecordingLoading());

      // Use the enhanced StartRecordingUseCase that handles permissions and Live Activity
      final session = await _startRecordingUseCase.execute(
        StartRecordingParams(
          title: title,
          recorderController: _recorderController,
        ),
      );

      // Clear amplitude buffer
      _amplitudeBuffer.clear();

      // Start recording updates using the unified execute method
      _recordingUpdatesSubscription = await _updateLiveActivityUseCase.execute(
        StartRecordingUpdatesParams(
          title: title,
          recorderController: _recorderController,
          onUpdate: (durationMs, amplitude) {
            final currentState = state;
            if (currentState is RecordingActive) {
              emit(
                RecordingActive(
                  session: currentState.session,
                  duration: durationMs,
                  amplitude: amplitude,
                ),
              );
            }
          },
        ),
      );

      emit(
        RecordingActive(
          session: session,
          duration: 0,
          amplitude: 0.0,
        ),
      );
    } catch (e) {
      if (e is AppFailure) {
        emit(RecordingError(failure: e));
      } else {
        // Handle unexpected errors using standardized approach
        final failure = ExceptionHandler.handle(e);
        emit(RecordingError(failure: failure));
      }
    }
  }

  /// Stop the current recording (without saving to database)
  Future<void> stopRecording() async {
    if (state is! RecordingActive && state is! RecordingPaused) return;

    final currentSession = _getCurrentSession();
    if (currentSession == null) return;

    try {
      emit(const RecordingLoading());

      // Stop recording updates subscription
      _recordingUpdatesSubscription?.cancel();
      _recordingUpdatesSubscription = null;

      // Use the enhanced StopRecordingUseCase that handles Live Activity termination
      final session = await _stopRecordingUseCase.execute(
        StopRecordingParams(sessionId: currentSession.id),
      );

      // Emit stopped state (not completed - user needs to choose save/delete)
      emit(RecordingStopped(session: session));
    } catch (e) {
      emit(
        RecordingError(
          failure: UnknownFailure(
            'Failed to stop recording: ${e.toString()}',
            code: 'STOP_RECORDING_ERROR',
            originalException: e,
          ),
        ),
      );
    }
  }

  /// Save recording to database
  Future<void> saveRecordingToDatabase() async {
    final currentState = state;

    if (currentState is! RecordingStopped) {
      emit(
        RecordingError(
          failure: AppFailureConstructors.recordingInvalidState(
            'Cannot save recording. Recording must be stopped first.',
          ),
        ),
      );
      return;
    }

    try {
      emit(const RecordingLoading());

      // Use the enhanced SaveRecordUseCase that handles Live Activity and upload triggering
      await _saveRecordUseCase.execute(
        SaveRecordParams(
          session: currentState.session,
          waveformData: _amplitudeBuffer,
        ),
      );

      emit(RecordingCompleted(session: currentState.session));
    } catch (e) {
      final failure = ExceptionHandler.handle(e);
      emit(RecordingError(failure: failure));
    }
  }

  /// Delete recording file
  Future<void> deleteRecordingFile() async {
    final currentState = state;
    if (currentState is! RecordingStopped) return;

    try {
      emit(const RecordingLoading());

      // Use the enhanced DeleteRecordFileUseCase that handles Live Activity
      await _deleteRecordingFileUseCase.execute(
        DeleteRecordFileParams(
          session: currentState.session,
        ),
      );

      // Return to initial state
      emit(const RecordingInitial());
    } catch (e) {
      final failure = ExceptionHandler.handle(e);
      emit(RecordingError(failure: failure));
    }
  }

  RecorderSessionEntity? _getCurrentSession() {
    final currentState = state;
    if (currentState is RecordingActive) return currentState.session;
    if (currentState is RecordingPaused) return currentState.session;
    if (currentState is RecordingCompleted) return currentState.session;
    return null;
  }

  void emitErrorState(
    AppFailure failure, {
    bool canRetry = true,
    VoidCallback? retryAction,
  }) {
    emit(
      RecordingError(
        failure: failure,
        canRetry: canRetry,
        retryAction: retryAction,
      ),
    );
  }

  @override
  Future<void> close() {
    _recordingUpdatesSubscription?.cancel();
    _recorderController = null;
    return super.close();
  }
}
