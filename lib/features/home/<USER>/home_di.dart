import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/home/<USER>/datasources/api_services/home_api_service.dart';
import 'package:glidic_app/features/home/<USER>/datasources/home_remote_datasource.dart';
import 'package:glidic_app/features/home/<USER>/repositories/home_repository_impl.dart';
import 'package:glidic_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:glidic_app/features/home/<USER>/usecases/get_dashboard_data_usecase.dart';

/// Dependency injection configuration for Home feature
class HomeDI {
  /// Initialize Home feature dependencies
  static Future<void> init() async {
    // API Service
    sl.registerLazySingleton<HomeApiService>(
      () => HomeApiService(sl()),
    );

    // Data Sources
    sl.registerLazySingleton<HomeRemoteDataSource>(
      () => HomeRemoteDataSourceImpl(sl()),
    );

    // Repository
    sl.registerLazySingleton<HomeRepository>(
      () => HomeRepositoryImpl(
        remoteDataSource: sl(),
      ),
    );

    // Use Cases
    sl.registerFactory(() => GetDashboardDataUseCase(sl()));
  }
}
