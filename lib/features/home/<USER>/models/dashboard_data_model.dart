import 'package:glidic_app/features/home/<USER>/entities/dashboard_data.dart';

/// Data model for DashboardData that extends the domain entity
class DashboardDataModel extends DashboardData {
  const DashboardDataModel({
    required super.welcomeMessage,
    required super.totalUsers,
    required super.totalPosts,
    required super.recentActivities,
  });

  /// Create DashboardDataModel from JSON
  factory DashboardDataModel.fromJson(Map<String, dynamic> json) {
    return DashboardDataModel(
      welcomeMessage: json['welcome_message'] as String,
      totalUsers: json['total_users'] as int,
      totalPosts: json['total_posts'] as int,
      recentActivities: List<String>.from(json['recent_activities'] as List),
    );
  }

  /// Convert DashboardDataModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'welcome_message': welcomeMessage,
      'total_users': totalUsers,
      'total_posts': totalPosts,
      'recent_activities': recentActivities,
    };
  }

  /// Create DashboardDataModel from DashboardData entity
  factory DashboardDataModel.fromEntity(DashboardData entity) {
    return DashboardDataModel(
      welcomeMessage: entity.welcomeMessage,
      totalUsers: entity.totalUsers,
      totalPosts: entity.totalPosts,
      recentActivities: entity.recentActivities,
    );
  }

  /// Convert to DashboardData entity
  DashboardData toEntity() {
    return DashboardData(
      welcomeMessage: welcomeMessage,
      totalUsers: totalUsers,
      totalPosts: totalPosts,
      recentActivities: recentActivities,
    );
  }
}
