import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/home/<USER>/entities/dashboard_data.dart';
import 'package:glidic_app/features/home/<USER>/repositories/home_repository.dart';

/// Use case for getting dashboard data
class GetDashboardDataUseCase {
  GetDashboardDataUseCase(this._homeRepository);

  final HomeRepository _homeRepository;

  /// Execute dashboard data retrieval
  /// Throws [AppFailure] or its subclasses on error:
  /// - [NetworkFailure] for network-related errors
  /// - [UnknownFailure] for unexpected errors
  Future<DashboardData> execute() async {
    return _homeRepository.getDashboardData();
  }
}
