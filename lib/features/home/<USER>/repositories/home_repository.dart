import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/home/<USER>/entities/dashboard_data.dart';

/// Abstract repository interface for home-related operations
///
/// All methods throw [AppFailure] or its subclasses on error:
/// - [NetworkFailure] for network-related errors
/// - [UnknownFailure] for unexpected errors
abstract class HomeRepository {
  /// Get dashboard data for the home screen
  /// Throws [NetworkFailure] if no internet connection
  /// Throws [UnknownFailure] for unexpected errors
  Future<DashboardData> getDashboardData();

  /// Refresh dashboard data
  /// Throws [NetworkFailure] if no internet connection
  /// Throws [UnknownFailure] for unexpected errors
  Future<DashboardData> refreshDashboardData();
}
