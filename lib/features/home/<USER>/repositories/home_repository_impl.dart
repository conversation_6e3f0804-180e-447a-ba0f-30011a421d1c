import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/features/home/<USER>/datasources/home_remote_datasource.dart';
import 'package:glidic_app/features/home/<USER>/entities/dashboard_data.dart';
import 'package:glidic_app/features/home/<USER>/repositories/home_repository.dart';

/// Implementation of HomeRepository
class HomeRepositoryImpl implements HomeRepository {
  const HomeRepositoryImpl({
    required this.remoteDataSource,
  });

  final HomeRemoteDataSource remoteDataSource;

  @override
  Future<DashboardData> getDashboardData() async {
    try {
      final dashboardData = await remoteDataSource.getDashboardData();
      return dashboardData.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<DashboardData> refreshDashboardData() async {
    try {
      final dashboardData = await remoteDataSource.refreshDashboardData();
      return dashboardData.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }


}
