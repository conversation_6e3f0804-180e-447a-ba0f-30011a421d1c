import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_cubit.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_state.dart';

/// Home screen app bar widget
/// Contains title and logout functionality
class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  const HomeAppBar({
    super.key,
    required this.onLogout,
  });

  final VoidCallback onLogout;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: const Text('Dashboard'),
      actions: [
        BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            return PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'logout') {
                  _handleLogout(context);
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'logout',
                  enabled: !state.isLoggingOut,
                  child: Row(
                    children: [
                      if (state.isLoggingOut)
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      else
                        const Icon(Icons.logout),
                      const SizedBox(width: 8),
                      Text(state.isLoggingOut ? 'Logging out...' : 'Logout'),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  void _handleLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              context.read<HomeCubit>().logout();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
