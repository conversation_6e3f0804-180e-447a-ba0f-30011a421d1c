import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/core/presentation/router/route_paths.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_cubit.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_state.dart';

/// Home screen content widget
/// Handles different states and displays appropriate content
class HomeContent extends StatelessWidget {
  const HomeContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      builder: (context, state) {
        if (state.isLoading) {
          return _buildLoadingState();
        } else if (state.hasError && !state.hasData) {
          return _buildErrorState(context, state);
        } else if (state.hasData) {
          return _buildLoadedState(context, state);
        } else {
          return _buildEmptyState(context);
        }
      },
    );
  }

  /// Build loading state UI
  Widget _buildLoadingState() {
    return Builder(
      builder: (context) {
        final l10n = AppLocalizations.of(context);
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: Dimensions.defaultPadding),
              Text(l10n.loadingDashboardData),
            ],
          ),
        );
      },
    );
  }

  /// Build error state UI
  Widget _buildErrorState(BuildContext context, HomeState state) {
    final l10n = AppLocalizations.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(Dimensions.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: ColorConstants.errorColor,
            ),
            const SizedBox(height: Dimensions.defaultPadding),
            Text(
              l10n.failedToLoadDashboard,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w500,
                color: ColorConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Dimensions.smallPadding),
            Text(
              state.failure?.message ?? l10n.unexpectedError,
              style: const TextStyle(
                color: ColorConstants.onSurfaceVariant,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Dimensions.largePadding),
            ElevatedButton.icon(
              onPressed: () => context.read<HomeCubit>().retryLoadData(),
              icon: const Icon(Icons.refresh),
              label: Text(l10n.retry),
            ),
          ],
        ),
      ),
    );
  }

  /// Build loaded state UI with data
  Widget _buildLoadedState(BuildContext context, HomeState state) {
    final l10n = AppLocalizations.of(context);

    return RefreshIndicator(
      onRefresh: () => context.read<HomeCubit>().refreshDashboardData(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(Dimensions.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome message
            _buildWelcomeSection(context),
            const SizedBox(height: Dimensions.largePadding),

            // Dashboard statistics section removed
            const SizedBox(height: Dimensions.largePadding),

            // Additional sections can be added here
            _buildQuickActions(context),

            // Show refresh indicator if refreshing
            if (state.isRefreshing)
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: Dimensions.defaultPadding,
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      const SizedBox(width: 8),
                      Text(l10n.refreshing),
                    ],
                  ),
                ),
              ),

            // Show error message if refresh failed but data is still available
            if (state.hasError && state.hasData)
              Container(
                margin: const EdgeInsets.only(top: Dimensions.defaultPadding),
                padding: const EdgeInsets.all(Dimensions.defaultPadding),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(Dimensions.radiusMd),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        l10n.failedToRefresh(
                          state.failure?.message ?? l10n.unexpectedError,
                        ),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build empty state UI
  Widget _buildEmptyState(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: Dimensions.defaultPadding),
          Text(
            l10n.noDataAvailable,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: Dimensions.smallPadding),
          Text(
            l10n.pullToRefresh,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  /// Build welcome section
  Widget _buildWelcomeSection(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.welcome,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: ColorConstants.textPrimary,
          ),
        ),
        const SizedBox(height: Dimensions.smallPadding),
        Text(
          l10n.welcomeMessage,
          style: const TextStyle(
            color: ColorConstants.onSurfaceVariant,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  /// Build quick actions section
  Widget _buildQuickActions(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.quickActions,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 22,
            color: ColorConstants.textPrimary,
          ),
        ),
        const SizedBox(height: Dimensions.defaultPadding),
        // Recording button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              context.router.pushPath(RoutePaths.recording);
            },
            icon: const Icon(Icons.mic, color: Colors.white),
            label: const Text(
              'Start Recording',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 16),
              textStyle:
                  const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ),

        const SizedBox(height: Dimensions.defaultPadding),

        // Records button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              context.router.pushPath(RoutePaths.recordsList);
            },
            icon: const Icon(Icons.list),
            label: const Text(
              'View Recordings',
              style: TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: Colors.red),
              textStyle: const TextStyle(fontSize: 18),
            ),
          ),
        ),

        const SizedBox(height: Dimensions.defaultPadding),

        // Record button (alternative navigation to Records List)
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              context.router.pushPath(RoutePaths.recordsList);
            },
            icon: const Icon(Icons.folder, color: Colors.white),
            label: const Text(
              'Record',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              padding: const EdgeInsets.symmetric(vertical: 16),
              textStyle: const TextStyle(fontSize: 18),
            ),
          ),
        ),

        const SizedBox(height: Dimensions.defaultPadding),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n.featureComingSoon)),
                  );
                },
                icon: const Icon(Icons.add),
                label: Text(l10n.addNew),
              ),
            ),
            const SizedBox(width: Dimensions.defaultPadding),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n.featureComingSoon)),
                  );
                },
                icon: const Icon(Icons.settings),
                label: Text(l10n.settings),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
