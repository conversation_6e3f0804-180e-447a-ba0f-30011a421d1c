import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/domain/usecases/logout_usecase.dart';
import 'package:glidic_app/features/home/<USER>/entities/dashboard_data.dart';
import 'package:glidic_app/features/home/<USER>/usecases/get_dashboard_data_usecase.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_state.dart';

/// Home screen cubit (ViewModel in MVVM pattern)
/// Manages the state and business logic specific to the home screen
class HomeCubit extends Cubit<HomeState> {
  HomeCubit({
    required this.getDashboardDataUseCase,
    required this.logoutUseCase,
  }) : super(const HomeInitial());

  final GetDashboardDataUseCase getDashboardDataUseCase;
  final LogoutUseCase logoutUseCase;

  /// Load dashboard data (initial load)
  Future<void> loadDashboardData() async {
    emit(const HomeLoading());

    try {
      final data = await getDashboardDataUseCase.execute();
      emit(HomeLoaded(dashboardData: data));
    } catch (e) {
      if (e is AppFailure) {
        emit(HomeError(failure: e));
      } else {
        emit(
          const HomeError(
            failure: UnknownFailure('Failed to load dashboard data'),
          ),
        );
      }
    }
  }

  /// Refresh dashboard data (pull-to-refresh)
  Future<void> refreshDashboardData() async {
    final currentData = state.dashboardData;

    if (currentData != null) {
      emit(HomeRefreshing(currentData: currentData));
    } else {
      emit(const HomeLoading());
    }

    try {
      final data = await getDashboardDataUseCase.execute();
      emit(
        HomeLoaded(
          dashboardData: data,
          isRefreshed: true,
        ),
      );
    } catch (e) {
      if (e is AppFailure) {
        emit(
          HomeError(
            failure: e,
            previousData: currentData,
          ),
        );
      } else {
        // Handle unexpected errors during refresh
        emit(
          HomeError(
            failure: const UnknownFailure('Failed to refresh dashboard data'),
            previousData: currentData,
          ),
        );
      }
    }
  }

  /// Retry loading data after error
  Future<void> retryLoadData() async {
    await loadDashboardData();
  }

  /// Perform logout operation
  Future<void> logout() async {
    emit(const HomeLoggingOut());

    try {
      await logoutUseCase.execute();
      emit(const HomeLoggedOut());
    } catch (e) {
      // Always succeed logout locally even if server call fails
      // This is a common pattern for logout operations
      emit(const HomeLoggedOut());
    }
  }

  /// Reset to initial state
  void reset() {
    emit(const HomeInitial());
  }

  /// Get current dashboard data
  DashboardData? get currentDashboardData => state.dashboardData;

  /// Check if data is available
  bool get hasData => state.hasData;

  /// Check if currently loading
  bool get isLoading => state.isLoading;

  /// Check if currently refreshing
  bool get isRefreshing => state.isRefreshing;
}
