import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/core/presentation/router/app_router.gr.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_cubit.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/home_state.dart';
import 'package:glidic_app/features/home/<USER>/screens/home_screen/widgets/home_app_bar.dart';

/// Home screen (View in MVVM pattern)
/// Demonstrates Clean Architecture implementation with MVVM pattern
@RoutePage()
class HomeScreen extends StatelessWidget implements AutoRouteWrapper {
  const HomeScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(
        getDashboardDataUseCase: sl(),
        logoutUseCase: sl(),
      )..loadDashboardData(),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeCubit, HomeState>(
      listener: (context, state) {
        if (state.isLoggedOut) {
          _handleLogout(context);
        }
      },
      child: Scaffold(
        appBar: HomeAppBar(
          onLogout: () => _handleLogout(context),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),
              // TODO: Replace with ARB localization in production
              const Text(
                'Welcome to Glidic App', // Hardcoded text for POC - TODO: Move to ARB localization
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              PrimaryButton(
                text:
                    'Create Calendar Event', // Hardcoded text for POC - TODO: Move to ARB localization
                onPressed: () {
                  AppNavigator.to(const CalendarEventRoute());
                },
                icon: Icons.calendar_today,
              ),
              const SizedBox(height: 16),
              PrimaryButton(
                text:
                    'View Calendar Events', // Hardcoded text for POC - TODO: Move to ARB localization
                onPressed: () {
                  AppNavigator.to(const CalendarEventsListRoute());
                },
                icon: Icons.event_note,
              ),
              const SizedBox(height: 16),
              PrimaryButton(
                text:
                    'Create Google Calendar Event', // Hardcoded text for POC - TODO: Move to ARB localization
                onPressed: () {
                  AppNavigator.to(const GoogleCalendarEventRoute());
                },
                icon: Icons.event_available,
              ),
              const SizedBox(height: 16),
              PrimaryButton(
                text:
                    'View Google Calendar Events', // Hardcoded text for POC - TODO: Move to ARB localization
                onPressed: () {
                  AppNavigator.to(const GoogleCalendarEventsListRoute());
                },
                icon: Icons.list_alt,
              ),
              const SizedBox(height: 16),
              PrimaryButton(
                text:
                    'View Records List', // Hardcoded text for POC - TODO: Move to ARB localization
                onPressed: () {
                  AppNavigator.to(const RecordsListRoute());
                },
                icon: Icons.mic,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle logout navigation
  void _handleLogout(BuildContext context) {
    AppNavigator.replace(const LoginRoute());
  }
}
