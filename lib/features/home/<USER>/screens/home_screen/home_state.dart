import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/home/<USER>/entities/dashboard_data.dart';

/// Home screen specific state classes designed for UI needs
/// These states are focused specifically on the home screen flow
abstract class HomeState extends Equatable {
  const HomeState();
}

/// Initial state - home screen is initializing
class HomeInitial extends HomeState {
  const HomeInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state - dashboard data is being loaded
class HomeLoading extends HomeState {
  const HomeLoading();

  @override
  List<Object?> get props => [];
}

/// Refreshing state - dashboard data is being refreshed (different from initial loading)
class HomeRefreshing extends HomeState {
  const HomeRefreshing({
    required this.currentData,
  });

  final DashboardData currentData;

  @override
  List<Object?> get props => [currentData];
}

/// Success state - dashboard data loaded successfully
class HomeLoaded extends HomeState {
  const HomeLoaded({
    required this.dashboardData,
    this.isRefreshed = false,
  });

  final DashboardData dashboardData;
  final bool isRefreshed;

  @override
  List<Object?> get props => [dashboardData, isRefreshed];
}

/// Error state - failed to load dashboard data
class HomeError extends HomeState with ErrorStateMixin {
  const HomeError({
    required this.failure,
    this.previousData,
    this.canRetry = true,
  });

  @override
  final AppFailure failure;
  final DashboardData? previousData;
  @override
  final bool canRetry;

  bool get isRetryableError => canRetry;

  bool get hasPreviousData => previousData != null;

  @override
  List<Object?> get props => [failure, previousData, canRetry];
}

/// Logout in progress state
class HomeLoggingOut extends HomeState {
  const HomeLoggingOut();

  @override
  List<Object?> get props => [];
}

/// Logout success state
class HomeLoggedOut extends HomeState {
  const HomeLoggedOut();

  @override
  List<Object?> get props => [];
}

/// Extension methods for convenient state checking
extension HomeStateExtension on HomeState {
  /// Check if home is loading (initial load)
  bool get isLoading => this is HomeLoading;

  /// Check if home is refreshing
  bool get isRefreshing => this is HomeRefreshing;

  /// Check if data is loaded
  bool get isLoaded => this is HomeLoaded;

  /// Check if there's an error
  bool get hasError => this is HomeError;

  /// Check if state is initial
  bool get isInitial => this is HomeInitial;

  /// Check if logout is in progress
  bool get isLoggingOut => this is HomeLoggingOut;

  /// Check if logged out
  bool get isLoggedOut => this is HomeLoggedOut;

  /// Get dashboard data (null if not loaded)
  DashboardData? get dashboardData {
    if (this is HomeLoaded) {
      return (this as HomeLoaded).dashboardData;
    } else if (this is HomeRefreshing) {
      return (this as HomeRefreshing).currentData;
    } else if (this is HomeError) {
      return (this as HomeError).previousData;
    }
    return null;
  }

  /// Get error failure (null if no error)
  AppFailure? get failure {
    if (this is HomeError) {
      return (this as HomeError).failure;
    }
    return null;
  }

  /// Check if data is available (even with error)
  bool get hasData => dashboardData != null;

  /// Check if this is a refresh operation
  bool get isRefreshOperation => this is HomeRefreshing;
}
