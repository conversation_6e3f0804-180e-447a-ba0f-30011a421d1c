import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/features/home/<USER>/datasources/api_services/home_api_service.dart';
import 'package:glidic_app/features/home/<USER>/models/dashboard_data_model.dart';

/// Abstract interface for home remote data source
abstract class HomeRemoteDataSource {
  Future<DashboardDataModel> getDashboardData();
  Future<DashboardDataModel> refreshDashboardData();
}

/// Implementation of home remote data source
class HomeRemoteDataSourceImpl implements HomeRemoteDataSource {
  const HomeRemoteDataSourceImpl(this._homeApiService);

  // ignore: unused_field
  final HomeApiService _homeApiService;

  @override
  Future<DashboardDataModel> getDashboardData() async {
    try {
      // For demo purposes, we'll return mock data
      // In a real app, this would use: return await _homeApiService.getDashboardData();
      await Future.delayed(
        const Duration(seconds: 1),
      ); // Simulate network delay

      return const DashboardDataModel(
        welcomeMessage: 'Welcome to Flutter Base Project!',
        totalUsers: 1250,
        totalPosts: 3420,
        recentActivities: [
          'User John Doe registered',
          'New post published: "Flutter Best Practices"',
          'System maintenance completed',
          'Database backup successful',
          'New feature deployed',
        ],
      );
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<DashboardDataModel> refreshDashboardData() async {
    try {
      // For demo purposes, we'll return mock data
      // In a real app, this would use: return await _homeApiService.refreshDashboardData();
      return await getDashboardData();
    } catch (e) {
      throw Exception('Failed to refresh dashboard data: ${e.toString()}');
    }
  }
}
