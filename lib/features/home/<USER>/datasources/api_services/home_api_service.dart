import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/features/home/<USER>/models/dashboard_data_model.dart';
import 'package:retrofit/retrofit.dart';

part 'home_api_service.g.dart';

/// Retrofit API service for home/dashboard endpoints
/// Provides type-safe API calls with proper request/response models
@RestApi()
abstract class HomeApiService {
  factory HomeApiService(Dio dio) = _HomeApiService;

  /// Get dashboard data
  /// Returns dashboard information including stats and recent activities
  @GET(NetworkConstants.homeDashboard)
  Future<DashboardDataModel> getDashboardData();

  /// Refresh dashboard data
  /// Returns updated dashboard information
  @POST(NetworkConstants.homeRefreshDashboard)
  Future<DashboardDataModel> refreshDashboardData();
}
