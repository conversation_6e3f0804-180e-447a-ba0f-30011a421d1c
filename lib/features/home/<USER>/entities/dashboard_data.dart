import 'package:equatable/equatable.dart';

/// Dashboard data entity for the home screen
/// Contains information to be displayed on the main dashboard
class DashboardData extends Equatable {
  const DashboardData({
    required this.welcomeMessage,
    required this.totalUsers,
    required this.totalPosts,
    required this.recentActivities,
  });

  final String welcomeMessage;
  final int totalUsers;
  final int totalPosts;
  final List<String> recentActivities;

  @override
  List<Object?> get props => [
        welcomeMessage,
        totalUsers,
        totalPosts,
        recentActivities,
      ];

  DashboardData copyWith({
    String? welcomeMessage,
    int? totalUsers,
    int? totalPosts,
    List<String>? recentActivities,
  }) {
    return DashboardData(
      welcomeMessage: welcomeMessage ?? this.welcomeMessage,
      totalUsers: totalUsers ?? this.totalUsers,
      totalPosts: totalPosts ?? this.totalPosts,
      recentActivities: recentActivities ?? this.recentActivities,
    );
  }
}
