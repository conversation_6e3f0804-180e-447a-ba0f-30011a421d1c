import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/domain/entities/user_entity.dart';
import 'package:glidic_app/features/auth/domain/repositories/auth_repository.dart';

/// Use case for user login
/// Encapsulates the business logic for authentication
class LoginUseCase {
  LoginUseCase(this._authRepository);

  final AuthRepository _authRepository;

  /// Execute login with the provided parameters
  ///
  /// Throws [ValidationFailure] if input validation fails
  /// Throws [AuthFailure] if authentication fails
  /// Throws [NetworkFailure] if network request fails
  Future<UserEntity> execute(LoginParams params) async {
    // Validate input parameters
    _validateParams(params);

    // Execute the login operation
    return _authRepository.login(
      email: params.email,
      password: params.password,
    );
  }

  /// Validate login parameters
  /// Throws [ValidationFailure] if validation fails
  void _validateParams(LoginParams params) {
    if (params.email.isEmpty) {
      throw const ValidationFailure('Email is required');
    }

    if (params.password.isEmpty) {
      throw const ValidationFailure('Password is required');
    }

    // Basic email validation
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(params.email)) {
      throw const ValidationFailure('Please enter a valid email');
    }

    if (params.password.length < 6) {
      throw const ValidationFailure('Password must be at least 6 characters');
    }
  }
}

/// Parameters for login use case
class LoginParams {
  const LoginParams({
    required this.email,
    required this.password,
  });

  final String email;
  final String password;
}
