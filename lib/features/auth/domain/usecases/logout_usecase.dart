import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/domain/repositories/auth_repository.dart';

/// Use case for user logout
/// Handles the business logic for signing out users
class LogoutUseCase {
  LogoutUseCase(this._authRepository);

  final AuthRepository _authRepository;

  /// Execute logout operation
  /// Throws [AppFailure] or its subclasses on error:
  /// - [NetworkFailure] for network-related errors
  /// - [AuthFailure] for authentication-related errors
  /// - [UnknownFailure] for unexpected errors
  Future<void> execute() async {
    return _authRepository.logout();
  }
}
