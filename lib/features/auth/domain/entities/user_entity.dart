import 'package:equatable/equatable.dart';

/// User entity representing the core user data
/// This is part of the domain layer and contains only business logic
/// It's independent of any external frameworks or data sources
class UserEntity extends Equatable {
  const UserEntity({
    required this.id,
    required this.email,
    required this.name,
    this.profilePicture,
    this.isEmailVerified = false,
  });

  final String id;
  final String email;
  final String name;
  final String? profilePicture;
  final bool isEmailVerified;

  @override
  List<Object?> get props => [
        id,
        email,
        name,
        profilePicture,
        isEmailVerified,
      ];

  /// Create a copy of the entity with updated fields
  UserEntity copyWith({
    String? id,
    String? email,
    String? name,
    String? profilePicture,
    bool? isEmailVerified,
  }) {
    return UserEntity(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
    );
  }
}
