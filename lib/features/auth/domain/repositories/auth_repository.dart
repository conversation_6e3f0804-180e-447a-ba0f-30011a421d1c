import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/domain/entities/user_entity.dart';

/// Abstract repository interface for authentication operations
/// This defines the contract that the data layer must implement
/// Following the dependency inversion principle of Clean Architecture
abstract class AuthRepository {
  /// Login with email and password
  ///
  /// Throws [AuthFailure] if authentication fails
  /// Throws [ValidationFailure] if input validation fails
  /// Throws [NetworkFailure] if network request fails
  Future<UserEntity> login({
    required String email,
    required String password,
  });

  /// Register a new user
  ///
  /// Throws [AuthFailure] if registration fails
  /// Throws [ValidationFailure] if input validation fails
  /// Throws [NetworkFailure] if network request fails
  Future<UserEntity> register({
    required String email,
    required String password,
    required String name,
  });

  /// Logout the current user
  ///
  /// Throws [AuthFailure] if logout fails
  /// Throws [NetworkFailure] if network request fails
  Future<void> logout();

  /// Get current user information
  ///
  /// Returns null if no user is logged in
  /// Throws [AuthFailure] if authentication state is invalid
  /// Throws [StorageFailure] if local storage access fails
  /// Throws [NetworkFailure] if network request fails
  Future<UserEntity?> getCurrentUser();

  /// Check if user is logged in
  ///
  /// Throws [StorageFailure] if local storage access fails
  Future<bool> isLoggedIn();

  /// Refresh authentication token
  ///
  /// Throws [AuthFailure] if token refresh fails
  /// Throws [NetworkFailure] if network request fails
  Future<void> refreshToken();

  /// Send password reset email
  ///
  /// Throws [ValidationFailure] if email validation fails
  /// Throws [NetworkFailure] if network request fails
  Future<void> sendPasswordResetEmail({
    required String email,
  });

  /// Verify email with token
  ///
  /// Throws [AuthFailure] if token verification fails
  /// Throws [ValidationFailure] if token validation fails
  /// Throws [NetworkFailure] if network request fails
  Future<void> verifyEmail({
    required String token,
  });
}
