import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/auth/data/datasources/api_services/auth_api_service.dart';
import 'package:glidic_app/features/auth/data/datasources/auth_local_datasource_impl.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_local_datasource.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_remote_datasource.dart';
import 'package:glidic_app/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:glidic_app/features/auth/domain/repositories/auth_repository.dart';
import 'package:glidic_app/features/auth/domain/usecases/login_usecase.dart';
import 'package:glidic_app/features/auth/domain/usecases/logout_usecase.dart';

/// Dependency injection configuration for Auth feature
/// This class encapsulates all Auth-related dependency registrations
class AuthDI {
  /// Initialize Auth feature dependencies
  static Future<void> init() async {
    // API Service
    sl.registerLazySingleton<AuthApiService>(
      () => AuthApiService(sl()),
    );

    // Data Sources
    sl.registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(sl()),
    );

    sl.registerLazySingleton<AuthLocalDataSource>(
      () => AuthLocalDataSourceImpl(
        secureStorageService: sl(),
        preferencesService: sl(),
      ),
    );

    // Repository
    sl.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
      ),
    );

    // Use Cases
    sl.registerFactory(() => LoginUseCase(sl()));
    sl.registerFactory(() => LogoutUseCase(sl()));
  }
}
