import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/domain/usecases/login_usecase.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_state.dart';

/// Login screen cubit (ViewModel in MVVM pattern)
/// Manages the state and business logic specific to the login screen
class LoginCubit extends Cubit<LoginState> {
  LoginCubit({
    required this.loginUseCase,
  }) : super(const LoginInitial());

  final LoginUseCase loginUseCase;

  /// Validate email format
  String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate password
  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }

    return null;
  }

  /// Validate form and emit validation state
  void validateForm({
    required String email,
    required String password,
  }) {
    final emailError = validateEmail(email);
    final passwordError = validatePassword(password);
    final isFormValid = emailError == null && passwordError == null;

    emit(
      LoginFormValidation(
        emailError: emailError,
        passwordError: passwordError,
        isFormValid: isFormValid,
      ),
    );
  }

  /// Perform login operation
  Future<void> login({
    required String email,
    required String password,
  }) async {
    // First validate the form
    final emailError = validateEmail(email);
    final passwordError = validatePassword(password);

    if (emailError != null || passwordError != null) {
      emit(
        LoginFormValidation(
          emailError: emailError,
          passwordError: passwordError,
          isFormValid: false,
        ),
      );
      return;
    }

    // Emit loading state
    emit(const LoginLoading());

    try {
      // Execute login use case
      final user = await loginUseCase.execute(
        LoginParams(email: email, password: password),
      );

      // Emit success state
      emit(LoginSuccess(user: user));
    } catch (e) {
      // Handle different types of failures
      if (e is AppFailure) {
        emit(LoginError(failure: e));
      } else {
        // Handle unexpected errors
        final failure = ExceptionHandler.handle(e);
        emit(LoginError(failure: failure));
      }
    }
  }

  /// Reset to initial state
  void reset() {
    emit(const LoginInitial());
  }

  /// Clear form validation errors
  void clearValidation() {
    if (state.isFormValidation) {
      emit(const LoginInitial());
    }
  }
}
