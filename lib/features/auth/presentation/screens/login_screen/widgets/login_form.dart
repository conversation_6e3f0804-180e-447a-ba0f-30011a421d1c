import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/core/presentation/widgets/widgets.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_cubit.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_state.dart';

/// Login form widget specific to the login screen
/// Contains form fields and validation logic
class LoginForm extends StatefulWidget {
  const LoginForm({
    super.key,
    required this.onLoginSuccess,
  });

  final VoidCallback onLoginSuccess;

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  // Widget-specific dimensions (from Figma design)
  static const double _formFieldGap = 12.0;
  static const double _formToLinksGap = 13.0;
  static const double _buttonGap = 22.0;

  // Widget-specific colors (from Figma design)
  static const Color _forgotPasswordLinkColor = Color(0xFF000000);

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return BlocListener<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state.isSuccess) {
          widget.onLoginSuccess();
        } else if (state.hasError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.failure?.message ?? l10n.loginFailed),
              backgroundColor: ColorConstants.errorColor,
            ),
          );
        }
      },
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Email field
            BlocBuilder<LoginCubit, LoginState>(
              builder: (context, state) {
                return PrimaryTextField(
                  controller: _emailController,
                  hintText: l10n.emailPlaceholder,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (state.isFormValidation) {
                      return state.formValidation?.emailError;
                    }
                    return context.read<LoginCubit>().validateEmail(value);
                  },
                  onChanged: (value) {
                    // Real-time validation
                    context.read<LoginCubit>().validateForm(
                          email: value,
                          password: _passwordController.text,
                        );
                  },
                );
              },
            ),
            const SizedBox(height: _formFieldGap),

            // Password field
            BlocBuilder<LoginCubit, LoginState>(
              builder: (context, state) {
                return PrimaryTextField(
                  controller: _passwordController,
                  hintText: l10n.passwordPlaceholder,
                  obscureText: _obscurePassword,
                  suffixIcon: IconButton(
                    icon: SvgPicture.asset(
                      PathConstants.eyeOffIcon,
                      width: 16,
                      height: 16,
                      colorFilter: const ColorFilter.mode(
                        ColorConstants.textFieldIcon,
                        BlendMode.srcIn,
                      ),
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  validator: (value) {
                    if (state.isFormValidation) {
                      return state.formValidation?.passwordError;
                    }
                    return context.read<LoginCubit>().validatePassword(value);
                  },
                  onChanged: (value) {
                    // Real-time validation
                    context.read<LoginCubit>().validateForm(
                          email: _emailController.text,
                          password: value,
                        );
                  },
                );
              },
            ),
            const SizedBox(height: _formToLinksGap),

            // Forgot password link
            Align(
              alignment: Alignment.center,
              child: TextButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(l10n.forgotPasswordFeatureComingSoon),
                    ),
                  );
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  l10n.forgotPasswordLink,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _forgotPasswordLinkColor,
                    height: 1.4,
                    letterSpacing: -0.01,
                  ),
                ),
              ),
            ),
            const SizedBox(height: _buttonGap),

            // Login button
            BlocBuilder<LoginCubit, LoginState>(
              builder: (context, state) {
                return PrimaryButton(
                  onPressed: state.isLoading ? null : _handleLogin,
                  text: l10n.loginButton,
                  isLoading: state.isLoading,
                );
              },
            ),
            const SizedBox(height: _buttonGap),

            // Sign up button
            PrimaryButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(l10n.signUpFeatureComingSoon),
                  ),
                );
              },
              text: l10n.signUpButton,
            ),
          ],
        ),
      ),
    );
  }

  void _handleLogin() {
    // Clear any previous validation states
    context.read<LoginCubit>().clearValidation();

    // Trigger form validation
    if (_formKey.currentState?.validate() ?? false) {
      context.read<LoginCubit>().login(
            email: _emailController.text.trim(),
            password: _passwordController.text,
          );
    }
  }
}
