import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';

/// Login logo widget
/// Displays the application logo with proper dimensions and styling
/// matching the Figma design specifications
class <PERSON>ginLogo extends StatelessWidget {
  const LoginLogo({super.key});

  // Widget-specific dimensions (from Figma design)
  static const double _logoWidth = 304.0;
  static const double _logoHeight = 98.0;

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      PathConstants.logoImage,
      width: _logoWidth,
      height: _logoHeight,
    );
  }
}
