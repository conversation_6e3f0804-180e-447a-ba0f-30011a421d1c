import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/domain/entities/user_entity.dart';

/// Login screen specific state classes designed for UI needs
/// These states are focused specifically on the login screen flow
abstract class LoginState extends Equatable {
  const LoginState();
}

/// Initial state - login screen is ready for user input
class LoginInitial extends LoginState {
  const LoginInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state - login operation in progress
class LoginLoading extends LoginState {
  const LoginLoading();

  @override
  List<Object?> get props => [];
}

/// Success state - login completed successfully
class LoginSuccess extends LoginState {
  const LoginSuccess({
    required this.user,
    this.isFirstLogin = false,
  });

  final UserEntity user;
  final bool isFirstLogin;

  @override
  List<Object?> get props => [user, isFirstLogin];
}

/// Error state - login operation failed
class LoginError extends LoginState {
  const LoginError({
    required this.failure,
  });

  final AppFailure failure;

  @override
  List<Object?> get props => [failure];
}

/// Form validation state - for real-time form validation feedback
class LoginFormValidation extends LoginState {
  const LoginFormValidation({
    this.emailError,
    this.passwordError,
    this.isFormValid = false,
  });

  final String? emailError;
  final String? passwordError;
  final bool isFormValid;

  @override
  List<Object?> get props => [emailError, passwordError, isFormValid];
}

/// Extension methods for convenient state checking
extension LoginStateExtension on LoginState {
  /// Check if login is in progress
  bool get isLoading => this is LoginLoading;

  /// Check if login was successful
  bool get isSuccess => this is LoginSuccess;

  /// Check if there's an error
  bool get hasError => this is LoginError;

  /// Check if state is initial
  bool get isInitial => this is LoginInitial;

  /// Check if state is form validation
  bool get isFormValidation => this is LoginFormValidation;

  /// Get authenticated user (null if not successful)
  UserEntity? get user {
    if (this is LoginSuccess) {
      return (this as LoginSuccess).user;
    }
    return null;
  }

  /// Get error failure (null if no error)
  AppFailure? get failure {
    if (this is LoginError) {
      return (this as LoginError).failure;
    }
    return null;
  }

  /// Get form validation errors
  LoginFormValidation? get formValidation {
    if (this is LoginFormValidation) {
      return this as LoginFormValidation;
    }
    return null;
  }
}
