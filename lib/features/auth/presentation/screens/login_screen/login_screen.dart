import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/core/presentation/router/app_router.gr.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/login_cubit.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/widgets/login_form.dart';
import 'package:glidic_app/features/auth/presentation/screens/login_screen/widgets/login_logo.dart';

/// Login screen (View in MVVM pattern)
/// Demonstrates Clean Architecture implementation with MVVM pattern
@RoutePage()
class LoginScreen extends StatelessWidget implements AutoRouteWrapper {
  const LoginScreen({super.key});

  // Widget-specific dimensions (from Figma design)
  static const double _logoHorizontalMargin = 38.0;
  static const double _formContainerHorizontalMargin = 26.0;

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(
        loginUseCase: sl(),
      ),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: SizedBox(
            width: double.infinity,
            child: Column(
              children: [
                // Logo section with proper positioning
                Container(
                  margin: const EdgeInsets.only(
                    top:
                        150, // Adjusted from Figma's 190px to account for SafeArea
                    left: _logoHorizontalMargin,
                    right: _logoHorizontalMargin,
                  ),
                  child: const Center(
                    child: LoginLogo(),
                  ),
                ),

                // Form section with proper positioning
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(
                    top: 41,
                    left: _formContainerHorizontalMargin,
                    right: _formContainerHorizontalMargin,
                  ),
                  child: LoginForm(
                    onLoginSuccess: () => _handleLoginSuccess(context),
                  ),
                ),

                const SizedBox(height: Dimensions.largePadding),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Handle successful login navigation
  void _handleLoginSuccess(BuildContext context) {
    AppNavigator.replace(const HomeRoute());
  }
}
