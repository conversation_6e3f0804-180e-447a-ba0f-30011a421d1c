import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_local_datasource.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_remote_datasource.dart';
import 'package:glidic_app/features/auth/data/models/user_model.dart';
import 'package:glidic_app/features/auth/domain/entities/user_entity.dart';
import 'package:glidic_app/features/auth/domain/repositories/auth_repository.dart';

/// Implementation of AuthRepository
/// This class bridges the domain and data layers
/// Handles both remote and local data sources with proper error handling
class AuthRepositoryImpl implements AuthRepository {
  const AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;

  @override
  Future<UserEntity> login({
    required String email,
    required String password,
  }) async {
    // ========================================
    // TEMPORARY MOCK IMPLEMENTATION FOR TESTING
    // ========================================
    // This bypasses the actual API call to allow web platform testing
    // without requiring a real backend. Remove this mock implementation
    // when connecting to a real API endpoint.

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 800));

      // Create mock user data based on the provided email
      final mockUser = UserModel(
        id: 'mock_user_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        name: _extractNameFromEmail(email),
        profilePicture:
            'https://ui-avatars.com/api/?name=${_extractNameFromEmail(email)}&background=6366f1&color=fff',
        isEmailVerified: true,
      );

      // Save user data locally
      await localDataSource.saveUser(mockUser);

      return mockUser.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }

    // ========================================
    // ORIGINAL IMPLEMENTATION (COMMENTED OUT)
    // ========================================
    // Uncomment this section when ready to use real API
    /*
    try {
      final user = await remoteDataSource.login(
        email: email,
        password: password,
      );

      // Save user data locally
      await localDataSource.saveUser(user);
      return user.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
    */
  }

  @override
  Future<UserEntity> register({
    required String email,
    required String password,
    required String name,
  }) async {
    // ========================================
    // TEMPORARY MOCK IMPLEMENTATION FOR TESTING
    // ========================================
    // This bypasses the actual API call to allow web platform testing
    // without requiring a real backend. Remove this mock implementation
    // when connecting to a real API endpoint.

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1000));

      // Create mock user data
      final mockUser = UserModel(
        id: 'mock_user_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        name: name,
        profilePicture:
            'https://ui-avatars.com/api/?name=${name.replaceAll(' ', '+')}&background=6366f1&color=fff',
        isEmailVerified:
            false, // New registrations typically need email verification
      );

      // Save user data locally
      await localDataSource.saveUser(mockUser);

      return mockUser.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }

    // ========================================
    // ORIGINAL IMPLEMENTATION (COMMENTED OUT)
    // ========================================
    // Uncomment this section when ready to use real API
    /*
    try {
      final user = await remoteDataSource.register(
        email: email,
        password: password,
        name: name,
      );

      // Save user data locally
      await localDataSource.saveUser(user);
      return user.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
    */
  }

  @override
  Future<void> logout() async {
    try {
      // Try to logout from remote first
      await remoteDataSource.logout();

      // Clear local data regardless of remote success
      await localDataSource.clearAll();
    } catch (e) {
      // Even if remote logout fails, try to clear local data
      try {
        await localDataSource.clearAll();
      } catch (_) {
        // Ignore local clear failures during error handling
      }

      // Re-throw the original exception
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<UserEntity?> getCurrentUser() async {
    try {
      // First try to get from local storage
      final localUser = await localDataSource.getUser();

      // If found locally, return it
      if (localUser != null) {
        return localUser.toEntity();
      }

      // If not found locally, try remote
      final remoteUser = await remoteDataSource.getCurrentUser();
      if (remoteUser != null) {
        await localDataSource.saveUser(remoteUser);
        return remoteUser.toEntity();
      }

      return null;
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      return await localDataSource.hasValidSession();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<void> refreshToken() async {
    try {
      await remoteDataSource.refreshToken();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      await remoteDataSource.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<void> verifyEmail({
    required String token,
  }) async {
    try {
      await remoteDataSource.verifyEmail(token: token);
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }



  /// Helper method to extract a display name from email address
  /// Used for mock user data generation during testing
  String _extractNameFromEmail(String email) {
    final username = email.split('@').first;
    // Convert underscores and dots to spaces, then capitalize each word
    final words = username.replaceAll(RegExp(r'[._]'), ' ').split(' ');
    return words
        .map(
          (word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
              : word,
        )
        .join(' ');
  }
}
