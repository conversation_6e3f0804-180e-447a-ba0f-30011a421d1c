import 'package:glidic_app/features/auth/domain/entities/user_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

/// Data model for User that extends the domain entity
/// This handles JSON serialization/deserialization
/// Part of the data layer in Clean Architecture
@JsonSerializable()
class UserModel extends UserEntity {
  @JsonKey(name: 'profile_picture')
  @override
  // ignore: overridden_fields
  final String? profilePicture;

  @<PERSON>sonKey(name: 'is_email_verified')
  @override
  // ignore: overridden_fields
  final bool isEmailVerified;

  const UserModel({
    required super.id,
    required super.email,
    required super.name,
    this.profilePicture,
    this.isEmailVerified = false,
  }) : super(
          profilePicture: profilePicture,
          isEmailVerified: isEmailVerified,
        );

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// Create UserModel from UserEntity
  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      id: entity.id,
      email: entity.email,
      name: entity.name,
      profilePicture: entity.profilePicture,
      isEmailVerified: entity.isEmailVerified,
    );
  }

  /// Convert to UserEntity
  UserEntity toEntity() {
    return UserEntity(
      id: id,
      email: email,
      name: name,
      profilePicture: profilePicture,
      isEmailVerified: isEmailVerified,
    );
  }

  /// Create a copy with updated fields
  @override
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? profilePicture,
    bool? isEmailVerified,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
    );
  }
}
