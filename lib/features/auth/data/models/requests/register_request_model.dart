import 'package:json_annotation/json_annotation.dart';

part 'register_request_model.g.dart';

/// Request model for user registration
/// Contains email, password, and name for creating a new account
@JsonSerializable()
class RegisterRequestModel {
  const RegisterRequestModel({
    required this.email,
    required this.password,
    required this.name,
  });

  /// User's email address
  final String email;

  /// User's password
  final String password;

  /// User's full name
  final String name;

  /// Create RegisterRequestModel from JSON
  factory RegisterRequestModel.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestModelFromJson(json);

  /// Convert RegisterRequestModel to JSON
  Map<String, dynamic> toJson() => _$RegisterRequestModelToJson(this);

  @override
  String toString() => 'RegisterRequestModel(email: $email, name: $name)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RegisterRequestModel &&
        other.email == email &&
        other.password == password &&
        other.name == name;
  }

  @override
  int get hashCode => email.hashCode ^ password.hashCode ^ name.hashCode;
}
