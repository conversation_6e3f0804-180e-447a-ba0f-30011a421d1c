import 'package:json_annotation/json_annotation.dart';

part 'forgot_password_request_model.g.dart';

/// Request model for forgot password functionality
/// Contains email for password reset
@JsonSerializable()
class ForgotPasswordRequestModel {
  const ForgotPasswordRequestModel({
    required this.email,
  });

  /// User's email address for password reset
  final String email;

  /// Create ForgotPasswordRequestModel from JSON
  factory ForgotPasswordRequestModel.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordRequestModelFromJson(json);

  /// Convert ForgotPasswordRequestModel to JSON
  Map<String, dynamic> toJson() => _$ForgotPasswordRequestModelToJson(this);

  @override
  String toString() => 'ForgotPasswordRequestModel(email: $email)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ForgotPasswordRequestModel && other.email == email;
  }

  @override
  int get hashCode => email.hashCode;
}
