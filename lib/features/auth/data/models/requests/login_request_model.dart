import 'package:json_annotation/json_annotation.dart';

part 'login_request_model.g.dart';

/// Request model for user login
/// Contains email and password for authentication
@JsonSerializable()
class LoginRequestModel {
  const LoginRequestModel({
    required this.email,
    required this.password,
  });

  /// User's email address
  final String email;

  /// User's password
  final String password;

  /// Create LoginRequestModel from JSON
  factory LoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestModelFromJson(json);

  /// Convert LoginRequestModel to JSON
  Map<String, dynamic> toJson() => _$LoginRequestModelToJson(this);

  @override
  String toString() => 'LoginRequestModel(email: $email)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginRequestModel &&
        other.email == email &&
        other.password == password;
  }

  @override
  int get hashCode => email.hashCode ^ password.hashCode;
}
