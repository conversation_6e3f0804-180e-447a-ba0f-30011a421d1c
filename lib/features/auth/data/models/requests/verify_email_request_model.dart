import 'package:json_annotation/json_annotation.dart';

part 'verify_email_request_model.g.dart';

/// Request model for email verification
/// Contains token for verifying user's email address
@JsonSerializable()
class VerifyEmailRequestModel {
  const VerifyEmailRequestModel({
    required this.token,
  });

  /// Verification token sent to user's email
  final String token;

  /// Create VerifyEmailRequestModel from JSON
  factory VerifyEmailRequestModel.fromJson(Map<String, dynamic> json) =>
      _$VerifyEmailRequestModelFromJson(json);

  /// Convert VerifyEmailRequestModel to JSON
  Map<String, dynamic> toJson() => _$VerifyEmailRequestModelToJson(this);

  @override
  String toString() =>
      'VerifyEmailRequestModel(token: ${token.substring(0, 8)}...)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VerifyEmailRequestModel && other.token == token;
  }

  @override
  int get hashCode => token.hashCode;
}
