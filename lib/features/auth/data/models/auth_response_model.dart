import 'package:glidic_app/features/auth/data/models/user_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_response_model.g.dart';

/// Response model for authentication endpoints that return user data
@JsonSerializable()
class AuthResponseModel {
  const AuthResponseModel({
    required this.user,
    this.message,
  });

  /// User data returned from authentication
  final UserModel user;

  /// Optional message from the server
  final String? message;

  /// Create AuthResponseModel from JSON
  factory AuthResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseModelFromJson(json);

  /// Convert AuthResponseModel to JSON
  Map<String, dynamic> toJson() => _$AuthResponseModelToJson(this);

  @override
  String toString() => 'AuthResponseModel(user: $user, message: $message)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResponseModel &&
        other.user == user &&
        other.message == message;
  }

  @override
  int get hashCode => user.hashCode ^ message.hashCode;
}

/// Response model for user profile endpoint
@JsonSerializable()
class UserResponseModel {
  const UserResponseModel({
    this.user,
    this.message,
  });

  /// User data (can be null if not authenticated)
  final UserModel? user;

  /// Optional message from the server
  final String? message;

  /// Create UserResponseModel from JSON
  factory UserResponseModel.fromJson(Map<String, dynamic> json) =>
      _$UserResponseModelFromJson(json);

  /// Convert UserResponseModel to JSON
  Map<String, dynamic> toJson() => _$UserResponseModelToJson(this);

  @override
  String toString() => 'UserResponseModel(user: $user, message: $message)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserResponseModel &&
        other.user == user &&
        other.message == message;
  }

  @override
  int get hashCode => user.hashCode ^ message.hashCode;
}
