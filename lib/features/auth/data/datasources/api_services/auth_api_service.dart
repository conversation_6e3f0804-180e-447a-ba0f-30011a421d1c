import 'package:dio/dio.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/features/auth/data/models/auth_response_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/forgot_password_request_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/login_request_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/register_request_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/verify_email_request_model.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_api_service.g.dart';

/// Retrofit API service for authentication endpoints
/// Provides type-safe API calls with proper request/response models
@RestApi()
abstract class AuthApiService {
  factory AuthApiService(Dio dio) = _AuthApiService;

  /// Login user with email and password
  /// Returns user data on successful authentication
  @POST(NetworkConstants.authLogin)
  Future<AuthResponseModel> login(
    @Body() LoginRequestModel request,
  );

  /// Register new user account
  /// Returns user data on successful registration
  @POST(NetworkConstants.authRegister)
  Future<AuthResponseModel> register(
    @Body() RegisterRequestModel request,
  );

  /// Logout current user
  /// Invalidates the current session/token
  @POST(NetworkConstants.authLogout)
  Future<void> logout();

  /// Get current authenticated user information
  /// Returns user data if authenticated, null otherwise
  @GET(NetworkConstants.authMe)
  Future<UserResponseModel> getCurrentUser();

  /// Refresh authentication token
  /// Extends the current session with a new token
  @POST(NetworkConstants.authRefresh)
  Future<void> refreshToken();

  /// Send password reset email
  /// Sends reset instructions to the provided email address
  @POST(NetworkConstants.authForgotPassword)
  Future<void> sendPasswordResetEmail(
    @Body() ForgotPasswordRequestModel request,
  );

  /// Verify user's email address
  /// Confirms email using the provided verification token
  @POST(NetworkConstants.authVerifyEmail)
  Future<void> verifyEmail(
    @Body() VerifyEmailRequestModel request,
  );
}
