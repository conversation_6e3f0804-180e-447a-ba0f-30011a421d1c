import 'dart:convert';

import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/core/data/datasources/storage/interfaces/preferences_service.dart';
import 'package:glidic_app/core/data/datasources/storage/interfaces/secure_storage_service.dart';
import 'package:glidic_app/features/auth/data/datasources/interfaces/auth_local_datasource.dart';
import 'package:glidic_app/features/auth/data/models/user_model.dart';

/// Implementation of [AuthLocalDataSource] using secure storage and preferences
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SecureStorageService _secureStorageService;
  final PreferencesService _preferencesService;

  AuthLocalDataSourceImpl({
    required SecureStorageService secureStorageService,
    required PreferencesService preferencesService,
  })  : _secureStorageService = secureStorageService,
        _preferencesService = preferencesService;

  @override
  Future<void> saveAccessToken(String token) {
    return _secureStorageService.store(
      StorageConstants.accessTokenKey,
      token,
    );
  }

  @override
  Future<String?> getAccessToken() {
    return _secureStorageService.retrieve(StorageConstants.accessTokenKey);
  }

  @override
  Future<void> saveRefreshToken(String token) {
    return _secureStorageService.store(
      StorageConstants.refreshTokenKey,
      token,
    );
  }

  @override
  Future<String?> getRefreshToken() {
    return _secureStorageService.retrieve(StorageConstants.refreshTokenKey);
  }

  @override
  Future<void> saveUser(UserModel user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _preferencesService.setString(
        StorageConstants.userKey,
        userJson,
      );
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<UserModel?> getUser() async {
    try {
      final userJson = await _preferencesService.getString(StorageConstants.userKey);

      if (userJson == null) {
        return null;
      }

      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return UserModel.fromJson(userMap);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<void> clearTokens() async {
    try {
      await _secureStorageService.delete(StorageConstants.accessTokenKey);
      await _secureStorageService.delete(StorageConstants.refreshTokenKey);
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<void> clearUser() {
    return _preferencesService.remove(StorageConstants.userKey);
  }

  @override
  Future<void> clearAll() async {
    try {
      await clearTokens();
      await clearUser();
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<bool> hasValidSession() async {
    try {
      final token = await getAccessToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      if (e is AppFailure) rethrow;
      throw ExceptionHandler.handle(e);
    }
  }
}
