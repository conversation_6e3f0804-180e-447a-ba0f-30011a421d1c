import 'package:glidic_app/features/auth/data/datasources/api_services/auth_api_service.dart';
import 'package:glidic_app/features/auth/data/models/requests/forgot_password_request_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/login_request_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/register_request_model.dart';
import 'package:glidic_app/features/auth/data/models/requests/verify_email_request_model.dart';
import 'package:glidic_app/features/auth/data/models/user_model.dart';

/// Abstract interface for remote authentication data source
abstract class AuthRemoteDataSource {
  Future<UserModel> login({
    required String email,
    required String password,
  });

  Future<UserModel> register({
    required String email,
    required String password,
    required String name,
  });

  Future<void> logout();

  Future<UserModel?> getCurrentUser();

  Future<void> refreshToken();

  Future<void> sendPasswordResetEmail({
    required String email,
  });

  Future<void> verifyEmail({
    required String token,
  });
}

/// Implementation of remote authentication data source
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  const AuthRemoteDataSourceImpl(this._authApiService);

  final AuthApiService _authApiService;

  @override
  Future<UserModel> login({
    required String email,
    required String password,
  }) async {
    try {
      final request = LoginRequestModel(
        email: email,
        password: password,
      );

      final response = await _authApiService.login(request);
      return response.user;
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> register({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      final request = RegisterRequestModel(
        email: email,
        password: password,
        name: name,
      );

      final response = await _authApiService.register(request);
      return response.user;
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    try {
      await _authApiService.logout();
    } catch (e) {
      throw Exception('Logout failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final response = await _authApiService.getCurrentUser();
      return response.user;
    } catch (e) {
      throw Exception('Failed to get current user: ${e.toString()}');
    }
  }

  @override
  Future<void> refreshToken() async {
    try {
      await _authApiService.refreshToken();
    } catch (e) {
      throw Exception('Token refresh failed: ${e.toString()}');
    }
  }

  @override
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      final request = ForgotPasswordRequestModel(email: email);
      await _authApiService.sendPasswordResetEmail(request);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }

  @override
  Future<void> verifyEmail({
    required String token,
  }) async {
    try {
      final request = VerifyEmailRequestModel(token: token);
      await _authApiService.verifyEmail(request);
    } catch (e) {
      throw Exception('Email verification failed: ${e.toString()}');
    }
  }
}
