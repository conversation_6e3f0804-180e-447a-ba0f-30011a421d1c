import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/auth/data/models/user_model.dart';

/// Abstract interface for local authentication data source
/// Handles local storage of authentication data (tokens, user info, etc.)
///
/// All methods throw [AppFailure] or its subclasses on error:
/// - [StorageFailure] for storage-related errors
/// - [ValidationFailure] for invalid input data
abstract class AuthLocalDataSource {
  /// Save user data to local storage
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if user data is invalid
  Future<void> saveUser(UserModel user);

  /// Retrieve user data from local storage
  /// Returns null if no user data is stored
  /// Throws [StorageFailure] if storage operation fails
  Future<UserModel?> getUser();

  /// Clear user data from local storage
  /// Throws [StorageFailure] if storage operation fails
  Future<void> clearUser();

  /// Save access token to secure storage
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if token is invalid
  Future<void> saveAccessToken(String token);

  /// Retrieve access token from secure storage
  /// Returns null if no token is stored
  /// Throws [StorageFailure] if storage operation fails
  Future<String?> getAccessToken();

  /// Save refresh token to secure storage
  /// Throws [StorageFailure] if storage operation fails
  /// Throws [ValidationFailure] if token is invalid
  Future<void> saveRefreshToken(String token);

  /// Retrieve refresh token from secure storage
  /// Returns null if no token is stored
  /// Throws [StorageFailure] if storage operation fails
  Future<String?> getRefreshToken();

  /// Clear all tokens from secure storage
  /// Throws [StorageFailure] if storage operation fails
  Future<void> clearTokens();

  /// Clear all authentication data (user data and tokens)
  /// Throws [StorageFailure] if storage operation fails
  Future<void> clearAll();

  /// Check if there is a valid authentication session
  /// Returns true if valid access token exists
  /// Throws [StorageFailure] if storage operation fails
  Future<bool> hasValidSession();
}
