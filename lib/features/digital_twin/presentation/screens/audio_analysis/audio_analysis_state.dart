part of 'audio_analysis_cubit.dart';

class AudioAnalysisState extends Equatable {
  final String audioId;
  final AppFailure? failure;
  final RecordItem? recordItem;
  final bool isPlaying;
  final Duration totalDuration;
  final TranscriptionAudioEntity? transcriptionAudioEntity;
  final bool isTranscriptionLoading;
  final int? _currentTranscriptionIndex;
  final int selectedTabIndex;
  final AudioPlayRateMode playRateMode;

  /// Returns the current transcription index only when audio is playing
  int? get currentTranscriptionIndex {
    if (!isPlaying) {
      return null;
    }
    return _currentTranscriptionIndex;
  }

  const AudioAnalysisState({
    required this.audioId,
    this.failure,
    this.recordItem,
    this.isPlaying = false,
    this.totalDuration = Duration.zero,
    int? currentTranscriptionIndex,
    this.transcriptionAudioEntity,
    this.isTranscriptionLoading = false,
    this.selectedTabIndex = 0,
    this.playRateMode = AudioPlayRateMode.x1,
  }) : _currentTranscriptionIndex = currentTranscriptionIndex;

  AudioAnalysisState copyWith({
    String? audioId,
    AppFailure? failure,
    RecordItem? recordItem,
    bool? isPlaying,
    bool? isLoading,
    Duration? totalDuration,
    TranscriptionAudioEntity? transcriptionAudioEntity,
    bool? isTranscriptionLoading,
    int? currentTranscriptionIndex,
    int? selectedTabIndex,
    AudioPlayRateMode? playRateMode,
  }) =>
      AudioAnalysisState(
        audioId: audioId ?? this.audioId,
        failure: failure ?? this.failure,
        recordItem: recordItem ?? this.recordItem,
        isPlaying: isPlaying ?? this.isPlaying,
        totalDuration: totalDuration ?? this.totalDuration,
        transcriptionAudioEntity: transcriptionAudioEntity ?? this.transcriptionAudioEntity,
        isTranscriptionLoading:
            isTranscriptionLoading ?? this.isTranscriptionLoading,
        currentTranscriptionIndex:
            currentTranscriptionIndex ?? _currentTranscriptionIndex,
        selectedTabIndex: selectedTabIndex ?? this.selectedTabIndex,
        playRateMode: playRateMode ?? this.playRateMode,
      );

  @override
  List<Object?> get props => [
        audioId,
        failure,
        recordItem,
        isPlaying,
        totalDuration,
        transcriptionAudioEntity,
        isTranscriptionLoading,
        currentTranscriptionIndex,
        selectedTabIndex,
        playRateMode,
      ];
}
