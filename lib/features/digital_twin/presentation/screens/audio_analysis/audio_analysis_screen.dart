import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/audio_analysis_cubit.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/widgets/analysis_audio_controller_widget.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/widgets/analysis_audio_tab.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/widgets/audio_analysis_app_bar.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/widgets/audio_summary.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/widgets/audio_transcript.dart';

@RoutePage()
class AudioAnalysisScreen extends StatelessWidget implements AutoRouteWrapper {
  const AudioAnalysisScreen({super.key, required this.audioId});

  final String audioId;

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => AudioAnalysisCubit(
        audioId: audioId,
        getRecordingUseCase: sl(),
        getTranscriptionAudioUseCase: sl(),
      )..init(),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AudioAnalysisCubit, AudioAnalysisState>(
      listenWhen: (previous, current) =>  
          previous.failure != current.failure,
      listener: (context, state) {
        if (state.failure != null) {
          _showErrorSnackBar(context, state.failure!.message);
        }
      },
      child: Scaffold(
        backgroundColor: ColorConstants.backgroundColor,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: Dimensions.defaultPadding,
            ),
            child: Column(
              children: [
                const AudioAnalysisAppBar(),
                const SizedBox(height: Dimensions.gapLg),
                const AnalysisAudioControllerWidget(),
                const SizedBox(height: Dimensions.gapLg),
                const AnalysisAudioTab(),
                const SizedBox(height: Dimensions.gapLg),
                BlocBuilder<AudioAnalysisCubit, AudioAnalysisState>(
                  buildWhen: (previous, current) =>
                      previous.selectedTabIndex != current.selectedTabIndex,
                  builder: (context, state) {
                    return Expanded(
                      child: IndexedStack(
                        index: state.selectedTabIndex,
                        children: const [
                          AudioTranscript(),
                          AudioSummary(),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
