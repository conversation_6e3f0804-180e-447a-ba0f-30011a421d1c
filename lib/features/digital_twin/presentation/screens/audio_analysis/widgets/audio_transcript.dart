import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/dimensions.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/audio_analysis_cubit.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/widgets/transcription_item_widget.dart';

class AudioTranscript extends StatelessWidget {
  const AudioTranscript({super.key});

  void _onTapTranscriptionItem(BuildContext context, Duration duration) {
    context.read<AudioAnalysisCubit>().seekTo(duration);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AudioAnalysisCubit, AudioAnalysisState>(
      buildWhen: _shouldRebuildTranscriptionItem,
      builder: (context, state) {
        if (state.isTranscriptionLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return ListView.separated(
          itemCount: state.transcriptionAudioEntity?.transcriptions.length ?? 0,
          separatorBuilder: (context, index) =>
              const SizedBox(height: Dimensions.gapXL),
          itemBuilder: (context, index) {
            final transcription = state.transcriptionAudioEntity!.transcriptions[index];
            return TranscriptionItemWidget(
              transcription: transcription,
              duration: Duration(seconds: transcription.startTime),
              color: Color(state.transcriptionAudioEntity!.getColorSpeaker(transcription.speaker)),
              isPlaying: state.currentTranscriptionIndex == index,
              onTap: () => _onTapTranscriptionItem(
                context,
                Duration(seconds: transcription.startTime),
              ),
            );
          },
        );
      },
    );
  }

  bool _shouldRebuildTranscriptionItem(
    AudioAnalysisState previous,
    AudioAnalysisState current,
  ) {
    return previous.isTranscriptionLoading != current.isTranscriptionLoading ||
        previous.transcriptionAudioEntity != current.transcriptionAudioEntity ||
        previous.currentTranscriptionIndex != current.currentTranscriptionIndex;
  }
}
