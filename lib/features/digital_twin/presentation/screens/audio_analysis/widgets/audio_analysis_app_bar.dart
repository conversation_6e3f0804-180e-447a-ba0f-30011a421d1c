import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';
import 'package:glidic_app/core/presentation/widgets/gd_audio_file_waveforms.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/audio_analysis_cubit.dart';

const Color _kGrayTranscriptionColor = Color(0xFFE6E9EC);
const double _kTranscriptBtnSize = 34;
const double _kPlayerWaveformsHeight = 33;

class AudioAnalysisAppBar extends StatelessWidget {
  const AudioAnalysisAppBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            context.router.maybePop();
          },
          child: Container(
            width: _kTranscriptBtnSize,
            alignment: Alignment.centerLeft,
            child: SvgPicture.asset(
              PathConstants.backIcon,
            ),
          ),
        ),
        const SizedBox(width: Dimensions.gapMd),
        StreamBuilder<int>(
          stream: context.read<AudioAnalysisCubit>().playbackPositionStream,
          initialData: 0,
          builder: (context, snapshot) {
            final duration = Duration(milliseconds: snapshot.data ?? 0);

            return Text(
              duration.toFormattedString(
                isShowFull: true,
                format: DurationFormatConfig.digital,
              ),
              style: TextStyleConstants.labelExtraSmall.w700,
            );
          },
        ),
        const SizedBox(width: Dimensions.gapXS),
        Expanded(
          child: Container(
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: _kGrayTranscriptionColor,
              borderRadius: BorderRadius.circular(Dimensions.radiusLg),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                return GDAudioFileWaveforms(
                  playerController:
                      context.read<AudioAnalysisCubit>().playerController,
                  size: Size(constraints.maxWidth, _kPlayerWaveformsHeight),
                  playerWaveStyle: const GDPlayerWaveStyle(),
                );
              },
            ),
          ),
        ),
        const SizedBox(width: Dimensions.gapXS),
        BlocBuilder<AudioAnalysisCubit, AudioAnalysisState>(
          builder: (context, state) {
            return Text(
              state.totalDuration.toFormattedString(
                isShowFull: true,
                format: DurationFormatConfig.digital,
              ),
              style: TextStyleConstants.labelExtraSmall.w700,
            );
          },
        ),
        const SizedBox(width: Dimensions.gapMd),
        GestureDetector(
          onTap: () {
            // TODO: Implement share
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Not implemented'),
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(Dimensions.radiusLg),
                ),
              ),
            );
          },
          child: Container(
            width: _kTranscriptBtnSize,
            alignment: Alignment.centerRight,
            child: SvgPicture.asset(
              PathConstants.shareIcon,
            ),
          ),
        ),
      ],
    );
  }
}
