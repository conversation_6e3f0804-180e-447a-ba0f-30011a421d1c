import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_item_entity.dart';

class TranscriptionItemWidget extends StatelessWidget {
  const TranscriptionItemWidget({
    super.key,
    required this.transcription,
    required this.duration,
    required this.color,
    this.isPlaying = false,
    this.onTap,
  });

  final TranscriptionItemEntity transcription;
  final Color color;
  final Duration duration;
  final bool isPlaying;
  final VoidCallback? onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap?.call(),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: Dimensions.defaultPadding,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SvgPicture.asset(
                  PathConstants.personPinIcon,
                  colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
                ),
                const SizedBox(width: Dimensions.gapXS),
                Text(
                  transcription.speaker,
                  style: TextStyleConstants.label.copyWith(
                    color: color,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(width: Dimensions.gapMd),
                Text(
                  duration.toFormattedString(
                    isShowFull: true,
                    format: DurationFormatConfig.digital,
                  ),
                  style: TextStyleConstants.label.copyWith(
                    color: ColorConstants.textSecondary,
                  ),
                ),
                if (isPlaying) ...[
                  const SizedBox(width: Dimensions.gapXS),
                  SvgPicture.asset(
                    PathConstants.voiceAlertIcon,
                  ),
                ],
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(
                left: Dimensions.gapMd,
              ),
              child: Text(
                transcription.text,
                style: TextStyleConstants.label.copyWith(
                  color: isPlaying
                      ? ColorConstants.tertiaryColor
                      : ColorConstants.textPrimary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 