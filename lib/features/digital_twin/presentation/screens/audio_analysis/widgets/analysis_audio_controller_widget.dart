import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/circle_icon_button.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/rounded_icon_button.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/audio_analysis_cubit.dart';

const int _kTimeSeek = 15; // 15 seconds
const double _kPlayingIconSize = 18;

class AnalysisAudioControllerWidget extends StatelessWidget {
  const AnalysisAudioControllerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    const space = SizedBox(width: Dimensions.gapXL);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(),
        Bloc<PERSON>uilder<AudioAnalysisCubit, AudioAnalysisState>(
          buildWhen: (previous, current) =>
              previous.playRateMode != current.playRateMode,
          builder: (context, state) {
            return RoundedIconButton(
              icon: Text(
                state.playRateMode.label,
                style: TextStyleConstants.labelSmall.w700,
              ),
              onPressed: () => _onSpeedChange(context),
            );
          },
        ),
        space,
        RoundedIconButton(
          icon: SvgPicture.asset(
            PathConstants.rewind15sIcon,
            fit: BoxFit.contain,
          ),
          onPressed: () => _onSeek(context, -_kTimeSeek),
        ),
        space,
        BlocBuilder<AudioAnalysisCubit, AudioAnalysisState>(
          builder: (context, state) {
            return CircleIconButton(
              backgroundColor: ColorConstants.onSecondary,
              icon: state.isPlaying == true
                  ? SvgPicture.asset(
                      PathConstants.pauseIcon,
                      width: _kPlayingIconSize,
                      height: _kPlayingIconSize,
                      fit: BoxFit.contain,
                      colorFilter: const ColorFilter.mode(
                        ColorConstants.surfaceColor,
                        BlendMode.srcIn,
                      ),
                    )
                  : SvgPicture.asset(
                      PathConstants.playIcon,
                      fit: BoxFit.contain,
                    ),
              onPressed: () => state.isPlaying == true
                  ? _onPause(context)
                  : _onPlay(context),
            );
          },
        ),
        space,
        RoundedIconButton(
          icon: SvgPicture.asset(
            PathConstants.forward15sIcon,
            fit: BoxFit.contain,
          ),
          onPressed: () => _onSeek(context, _kTimeSeek),
        ),
        space,
        RoundedIconButton(
          icon: SvgPicture.asset(
            PathConstants.scissorsIcon,
            fit: BoxFit.contain,
          ),
          onPressed: () => _onEdit(context),
        ),
        Expanded(
          child: Align(
            alignment: Alignment.centerRight,
            child: GestureDetector(
              // TODO: implement this
              onTap: () => _showSnackBarNotImplemented(context),
              child: SvgPicture.asset(
                PathConstants.dotsHorizontalIcon,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onSpeedChange(BuildContext context) {
    final currentPlayRateMode =
        context.read<AudioAnalysisCubit>().state.playRateMode;

    context.read<AudioAnalysisCubit>().changePlayRateMode(
          currentPlayRateMode.next,
        );
  }

  void _onSeek(BuildContext context, int seconds) {
    context.read<AudioAnalysisCubit>().seekRelative(seconds);
  }

  void _onEdit(BuildContext context) {
    _showSnackBarNotImplemented(context);
  }

  void _onPlay(BuildContext context) {
    context.read<AudioAnalysisCubit>().playAudio();
  }

  void _onPause(BuildContext context) {
    context.read<AudioAnalysisCubit>().pauseAudio();
  }

  // TODO: remove this method
  void _showSnackBarNotImplemented(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Not implemented'),
      ),
    );
  }
}
