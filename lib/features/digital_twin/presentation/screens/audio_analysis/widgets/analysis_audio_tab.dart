import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/generated/l10n/app_localizations.dart';
import 'package:glidic_app/features/digital_twin/presentation/screens/audio_analysis/audio_analysis_cubit.dart';

const Color _kGrayTranscriptionColor = Color(0xFFE6E9EC);
const double _kSegmentedControlHeight = 40;

class AnalysisAudioTab extends StatelessWidget {
  const AnalysisAudioTab({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _kSegmentedControlHeight,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => context.read<AudioAnalysisCubit>().changeSelectedTabIndex(0),
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: ColorConstants.onSecondary,
                  borderRadius: BorderRadius.circular(Dimensions.radiusMd),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      PathConstants.messageIcon,
                      colorFilter: const ColorFilter.mode(
                        ColorConstants.surfaceColor,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: Dimensions.gapXS),
                    Text(
                      AppLocalizations.of(context).transcription,
                      style: TextStyleConstants.label.onPrimaryColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: Dimensions.gapLg),
          Expanded(
            child: GestureDetector(
              onTap: () => context.read<AudioAnalysisCubit>().changeSelectedTabIndex(1),
              child: Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: _kGrayTranscriptionColor,
                  borderRadius: BorderRadius.circular(Dimensions.radiusMd),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      PathConstants.menuIcon,
                      colorFilter: const ColorFilter.mode(
                        ColorConstants.onSecondary,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: Dimensions.gapXS),
                    Text(
                      AppLocalizations.of(context).aiSummary,
                      style: TextStyleConstants.label.onSecondaryColor,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }


} 