import 'dart:async';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/common/errors/exception_handler.dart';
import 'package:glidic_app/features/recording/domain/entities/record_item.dart';
import 'package:glidic_app/features/recording/domain/entities/transcription_audio_entity.dart';
import 'package:glidic_app/features/recording/domain/enum/audio_play_rate_mode.dart';
import 'package:glidic_app/features/recording/domain/usecases/get_recording_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/get_transcription_audio_usecase.dart';

part 'audio_analysis_state.dart';

class AudioAnalysisCubit extends Cubit<AudioAnalysisState> {
  // Use cases
  final GetRecordingUseCase _getRecordingUseCase;
  final GetTranscriptionAudioUseCase _getTranscriptionAudioUseCase;

  // Audio player
  final PlayerController _playerController = PlayerController();
  PlayerController get playerController => _playerController;

  // Duration tracking
  Duration _totalDuration = Duration.zero;
  final StreamController<int> _playbackPositionController =
      StreamController<int>.broadcast();
  Stream<int> get playbackPositionStream => _playbackPositionController.stream;

  // Subscriptions
  late StreamSubscription _playbackPositionSubscription;
  late StreamSubscription _playerStateSubscription;

  AudioAnalysisCubit({
    required GetRecordingUseCase getRecordingUseCase,
    required GetTranscriptionAudioUseCase getTranscriptionAudioUseCase,
    required String audioId,
  })  : _getRecordingUseCase = getRecordingUseCase,
        _getTranscriptionAudioUseCase = getTranscriptionAudioUseCase,
        super(AudioAnalysisState(audioId: audioId));

  /// Initializes the audio analysis by loading recording data and setting up player
  Future<void> init() async {
    emit(state.copyWith(isLoading: true));

    try {
      final recordItem = await _getRecordingUseCase.execute(state.audioId);
      await _initializePlayer(recordItem);
      _setupPlayerSubscriptions();

      emit(
        state.copyWith(
          recordItem: recordItem,
          isPlaying: false,
          totalDuration: _totalDuration,
          isLoading: false,
        ),
      );

      await _loadTranscription();
    } catch (e) {
      final failure = ExceptionHandler.handle(e);
      emit(
        state.copyWith(
          failure: failure,
          isLoading: false,
          isTranscriptionLoading: false,
        ),
      );
    }
  }

  /// Initializes the audio player with the recording file
  Future<void> _initializePlayer(RecordItem recordItem) async {
    await _playerController.preparePlayer(path: recordItem.filePath);

    final maxDurationMs = await _playerController.getDuration(
      DurationType.max,
    );
    _totalDuration = Duration(milliseconds: maxDurationMs);
  }

  /// Sets up subscriptions for player state and playback position changes
  void _setupPlayerSubscriptions() {
    _setupPlayerStateSubscription();
    _setupPlaybackPositionSubscription();
  }

  /// Sets up subscription for player state changes (playing/paused)
  void _setupPlayerStateSubscription() {
    _playerStateSubscription = _playerController.onPlayerStateChanged.listen(
      (playerState) {
        final isPlaying = playerState == PlayerState.playing;
        emit(state.copyWith(isPlaying: isPlaying));
      },
    );
  }

  /// Sets up subscription for playback position changes and transcription tracking
  void _setupPlaybackPositionSubscription() {
    _playbackPositionSubscription =
        _playerController.onCurrentDurationChanged.listen((positionMs) {
      _playbackPositionController.add(positionMs);
      _updateTranscriptionPosition(positionMs);
    });
  }

  /// Updates the current transcription index based on playback position
  void _updateTranscriptionPosition(int positionMs) {
    if (_playerController.playerState == PlayerState.playing &&
        state.transcriptionAudioEntity != null) {
      final positionSeconds = _millisecondsToSeconds(positionMs);
      final transcriptionIndex = state.transcriptionAudioEntity!
          .findTranscriptionIndexByTime(positionSeconds);
      emit(state.copyWith(currentTranscriptionIndex: transcriptionIndex));
    }
  }

  /// Starts audio playback
  Future<void> playAudio() async {
    await _playerController.startPlayer();
    _playerController.setFinishMode(finishMode: FinishMode.pause);
  }

  /// Pauses audio playback
  Future<void> pauseAudio() async {
    await _playerController.pausePlayer();
  }

  /// Seeks the audio playback by the specified number of seconds from the current position
  Future<void> seekRelative(int seconds) async {
    final currentMs = await _playerController.getDuration(
      DurationType.current,
    );
    final newMs = (currentMs + seconds * Constants.millisecondsPerSecond)
        .clamp(0, _totalDuration.inMilliseconds);
    await _playerController.seekTo(newMs);
  }

  /// Seeks the audio playback to the specified absolute duration
  Future<void> seekTo(Duration duration) async {
    final targetMs =
        duration.inMilliseconds.clamp(0, _totalDuration.inMilliseconds);
    await _playerController.seekTo(targetMs);
  }

  /// Changes the selected tab index (transcription/summary)
  void changeSelectedTabIndex(int index) {
    emit(state.copyWith(selectedTabIndex: index));
  }

  /// Changes the play rate mode
  void changePlayRateMode(AudioPlayRateMode playRateMode) {
    emit(state.copyWith(playRateMode: playRateMode));
    _playerController.setRate(playRateMode.value);
  }

  /// Loads transcription data for the audio
  Future<void> _loadTranscription() async {
    emit(state.copyWith(isTranscriptionLoading: true));
    try {
      final transcription =
          await _getTranscriptionAudioUseCase.execute(state.audioId);
      emit(
        state.copyWith(
          transcriptionAudioEntity: transcription,
          isTranscriptionLoading: false,
        ),
      );
    } catch (e) {
      final failure = ExceptionHandler.handle(e);
      emit(
        state.copyWith(
          failure: failure,
          isTranscriptionLoading: false,
        ),
      );
    }
  }

  /// Converts milliseconds to seconds
  int _millisecondsToSeconds(int milliseconds) {
    return milliseconds ~/ Constants.millisecondsPerSecond;
  }

  @override
  Future<void> close() async {
    await _playbackPositionController.close();
    await _playbackPositionSubscription.cancel();
    await _playerStateSubscription.cancel();
    _playerController.dispose();
    return super.close();
  }
}
