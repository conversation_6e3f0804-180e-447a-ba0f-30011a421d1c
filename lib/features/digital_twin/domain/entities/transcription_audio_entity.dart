  import 'dart:ui';

import 'package:equatable/equatable.dart';

class TranscriptionAudioEntity extends Equatable {
  final String id;
  final String title;
  final Color color;
  final Duration duration;
  final String content;

  const TranscriptionAudioEntity({
    required this.id,
    required this.title,
    required this.color,
    required this.duration,
    required this.content,
  });
  
  @override
  List<Object?> get props => [id, title, color, duration, content];
}