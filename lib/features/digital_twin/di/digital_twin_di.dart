import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/recording/domain/usecases/get_recording_usecase.dart';
import 'package:glidic_app/features/recording/domain/usecases/get_transcription_audio_usecase.dart';

class DigitalTwinDI {
  static Future<void> init() async {
    sl.registerFactory<GetRecordingUseCase>(
      () => GetRecordingUseCase( recordsRepository: sl()),
    );

    sl.registerFactory<GetTranscriptionAudioUseCase>(
      () => GetTranscriptionAudioUseCase(transcriptionRepository: sl()),
    );
  }
}