import 'package:flutter/material.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_info_entity.dart';

/// Dialog for selecting a calendar from available options
class CalendarSelectionDialog extends StatefulWidget {
  const CalendarSelectionDialog({
    super.key,
    required this.calendars,
    this.selectedCalendarId,
    this.title = 'Select Calendar',
  });

  /// List of available calendars
  final List<CalendarInfoEntity> calendars;

  /// Currently selected calendar ID (for default selection)
  final String? selectedCalendarId;

  /// Dialog title
  final String title;

  @override
  State<CalendarSelectionDialog> createState() => _CalendarSelectionDialogState();

  /// Show the calendar selection dialog
  static Future<CalendarInfoEntity?> show(
    BuildContext context, {
    required List<CalendarInfoEntity> calendars,
    String? selectedCalendarId,
    String title = 'Select Calendar',
  }) {
    return showDialog<CalendarInfoEntity>(
      context: context,
      barrierDismissible: false,
      builder: (context) => CalendarSelectionDialog(
        calendars: calendars,
        selectedCalendarId: selectedCalendarId,
        title: title,
      ),
    );
  }
}

class _CalendarSelectionDialogState extends State<CalendarSelectionDialog> {
  String? _selectedCalendarId;

  @override
  void initState() {
    super.initState();
    _selectedCalendarId = widget.selectedCalendarId ?? _getDefaultSelection();
  }

  /// Get default calendar selection (prefer Google Calendar)
  String? _getDefaultSelection() {
    if (widget.calendars.isEmpty) return null;

    // First, try to find a Google Calendar
    final googleCalendars = widget.calendars.where((cal) => cal.isGoogleCalendar).toList();
    if (googleCalendars.isNotEmpty) {
      return googleCalendars.first.id;
    }

    // Fallback to first writable calendar
    final writableCalendars = widget.calendars.where((cal) => !cal.isReadOnly).toList();
    if (writableCalendars.isNotEmpty) {
      return writableCalendars.first.id;
    }

    // Last resort: first calendar
    return widget.calendars.first.id;
  }

  /// Get selected calendar entity
  CalendarInfoEntity? get _selectedCalendar {
    if (_selectedCalendarId == null) return null;
    try {
      return widget.calendars.firstWhere((cal) => cal.id == _selectedCalendarId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.calendars.isEmpty)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'No writable calendars found on this device.',
                  textAlign: TextAlign.center,
                ),
              )
            else ...[
              const Text(
                'Choose which calendar to save your event to:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    children: widget.calendars.map((calendar) {
                      return _CalendarTile(
                        calendar: calendar,
                        isSelected: calendar.id == _selectedCalendarId,
                        onTap: () {
                          setState(() {
                            _selectedCalendarId = calendar.id;
                          });
                        },
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(null),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: widget.calendars.isEmpty || _selectedCalendar == null
              ? null
              : () => Navigator.of(context).pop(_selectedCalendar),
          child: const Text('Select'),
        ),
      ],
    );
  }
}

/// Individual calendar tile widget
class _CalendarTile extends StatelessWidget {
  const _CalendarTile({
    required this.calendar,
    required this.isSelected,
    required this.onTap,
  });

  final CalendarInfoEntity calendar;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isSelected ? 2 : 1,
      color: isSelected ? colorScheme.primaryContainer : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Selection indicator
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? colorScheme.primary : colorScheme.outline,
              ),
              const SizedBox(width: 12),
              
              // Calendar icon
              _CalendarIcon(calendar: calendar),
              const SizedBox(width: 12),
              
              // Calendar info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            calendar.displayName,
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            ),
                          ),
                        ),
                        if (calendar.isGoogleCalendar)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'Recommended',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    Text(
                      calendar.accountDisplayName,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      calendar.calendarType.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Calendar icon widget
class _CalendarIcon extends StatelessWidget {
  const _CalendarIcon({required this.calendar});

  final CalendarInfoEntity calendar;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    IconData iconData;
    Color iconColor;

    switch (calendar.calendarType) {
      case CalendarType.google:
        iconData = Icons.event;
        iconColor = Colors.blue;
        break;
      case CalendarType.local:
        iconData = Icons.phone_android;
        iconColor = colorScheme.primary;
        break;
      case CalendarType.other:
        iconData = Icons.account_circle;
        iconColor = colorScheme.secondary;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        size: 20,
        color: iconColor,
      ),
    );
  }
}
