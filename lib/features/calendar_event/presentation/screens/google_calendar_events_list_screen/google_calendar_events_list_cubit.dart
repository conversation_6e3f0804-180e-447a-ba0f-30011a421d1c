import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/authenticate_google_calendar_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/get_google_calendar_events_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/update_google_calendar_event_usecase.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_events_list_screen/google_calendar_events_list_state.dart';

/// Google Calendar events list cubit
class GoogleCalendarEventsListCubit
    extends Cubit<GoogleCalendarEventsListState> {
  GoogleCalendarEventsListCubit({
    required this.authenticateGoogleCalendarUseCase,
    required this.checkGoogleCalendarAuthUseCase,
    required this.getGoogleCalendarEventsUseCase,
    required this.deleteGoogleCalendarEventUseCase,
    required this.signOutGoogleCalendarUseCase,
  }) : super(const GoogleCalendarEventsListInitial());

  final AuthenticateGoogleCalendarUseCase authenticateGoogleCalendarUseCase;
  final CheckGoogleCalendarAuthUseCase checkGoogleCalendarAuthUseCase;
  final GetGoogleCalendarEventsUseCase getGoogleCalendarEventsUseCase;
  final DeleteGoogleCalendarEventUseCase deleteGoogleCalendarEventUseCase;
  final SignOutGoogleCalendarUseCase signOutGoogleCalendarUseCase;

  /// Initialize the screen by checking authentication and loading events
  Future<void> initialize() async {
    emit(const GoogleCalendarEventsListLoading());

    try {
      // Check if user is authenticated
      final isAuthenticated = await checkGoogleCalendarAuthUseCase.execute();

      if (!isAuthenticated) {
        emit(const GoogleCalendarEventsListAuthRequired());
        return;
      }

      // Load events
      await _loadEvents();
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(GoogleCalendarEventsListError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventsListError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Authenticate with Google Calendar
  Future<void> authenticate() async {
    emit(const GoogleCalendarEventsListAuthenticating());

    try {
      final success = await authenticateGoogleCalendarUseCase.execute();

      if (success) {
        // After successful authentication, load events
        await _loadEvents();
      } else {
        emit(
          const GoogleCalendarEventsListError(
            failure: CalendarFailure('Authentication was cancelled by user'),
            canRetry: true,
          ),
        );
      }
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(GoogleCalendarEventsListError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventsListError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Load events from Google Calendar
  Future<void> loadEvents() async {
    await _loadEvents();
  }

  /// Refresh events from Google Calendar
  Future<void> refreshEvents() async {
    final currentState = state;
    if (currentState is GoogleCalendarEventsListLoaded) {
      emit(currentState.copyWith(isRefreshing: true));
    }

    await _loadEvents();
  }

  /// Delete an event from Google Calendar
  Future<void> deleteEvent(String eventId, String eventTitle) async {
    final currentState = state;
    if (currentState is! GoogleCalendarEventsListLoaded) return;

    emit(
      GoogleCalendarEventsListDeleting(
        events: currentState.events,
        deletingEventId: eventId,
      ),
    );

    try {
      final success = await deleteGoogleCalendarEventUseCase.execute(
        DeleteGoogleCalendarEventParams(eventId: eventId),
      );

      if (success) {
        // Remove the deleted event from the list
        final updatedEvents = currentState.events
            .where((event) => event.eventId != eventId)
            .toList();

        emit(
          GoogleCalendarEventsListEventDeleted(
            events: updatedEvents,
            deletedEventTitle: eventTitle,
          ),
        );

        // After a short delay, update to loaded state
        await Future.delayed(const Duration(seconds: 1));

        if (updatedEvents.isEmpty) {
          emit(const GoogleCalendarEventsListEmpty());
        } else {
          emit(GoogleCalendarEventsListLoaded(events: updatedEvents));
        }
      } else {
        emit(GoogleCalendarEventsListLoaded(events: currentState.events));
        emit(
          const GoogleCalendarEventsListError(
            failure: CalendarFailure('Failed to delete event'),
            canRetry: true,
          ),
        );
      }
    } catch (e) {
      emit(GoogleCalendarEventsListLoaded(events: currentState.events));

      if (e is AppFailure) {
        emit(GoogleCalendarEventsListError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventsListError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Sign out from Google Calendar
  Future<void> signOut() async {
    try {
      await signOutGoogleCalendarUseCase.execute();
      emit(const GoogleCalendarEventsListAuthRequired());
    } catch (e) {
      // Even if sign out fails, show auth required state
      emit(const GoogleCalendarEventsListAuthRequired());
    }
  }

  /// Private method to load events
  Future<void> _loadEvents() async {
    try {
      final events = await getGoogleCalendarEventsUseCase.execute(
        const GetGoogleCalendarEventsParams(),
      );

      if (events.isEmpty) {
        emit(const GoogleCalendarEventsListEmpty());
      } else {
        emit(GoogleCalendarEventsListLoaded(events: events));
      }
    } catch (e) {
      if (e is AppFailure) {
        emit(GoogleCalendarEventsListError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventsListError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }
}
