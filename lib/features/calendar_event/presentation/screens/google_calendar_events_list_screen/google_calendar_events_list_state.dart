import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';

/// Base state for Google Calendar events list screen
abstract class GoogleCalendarEventsListState extends Equatable {
  const GoogleCalendarEventsListState();

  @override
  List<Object?> get props => [];
}

/// Initial state when the screen is first loaded
class GoogleCalendarEventsListInitial extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListInitial();
}

/// Loading state during authentication or events retrieval
class GoogleCalendarEventsListLoading extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListLoading();
}

/// State when authentication is required
class GoogleCalendarEventsListAuthRequired extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListAuthRequired();
}

/// State when authentication is in progress
class GoogleCalendarEventsListAuthenticating extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListAuthenticating();
}

/// State when events are successfully loaded
class GoogleCalendarEventsListLoaded extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListLoaded({
    required this.events,
    this.isRefreshing = false,
  });

  final List<GoogleCalendarEventEntity> events;
  final bool isRefreshing;

  @override
  List<Object?> get props => [events, isRefreshing];

  GoogleCalendarEventsListLoaded copyWith({
    List<GoogleCalendarEventEntity>? events,
    bool? isRefreshing,
  }) {
    return GoogleCalendarEventsListLoaded(
      events: events ?? this.events,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

/// State when no events are found
class GoogleCalendarEventsListEmpty extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListEmpty();
}

/// Error state with failure information
class GoogleCalendarEventsListError extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListError({
    required this.failure,
    this.canRetry = true,
  });

  final AppFailure failure;
  final bool canRetry;

  @override
  List<Object?> get props => [failure, canRetry];
}

/// State when an event is being deleted
class GoogleCalendarEventsListDeleting extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListDeleting({
    required this.events,
    required this.deletingEventId,
  });

  final List<GoogleCalendarEventEntity> events;
  final String deletingEventId;

  @override
  List<Object?> get props => [events, deletingEventId];
}

/// State when an event is successfully deleted
class GoogleCalendarEventsListEventDeleted extends GoogleCalendarEventsListState {
  const GoogleCalendarEventsListEventDeleted({
    required this.events,
    required this.deletedEventTitle,
  });

  final List<GoogleCalendarEventEntity> events;
  final String deletedEventTitle;

  @override
  List<Object?> get props => [events, deletedEventTitle];
}
