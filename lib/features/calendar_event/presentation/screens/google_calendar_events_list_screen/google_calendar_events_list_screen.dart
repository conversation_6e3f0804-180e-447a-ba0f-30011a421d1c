import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_events_list_screen/google_calendar_events_list_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_events_list_screen/google_calendar_events_list_state.dart';

/// Google Calendar events list screen
@RoutePage()
class GoogleCalendarEventsListScreen extends StatelessWidget
    implements AutoRouteWrapper {
  const GoogleCalendarEventsListScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => GoogleCalendarEventsListCubit(
        authenticateGoogleCalendarUseCase: sl(),
        checkGoogleCalendarAuthUseCase: sl(),
        getGoogleCalendarEventsUseCase: sl(),
        deleteGoogleCalendarEventUseCase: sl(),
        signOutGoogleCalendarUseCase: sl(),
      )..initialize(),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Google Calendar Events'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => AppNavigator.pop(),
        ),
        actions: [
          BlocBuilder<GoogleCalendarEventsListCubit,
              GoogleCalendarEventsListState>(
            builder: (context, state) {
              if (state is GoogleCalendarEventsListLoaded) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: () {
                        context
                            .read<GoogleCalendarEventsListCubit>()
                            .refreshEvents();
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.logout),
                      onPressed: () {
                        context.read<GoogleCalendarEventsListCubit>().signOut();
                      },
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: BlocConsumer<GoogleCalendarEventsListCubit,
          GoogleCalendarEventsListState>(
        listener: (context, state) {
          if (state is GoogleCalendarEventsListEventDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Event "${state.deletedEventTitle}" deleted successfully',
                ),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is GoogleCalendarEventsListError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.failure.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildContent(context, state),
          );
        },
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    GoogleCalendarEventsListState state,
  ) {
    switch (state.runtimeType) {
      case const (GoogleCalendarEventsListInitial):
      case const (GoogleCalendarEventsListLoading):
        return _buildLoadingContent();

      case const (GoogleCalendarEventsListAuthRequired):
        return _buildAuthRequiredContent(context);

      case const (GoogleCalendarEventsListAuthenticating):
        return _buildAuthenticatingContent();

      case const (GoogleCalendarEventsListLoaded):
        final loadedState = state as GoogleCalendarEventsListLoaded;
        return _buildEventsListContent(context, loadedState);

      case const (GoogleCalendarEventsListEmpty):
        return _buildEmptyContent();

      case const (GoogleCalendarEventsListDeleting):
        final deletingState = state as GoogleCalendarEventsListDeleting;
        return _buildDeletingContent(context, deletingState);

      case const (GoogleCalendarEventsListEventDeleted):
        final deletedState = state as GoogleCalendarEventsListEventDeleted;
        return _buildEventsListContent(
          context,
          GoogleCalendarEventsListLoaded(events: deletedState.events),
        );

      case GoogleCalendarEventsListError _:
        return _buildErrorContent(
          context,
          state as GoogleCalendarEventsListError,
        );

      default:
        return _buildLoadingContent();
    }
  }

  Widget _buildLoadingContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading events...', style: TextStyle(fontSize: 16)),
        ],
      ),
    );
  }

  Widget _buildAuthRequiredContent(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.account_circle, size: 80, color: Colors.grey),
          const SizedBox(height: 24),
          const Text(
            'Google Calendar Authentication Required',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'To view your Google Calendar events, you need to sign in with your Google account.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          PrimaryButton(
            text: 'Sign in with Google',
            onPressed: () {
              context.read<GoogleCalendarEventsListCubit>().authenticate();
            },
            icon: Icons.login,
          ),
        ],
      ),
    );
  }

  Widget _buildAuthenticatingContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Authenticating with Google Calendar...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildEventsListContent(
    BuildContext context,
    GoogleCalendarEventsListLoaded state,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<GoogleCalendarEventsListCubit>().refreshEvents();
      },
      child: ListView.builder(
        itemCount: state.events.length,
        itemBuilder: (context, index) {
          final event = state.events[index];
          return _buildEventCard(context, event, state.isRefreshing);
        },
      ),
    );
  }

  Widget _buildEventCard(
    BuildContext context,
    GoogleCalendarEventEntity event,
    bool isRefreshing,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Text(
          event.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_formatDateTime(event.startDateTime)} - ${_formatDateTime(event.endDateTime)}',
            ),
            if (event.description != null && event.description!.isNotEmpty)
              Text(
                event.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            if (event.location != null && event.location!.isNotEmpty)
              Text(
                '📍 ${event.location!}',
                style: const TextStyle(color: Colors.grey),
              ),
          ],
        ),
        trailing: isRefreshing
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'delete') {
                    _showDeleteConfirmation(context, event);
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete'),
                      ],
                    ),
                  ),
                ],
              ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildEmptyContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_busy, size: 80, color: Colors.grey),
          SizedBox(height: 24),
          Text(
            'No Events Found',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'You don\'t have any upcoming events in your Google Calendar.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeletingContent(
    BuildContext context,
    GoogleCalendarEventsListDeleting state,
  ) {
    return ListView.builder(
      itemCount: state.events.length,
      itemBuilder: (context, index) {
        final event = state.events[index];
        final isDeleting = event.eventId == state.deletingEventId;

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(
              event.title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isDeleting ? Colors.grey : null,
              ),
            ),
            subtitle: Text(
              '${_formatDateTime(event.startDateTime)} - ${_formatDateTime(event.endDateTime)}',
            ),
            trailing: isDeleting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : null,
          ),
        );
      },
    );
  }

  Widget _buildErrorContent(
    BuildContext context,
    GoogleCalendarEventsListError state,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 80, color: Colors.red),
          const SizedBox(height: 24),
          const Text(
            'Error',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            state.failure.message,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          if (state.canRetry) ...[
            const SizedBox(height: 32),
            PrimaryButton(
              text: 'Retry',
              onPressed: () {
                context.read<GoogleCalendarEventsListCubit>().initialize();
              },
              icon: Icons.refresh,
            ),
          ],
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    GoogleCalendarEventEntity event,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Event'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GoogleCalendarEventsListCubit>().deleteEvent(
                    event.eventId!,
                    event.title,
                  );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
