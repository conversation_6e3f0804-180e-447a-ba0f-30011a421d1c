import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/failures.dart';

import 'package:glidic_app/features/calendar_event/domain/usecases/get_calendar_events_usecase.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_events_list_screen/calendar_events_list_state.dart';

/// Calendar events list screen cubit (ViewModel in MVVM pattern)
/// Manages the state and business logic specific to the calendar events list screen
class CalendarEventsListCubit extends Cubit<CalendarEventsListState> {
  CalendarEventsListCubit({
    required this.getCalendarEventsUseCase,
  }) : super(const CalendarEventsListInitial());

  final GetCalendarEventsUseCase getCalendarEventsUseCase;

  /// Load calendar events (initial load)
  Future<void> loadCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  }) async {
    emit(const CalendarEventsListLoading());

    try {
      final events = await getCalendarEventsUseCase.execute(
        GetCalendarEventsParams(
          startDate: startDate,
          endDate: endDate,
          calendarIds: calendarIds,
        ),
      );

      emit(CalendarEventsListLoaded(eventsList: events));
    } catch (e) {
      if (e is AppFailure) {
        emit(CalendarEventsListError(failure: e));
      } else {
        emit(
          const CalendarEventsListError(
            failure: UnknownFailure('Failed to load calendar events'),
          ),
        );
      }
    }
  }

  /// Refresh calendar events (pull-to-refresh)
  Future<void> refreshCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  }) async {
    final currentEvents = state.events;

    if (currentEvents != null) {
      emit(CalendarEventsListRefreshing(currentEvents: currentEvents));
    } else {
      emit(const CalendarEventsListLoading());
    }

    try {
      final events = await getCalendarEventsUseCase.execute(
        GetCalendarEventsParams(
          startDate: startDate,
          endDate: endDate,
          calendarIds: calendarIds,
        ),
      );

      emit(
        CalendarEventsListLoaded(
          eventsList: events,
          isRefreshed: true,
        ),
      );
    } catch (e) {
      if (e is AppFailure) {
        emit(
          CalendarEventsListError(
            failure: e,
            previousEvents: currentEvents,
          ),
        );
      } else {
        emit(
          CalendarEventsListError(
            failure: const UnknownFailure('Failed to refresh calendar events'),
            previousEvents: currentEvents,
          ),
        );
      }
    }
  }

  /// Filter calendar events by date range
  Future<void> filterCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  }) async {
    final currentEvents = state.events ?? [];

    emit(CalendarEventsListFiltering(
      currentEvents: currentEvents,
      filterStartDate: startDate,
      filterEndDate: endDate,
    ),);

    try {
      final events = await getCalendarEventsUseCase.execute(
        GetCalendarEventsParams(
          startDate: startDate,
          endDate: endDate,
          calendarIds: calendarIds,
        ),
      );

      emit(CalendarEventsListLoaded(eventsList: events));
    } catch (e) {
      if (e is AppFailure) {
        emit(
          CalendarEventsListError(
            failure: e,
            previousEvents: currentEvents,
          ),
        );
      } else {
        emit(
          CalendarEventsListError(
            failure: const UnknownFailure('Failed to filter calendar events'),
            previousEvents: currentEvents,
          ),
        );
      }
    }
  }

  /// Retry loading events after error
  Future<void> retryLoadEvents() async {
    await loadCalendarEvents();
  }

  /// Clear any error state and return to loaded state
  void clearError() {
    final currentEvents = state.events;
    if (currentEvents != null) {
      emit(CalendarEventsListLoaded(eventsList: currentEvents));
    } else {
      emit(const CalendarEventsListInitial());
    }
  }
}
