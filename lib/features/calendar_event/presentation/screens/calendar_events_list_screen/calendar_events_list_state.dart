import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';

/// Base state for calendar events list screen
/// Following the MVVM pattern with BLoC state management
abstract class CalendarEventsListState extends Equatable {
  const CalendarEventsListState();

  /// Check if the current state is loading
  bool get isLoading => this is CalendarEventsListLoading;

  /// Check if the current state has events
  bool get hasEvents => this is CalendarEventsListLoaded && (this as CalendarEventsListLoaded).eventsList.isNotEmpty;

  /// Check if the current state is an error
  bool get isError => this is CalendarEventsListError;

  /// Check if the current state is refreshing
  bool get isRefreshing => this is CalendarEventsListRefreshing;

  /// Get events from the current state (null if not loaded)
  List<CalendarEventEntity>? get events {
    if (this is CalendarEventsListLoaded) {
      return (this as CalendarEventsListLoaded).eventsList;
    }
    if (this is CalendarEventsListRefreshing) {
      return (this as CalendarEventsListRefreshing).currentEvents;
    }
    if (this is CalendarEventsListError) {
      return (this as CalendarEventsListError).previousEvents;
    }
    return null;
  }

  /// Get error from the current state (null if no error)
  AppFailure? get error {
    if (this is CalendarEventsListError) {
      return (this as CalendarEventsListError).failure;
    }
    return null;
  }
}

/// Initial state when the screen is first loaded
class CalendarEventsListInitial extends CalendarEventsListState {
  const CalendarEventsListInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state when fetching calendar events
class CalendarEventsListLoading extends CalendarEventsListState {
  const CalendarEventsListLoading();

  @override
  List<Object?> get props => [];
}

/// Loaded state with calendar events data
class CalendarEventsListLoaded extends CalendarEventsListState {
  const CalendarEventsListLoaded({
    required this.eventsList,
    this.isRefreshed = false,
  });

  /// List of calendar events
  final List<CalendarEventEntity> eventsList;

  /// Whether this data was loaded from a refresh action
  final bool isRefreshed;

  @override
  List<Object?> get props => [eventsList, isRefreshed];
}

/// Refreshing state when pull-to-refresh is triggered
class CalendarEventsListRefreshing extends CalendarEventsListState {
  const CalendarEventsListRefreshing({
    required this.currentEvents,
  });

  /// Current events being displayed while refreshing
  final List<CalendarEventEntity> currentEvents;

  @override
  List<Object?> get props => [currentEvents];
}

/// Error state when calendar events loading fails
class CalendarEventsListError extends CalendarEventsListState {
  const CalendarEventsListError({
    required this.failure,
    this.previousEvents,
  });

  /// The failure that occurred
  final AppFailure failure;

  /// Previous events data (if any) to show while displaying error
  final List<CalendarEventEntity>? previousEvents;

  @override
  List<Object?> get props => [failure, previousEvents];
}

/// State when filtering events
class CalendarEventsListFiltering extends CalendarEventsListState {
  const CalendarEventsListFiltering({
    required this.currentEvents,
    required this.filterStartDate,
    required this.filterEndDate,
  });

  /// Current events being displayed while filtering
  final List<CalendarEventEntity> currentEvents;

  /// Start date filter being applied
  final DateTime? filterStartDate;

  /// End date filter being applied
  final DateTime? filterEndDate;

  @override
  List<Object?> get props => [currentEvents, filterStartDate, filterEndDate];
}
