import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/error_codes.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/core/common/extensions/date_time_extensions.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';

import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_events_list_screen/calendar_events_list_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_events_list_screen/calendar_events_list_state.dart';

/// Calendar events list screen (View in MVVM pattern)
/// Displays a list of calendar events with filtering and refresh capabilities
@RoutePage()
class CalendarEventsListScreen extends StatelessWidget implements AutoRouteWrapper {
  const CalendarEventsListScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => CalendarEventsListCubit(
        getCalendarEventsUseCase: sl(),
      )..loadCalendarEvents(),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Calendar Events',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
            tooltip: 'Filter Events',
          ),
        ],
      ),
      body: BlocConsumer<CalendarEventsListCubit, CalendarEventsListState>(
        listener: (context, state) {
          if (state.isError && state.error != null) {
            _handleError(context, state.error!);
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () => context.read<CalendarEventsListCubit>().refreshCalendarEvents(),
            child: _buildBody(context, state),
          );
        },
      ),
    );
  }

  /// Build the main body content based on current state
  Widget _buildBody(BuildContext context, CalendarEventsListState state) {
    if (state.isLoading && state.events == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading calendar events...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    final events = state.events;
    if (events == null || events.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildEventsList(context, events, state.isRefreshing);
  }

  /// Build empty state when no events are found
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.event_busy,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No Events Found',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'No calendar events found for the selected period. Try adjusting your filter or check your calendar permissions.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            PrimaryButton(
              text: 'Retry',
              onPressed: () {
                context.read<CalendarEventsListCubit>().retryLoadEvents();
              },
              icon: Icons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  /// Build the events list
  Widget _buildEventsList(BuildContext context, List<CalendarEventEntity> events, bool isRefreshing) {
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return _buildEventCard(event);
      },
    );
  }

  /// Build individual event card
  Widget _buildEventCard(CalendarEventEntity event) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              event.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  '${event.startDateTime.toCalendarEventFormat()} - ${event.endDateTime.toTimeOnlyFormat()}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            if (event.location != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      event.location!,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (event.description != null && event.description!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                event.description!,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Show filter dialog
  void _showFilterDialog(BuildContext context) {
    // TODO: Implement filter dialog with date range picker
    // This is a placeholder for the filter functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Filter functionality coming soon!'),
      ),
    );
  }

  /// Handle errors with conditional logic based on error codes
  /// This demonstrates how to use centralized error codes for different error handling
  void _handleError(BuildContext context, AppFailure error) {
    // Example of conditional error handling using error codes
    if (error.code == ErrorCodes.calendarPermissionDenied) {
      // Show specific UI for permission denied
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Calendar permission required'),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: 'Grant Permission',
            textColor: Colors.white,
            onPressed: () {
              // TODO: Navigate to app settings or request permissions
              context.read<CalendarEventsListCubit>().retryLoadEvents();
            },
          ),
        ),
      );
    } else if (error.code == ErrorCodes.noCalendarEventsFound) {
      // For no events found, we don't show a snackbar since the empty state handles this
      return;
    } else if (ErrorCodes.isNetworkError(error.code)) {
      // Handle network errors with specific messaging
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Network error. Please check your connection.'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () {
              context.read<CalendarEventsListCubit>().retryLoadEvents();
            },
          ),
        ),
      );
    } else {
      // Default error handling for other error types
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error.message),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () {
              context.read<CalendarEventsListCubit>().retryLoadEvents();
            },
          ),
        ),
      );
    }
  }
}
