import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/core/presentation/widgets/inputs/primary_text_field.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_state.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/widgets/google_calendar_reminders_selector.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/widgets/google_calendar_selector.dart';

/// Form widget for creating Google Calendar events
class GoogleCalendarEventForm extends StatefulWidget {
  const GoogleCalendarEventForm({super.key});

  @override
  State<GoogleCalendarEventForm> createState() =>
      _GoogleCalendarEventFormState();
}

class _GoogleCalendarEventFormState extends State<GoogleCalendarEventForm> {
  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Date and time variables
  DateTime _startDateTime = DateTime.now().add(const Duration(hours: 1));
  DateTime _endDateTime = DateTime.now().add(const Duration(hours: 2));

  // Calendar selection variables
  String? _selectedCalendarId;

  // Reminders variables
  List<int> _selectedReminders = [];

  // Screen-specific constants
  static const double _fieldSpacing = 16.0;
  static const double _sectionSpacing = 24.0;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GoogleCalendarEventCubit, GoogleCalendarEventState>(
      builder: (context, state) {
        final isLoading = state is GoogleCalendarEventLoading;

        return Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeaderSection(),
                const SizedBox(height: _sectionSpacing),
                _buildCalendarSelector(),
                const SizedBox(height: _fieldSpacing),
                _buildTitleField(),
                const SizedBox(height: _fieldSpacing),
                _buildDateTimeSection(),
                const SizedBox(height: _fieldSpacing),
                _buildDescriptionField(),
                const SizedBox(height: _fieldSpacing),
                _buildLocationField(),
                const SizedBox(height: _fieldSpacing),
                _buildRemindersSelector(),
                const SizedBox(height: _sectionSpacing),
                _buildCreateButton(isLoading),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build header section
  Widget _buildHeaderSection() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Create Google Calendar Event',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          'Fill in the details below to create an event in your Google Calendar.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  /// Build calendar selector
  Widget _buildCalendarSelector() {
    return GoogleCalendarSelector(
      selectedCalendarId: _selectedCalendarId,
      onCalendarSelected: (calendarId, calendarName) {
        setState(() {
          _selectedCalendarId = calendarId;
        });
      },
    );
  }

  /// Build title field
  Widget _buildTitleField() {
    return PrimaryTextField(
      controller: _titleController,
      labelText: 'Event Title',
      hintText: 'Enter event title',
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Title is required';
        }
        return null;
      },
      prefixIcon: const Icon(Icons.title),
    );
  }

  /// Build date and time section
  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date & Time',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateTimeButton(
                label: 'Start',
                dateTime: _startDateTime,
                onTap: () => _selectDateTime(true),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateTimeButton(
                label: 'End',
                dateTime: _endDateTime,
                onTap: () => _selectDateTime(false),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build date time button
  Widget _buildDateTimeButton({
    required String label,
    required DateTime dateTime,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${dateTime.day}/${dateTime.month}/${dateTime.year}',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            Text(
              '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// Build description field
  Widget _buildDescriptionField() {
    return PrimaryTextField(
      controller: _descriptionController,
      labelText: 'Description (Optional)',
      hintText: 'Enter event description',
      maxLines: 3,
      prefixIcon: const Icon(Icons.description),
    );
  }

  /// Build reminders selector
  Widget _buildRemindersSelector() {
    return GoogleCalendarRemindersSelector(
      initialReminders: _selectedReminders,
      onRemindersChanged: (reminders) {
        setState(() {
          _selectedReminders = reminders;
        });
      },
    );
  }

  /// Build location field
  Widget _buildLocationField() {
    return PrimaryTextField(
      controller: _locationController,
      labelText: 'Location (Optional)',
      hintText: 'Enter event location',
      prefixIcon: const Icon(Icons.location_on),
    );
  }

  /// Build create button
  Widget _buildCreateButton(bool isLoading) {
    return PrimaryButton(
      text: 'Create Event',
      onPressed: isLoading ? null : _handleCreateEvent,
      isLoading: isLoading,
      icon: Icons.event,
    );
  }

  /// Handle create event
  void _handleCreateEvent() {
    if (_formKey.currentState?.validate() ?? false) {
      // Validate date/time
      if (!_endDateTime.isAfter(_startDateTime)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('End time must be after start time'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Create the event
      context.read<GoogleCalendarEventCubit>().createEvent(
            title: _titleController.text,
            startDateTime: _startDateTime,
            endDateTime: _endDateTime,
            description: _descriptionController.text.isEmpty
                ? null
                : _descriptionController.text,
            location: _locationController.text.isEmpty
                ? null
                : _locationController.text,
            calendarId: _selectedCalendarId,
            reminders:
                _selectedReminders.isNotEmpty ? _selectedReminders : null,
          );
    }
  }

  /// Select date and time
  Future<void> _selectDateTime(bool isStart) async {
    final currentDateTime = isStart ? _startDateTime : _endDateTime;

    // Select date
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: currentDateTime,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (selectedDate == null) return;

    // Select time
    if (!mounted) return;
    final selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(currentDateTime),
    );

    if (selectedTime == null || !mounted) return;

    // Combine date and time
    final newDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      selectedTime.hour,
      selectedTime.minute,
    );

    setState(() {
      if (isStart) {
        _startDateTime = newDateTime;
        // Ensure end time is after start time
        if (!_endDateTime.isAfter(_startDateTime)) {
          _endDateTime = _startDateTime.add(const Duration(hours: 1));
        }
      } else {
        _endDateTime = newDateTime;
      }
    });
  }
}
