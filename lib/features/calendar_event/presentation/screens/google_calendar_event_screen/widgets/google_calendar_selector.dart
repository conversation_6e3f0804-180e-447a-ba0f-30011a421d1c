import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_cubit.dart';

/// Widget for selecting Google Calendar from available calendars
class GoogleCalendarSelector extends StatefulWidget {
  const GoogleCalendarSelector({
    super.key,
    required this.onCalendarSelected,
    this.selectedCalendarId,
  });

  final Function(String calendarId, String calendarName) onCalendarSelected;
  final String? selectedCalendarId;

  @override
  State<GoogleCalendarSelector> createState() => _GoogleCalendarSelectorState();
}

class _GoogleCalendarSelectorState extends State<GoogleCalendarSelector> {
  List<Map<String, dynamic>>? _calendars;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadCalendars();
  }

  Future<void> _loadCalendars() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cubit = context.read<GoogleCalendarEventCubit>();
      final calendars = await cubit.getGoogleCalendarsUseCase.execute();
      
      setState(() {
        _calendars = calendars;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Calendar',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        _buildCalendarSelector(),
      ],
    );
  }

  Widget _buildCalendarSelector() {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('Loading calendars...'),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.error, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Failed to load calendars',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 12),
              PrimaryButton(
                text: 'Retry',
                onPressed: _loadCalendars,
                icon: Icons.refresh,
              ),
            ],
          ),
        ),
      );
    }

    if (_calendars == null || _calendars!.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(Icons.calendar_today, color: Colors.grey, size: 20),
              SizedBox(width: 12),
              Text('No calendars available'),
            ],
          ),
        ),
      );
    }

    // Find selected calendar or default to primary
    final selectedCalendar = _calendars!.firstWhere(
      (calendar) => calendar['id'] == widget.selectedCalendarId,
      orElse: () => _calendars!.firstWhere(
        (calendar) => calendar['primary'] == true,
        orElse: () => _calendars!.first,
      ),
    );

    return Card(
      child: ExpansionTile(
        leading: Icon(
          Icons.calendar_today,
          color: _getCalendarColor(selectedCalendar),
        ),
        title: Text(
          selectedCalendar['summary'] ?? 'Untitled Calendar',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: selectedCalendar['primary'] == true
            ? const Text('Primary Calendar')
            : null,
        children: _calendars!.map((calendar) {
          final isSelected = calendar['id'] == selectedCalendar['id'];
          
          return ListTile(
            leading: Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
            ),
            title: Text(calendar['summary'] ?? 'Untitled Calendar'),
            subtitle: calendar['primary'] == true
                ? const Text('Primary Calendar')
                : Text(calendar['description'] ?? ''),
            trailing: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: _getCalendarColor(calendar),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey.shade300),
              ),
            ),
            onTap: () {
              widget.onCalendarSelected(
                calendar['id'] ?? 'primary',
                calendar['summary'] ?? 'Untitled Calendar',
              );
            },
          );
        }).toList(),
      ),
    );
  }

  Color _getCalendarColor(Map<String, dynamic> calendar) {
    final colorString = calendar['backgroundColor'] as String?;
    if (colorString != null && colorString.isNotEmpty) {
      try {
        // Remove # if present and parse hex color
        final hexColor = colorString.replaceAll('#', '');
        return Color(int.parse('FF$hexColor', radix: 16));
      } catch (e) {
        // Fallback to default color if parsing fails
      }
    }
    
    // Default colors based on calendar type
    if (calendar['primary'] == true) {
      return Colors.blue;
    }
    
    return Colors.green;
  }
}
