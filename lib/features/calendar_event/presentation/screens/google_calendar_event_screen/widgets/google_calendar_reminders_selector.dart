import 'package:flutter/material.dart';

/// Widget for selecting event reminders
class GoogleCalendarRemindersSelector extends StatefulWidget {
  const GoogleCalendarRemindersSelector({
    super.key,
    required this.onRemindersChanged,
    this.initialReminders,
  });

  final Function(List<int> reminders) onRemindersChanged;
  final List<int>? initialReminders;

  @override
  State<GoogleCalendarRemindersSelector> createState() =>
      _GoogleCalendarRemindersSelectorState();
}

class _GoogleCalendarRemindersSelectorState
    extends State<GoogleCalendarRemindersSelector> {
  late List<int> _selectedReminders;

  // TODO: Remove mock data
  // Common reminder options (in minutes)
  static const List<Map<String, dynamic>> _reminderOptions = [
    {'label': '5 minutes before', 'value': 5},
    {'label': '10 minutes before', 'value': 10},
    {'label': '15 minutes before', 'value': 15},
    {'label': '30 minutes before', 'value': 30},
    {'label': '1 hour before', 'value': 60},
    {'label': '2 hours before', 'value': 120},
    {'label': '1 day before', 'value': 1440},
    {'label': '1 week before', 'value': 10080},
  ];

  @override
  void initState() {
    super.initState();
    _selectedReminders = List.from(widget.initialReminders ?? []);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Reminders',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        _buildRemindersSelector(),
      ],
    );
  }

  Widget _buildRemindersSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.notifications, size: 20),
                const SizedBox(width: 8),
                Text(
                  _selectedReminders.isEmpty
                      ? 'No reminders set'
                      : '${_selectedReminders.length} reminder${_selectedReminders.length == 1 ? '' : 's'} set',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _showReminderDialog,
                  child: const Text('Edit'),
                ),
              ],
            ),
            if (_selectedReminders.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: _selectedReminders.map((minutes) {
                  final label = _getReminderLabel(minutes);
                  return Chip(
                    label: Text(label),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeReminder(minutes),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showReminderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Reminders'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select when you want to be reminded about this event:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ..._reminderOptions.map((option) {
                final isSelected = _selectedReminders.contains(option['value']);
                return CheckboxListTile(
                  title: Text(option['label']),
                  value: isSelected,
                  onChanged: (selected) {
                    setState(() {
                      if (selected == true) {
                        if (!_selectedReminders.contains(option['value'])) {
                          _selectedReminders.add(option['value']);
                          _selectedReminders.sort();
                        }
                      } else {
                        _selectedReminders.remove(option['value']);
                      }
                    });
                    Navigator.of(context).pop();
                    _showReminderDialog(); // Refresh dialog
                  },
                );
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedReminders.clear();
              });
              Navigator.of(context).pop();
              widget.onRemindersChanged(_selectedReminders);
            },
            child: const Text('Clear All'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onRemindersChanged(_selectedReminders);
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _removeReminder(int minutes) {
    setState(() {
      _selectedReminders.remove(minutes);
    });
    widget.onRemindersChanged(_selectedReminders);
  }

  String _getReminderLabel(int minutes) {
    final option = _reminderOptions.firstWhere(
      (option) => option['value'] == minutes,
      orElse: () => {'label': '$minutes minutes before', 'value': minutes},
    );
    return option['label'];
  }
}
