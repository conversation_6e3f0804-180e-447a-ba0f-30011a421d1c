import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_state.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/widgets/google_calendar_event_form.dart';

/// Main content widget for Google Calendar event screen
class GoogleCalendarEventContent extends StatelessWidget {
  const GoogleCalendarEventContent({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GoogleCalendarEventCubit, GoogleCalendarEventState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: _buildContent(context, state),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, GoogleCalendarEventState state) {
    switch (state.runtimeType) {
      case const (GoogleCalendarEventInitial):
      case const (GoogleCalendarEventLoading):
        return _buildLoadingContent();

      case const (GoogleCalendarEventAuthRequired):
        return _buildAuthRequiredContent(context);

      case const (GoogleCalendarEventAuthenticating):
        return _buildAuthenticatingContent();

      case const (GoogleCalendarEventAuthenticated):
        return const GoogleCalendarEventForm();

      case const (GoogleCalendarEventSuccess):
        return _buildSuccessContent(state as GoogleCalendarEventSuccess);

      case const (GoogleCalendarEventError):
        return _buildErrorContent(context, state as GoogleCalendarEventError);

      default:
        return _buildLoadingContent();
    }
  }

  /// Build loading content
  Widget _buildLoadingContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// Build authentication required content
  Widget _buildAuthRequiredContent(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.account_circle,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 24),
          const Text(
            'Google Calendar Authentication Required',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'To create events in Google Calendar, you need to sign in with your Google account.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          PrimaryButton(
            text: 'Sign in with Google',
            onPressed: () {
              context.read<GoogleCalendarEventCubit>().authenticate();
            },
            icon: Icons.login,
          ),
        ],
      ),
    );
  }

  /// Build authenticating content
  Widget _buildAuthenticatingContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Authenticating with Google Calendar...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// Build success content
  Widget _buildSuccessContent(GoogleCalendarEventSuccess state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle,
            size: 80,
            color: Colors.green,
          ),
          const SizedBox(height: 24),
          const Text(
            'Event Created Successfully!',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Event "${state.event.title}" has been created in your Google Calendar.',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build error content
  Widget _buildErrorContent(
    BuildContext context,
    GoogleCalendarEventError state,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 24),
          const Text(
            'Error',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            state.failure.message,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          if (state.canRetry) ...[
            const SizedBox(height: 32),
            PrimaryButton(
              text: 'Retry',
              onPressed: () {
                context.read<GoogleCalendarEventCubit>().initialize();
              },
              icon: Icons.refresh,
            ),
          ],
        ],
      ),
    );
  }
}
