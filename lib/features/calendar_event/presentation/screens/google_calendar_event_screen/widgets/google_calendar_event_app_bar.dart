import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_state.dart';

/// App bar for Google Calendar event screen
class GoogleCalendarEventAppBar extends StatelessWidget implements PreferredSizeWidget {
  const GoogleCalendarEventAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GoogleCalendarEventCubit, GoogleCalendarEventState>(
      builder: (context, state) {
        return AppBar(
          title: const Text(
            'Create Google Calendar Event',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => AppNavigator.pop(),
          ),
          actions: [
            if (state is GoogleCalendarEventAuthenticated) ...[
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () {
                  context.read<GoogleCalendarEventCubit>().signOut();
                },
                tooltip: 'Sign out from Google Calendar',
              ),
            ],
          ],
          elevation: 1,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
