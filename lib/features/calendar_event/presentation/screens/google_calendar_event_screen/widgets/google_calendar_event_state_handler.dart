import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/error_codes.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_state.dart';

/// State handler for Google Calendar event screen
class GoogleCalendarEventStateHandler extends StatelessWidget {
  const GoogleCalendarEventStateHandler({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocListener<GoogleCalendarEventCubit, GoogleCalendarEventState>(
      listener: (context, state) {
        if (state is GoogleCalendarEventSuccess) {
          _handleSuccess(context, state);
        } else if (state is GoogleCalendarEventError) {
          _handleError(context, state);
        }
      },
      child: child,
    );
  }

  /// Handle successful event creation
  void _handleSuccess(BuildContext context, GoogleCalendarEventSuccess state) {
    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Event "${state.event.title}" created successfully in Google Calendar!',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );

    // Navigate back after a short delay
    Future.delayed(const Duration(seconds: 2), () {
      if (context.mounted) {
        AppNavigator.pop();
      }
    });
  }

  /// Handles errors based on error codes
  void _handleError(BuildContext context, GoogleCalendarEventError state) {
    final error = state.failure;
    
    // Handle specific Google Calendar errors
    if (error.code == ErrorCodes.googleCalendarAuthFailed) {
      _showAuthErrorDialog(context);
    } else if (error.code == ErrorCodes.googleCalendarAccessDenied) {
      _showAccessDeniedDialog(context);
    } else if (error.code == ErrorCodes.googleCalendarQuotaExceeded) {
      _showQuotaExceededDialog(context);
    } else if (error.code == ErrorCodes.googleCalendarNotConfigured) {
      _showNotConfiguredDialog(context);
    } else if (error.code == ErrorCodes.noConnection) {
      _showNetworkErrorDialog(context);
    } else if (error.code == ErrorCodes.timeout) {
      _showTimeoutErrorDialog(context);
    } else if (error is ValidationFailure) {
      _showValidationErrorDialog(context, error);
    } else {
      // Show general error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            error.message,
          ),
          backgroundColor: Colors.red,
          action: state.canRetry
              ? SnackBarAction(
                  label: 'Retry',
                  textColor: Colors.white,
                  onPressed: () {
                    context.read<GoogleCalendarEventCubit>().initialize();
                  },
                )
              : null,
        ),
      );
    }
  }

  /// Show authentication error dialog
  void _showAuthErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Authentication Failed'),
        content: const Text(
          'Failed to authenticate with Google Calendar. Please try again.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show access denied dialog
  void _showAccessDeniedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Access Denied'),
        content: const Text(
          'Google Calendar access was denied. Please grant calendar permissions to create events.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GoogleCalendarEventCubit>().authenticate();
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  /// Show quota exceeded dialog
  void _showQuotaExceededDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quota Exceeded'),
        content: const Text(
          'Google Calendar API quota has been exceeded. Please try again later.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show not configured dialog
  void _showNotConfiguredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Google Calendar Setup Required'),
        content: const Text(
          'Google Calendar integration requires OAuth configuration. Please check that:\n\n'
          '1. Google Calendar API is enabled in Google Cloud Console\n'
          '2. OAuth 2.0 credentials are created for your platform\n'
          '3. SHA-1 fingerprint is added for Android\n'
          '4. Bundle identifier is correct for iOS\n\n'
          'See docs/GOOGLE_CALENDAR_SETUP.md for detailed setup instructions.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show network error dialog
  void _showNetworkErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Error'),
        content: const Text(
          'No internet connection available. Please check your network connection and try again.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GoogleCalendarEventCubit>().initialize();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Show timeout error dialog
  void _showTimeoutErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Request Timeout'),
        content: const Text(
          'The request took too long to complete. Please try again.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<GoogleCalendarEventCubit>().initialize();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Show validation error dialog
  void _showValidationErrorDialog(BuildContext context, ValidationFailure error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Invalid Input'),
        content: Text(
          error.message,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
