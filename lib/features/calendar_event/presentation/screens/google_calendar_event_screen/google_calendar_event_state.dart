import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';

/// Base state for Google Calendar event screen
abstract class GoogleCalendarEventState extends Equatable {
  const GoogleCalendarEventState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class GoogleCalendarEventInitial extends GoogleCalendarEventState {
  const GoogleCalendarEventInitial();
}

/// Loading state
class GoogleCalendarEventLoading extends GoogleCalendarEventState {
  const GoogleCalendarEventLoading();
}

/// State when authentication is required
class GoogleCalendarEventAuthRequired extends GoogleCalendarEventState {
  const GoogleCalendarEventAuthRequired();
}

/// State when authentication is in progress
class GoogleCalendarEventAuthenticating extends GoogleCalendarEventState {
  const GoogleCalendarEventAuthenticating();
}

/// State when authentication is successful and ready to create events
class GoogleCalendarEventAuthenticated extends GoogleCalendarEventState {
  const GoogleCalendarEventAuthenticated({
    required this.userInfo,
  });

  final Map<String, dynamic> userInfo;

  @override
  List<Object?> get props => [userInfo];
}

/// State when event creation is successful
class GoogleCalendarEventSuccess extends GoogleCalendarEventState {
  const GoogleCalendarEventSuccess({
    required this.event,
  });

  final GoogleCalendarEventEntity event;

  @override
  List<Object?> get props => [event];
}

/// Error state with failure information
class GoogleCalendarEventError extends GoogleCalendarEventState {
  const GoogleCalendarEventError({
    required this.failure,
    this.canRetry = true,
  });

  final AppFailure failure;
  final bool canRetry;

  @override
  List<Object?> get props => [failure, canRetry];
}
