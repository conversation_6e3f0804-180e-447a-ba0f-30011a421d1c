import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/widgets/google_calendar_event_app_bar.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/widgets/google_calendar_event_content.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/widgets/google_calendar_event_state_handler.dart';

/// Google Calendar event creation screen
@RoutePage()
class GoogleCalendarEventScreen extends StatelessWidget
    implements AutoRouteWrapper {
  const GoogleCalendarEventScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => GoogleCalendarEventCubit(
        authenticateGoogleCalendarUseCase: sl(),
        checkGoogleCalendarAuthUseCase: sl(),
        createGoogleCalendarEventUseCase: sl(),
        signOutGoogleCalendarUseCase: sl(),
        getGoogleCalendarsUseCase: sl(),
      )..initialize(),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return const GoogleCalendarEventStateHandler(
      child: Scaffold(
        appBar: GoogleCalendarEventAppBar(),
        body: GoogleCalendarEventContent(),
      ),
    );
  }
}
