import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/authenticate_google_calendar_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/create_google_calendar_event_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/get_google_calendar_events_usecase.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/google_calendar_event_screen/google_calendar_event_state.dart';

/// Google Calendar event cubit
class GoogleCalendarEventCubit extends Cubit<GoogleCalendarEventState> {
  GoogleCalendarEventCubit({
    required this.authenticateGoogleCalendarUseCase,
    required this.checkGoogleCalendarAuthUseCase,
    required this.createGoogleCalendarEventUseCase,
    required this.signOutGoogleCalendarUseCase,
    required this.getGoogleCalendarsUseCase,
  }) : super(const GoogleCalendarEventInitial());

  final AuthenticateGoogleCalendarUseCase authenticateGoogleCalendarUseCase;
  final CheckGoogleCalendarAuthUseCase checkGoogleCalendarAuthUseCase;
  final CreateGoogleCalendarEventUseCase createGoogleCalendarEventUseCase;
  final SignOutGoogleCalendarUseCase signOutGoogleCalendarUseCase;
  final GetGoogleCalendarsUseCase getGoogleCalendarsUseCase;

  /// Initializes the screen
  Future<void> initialize() async {
    emit(const GoogleCalendarEventLoading());

    try {
      final isAuthenticated = await checkGoogleCalendarAuthUseCase.execute();
      
      if (isAuthenticated) {
        // User is already authenticated, show ready state
        emit(const GoogleCalendarEventAuthenticated(userInfo: {}));
      } else {
        // User needs to authenticate
        emit(const GoogleCalendarEventAuthRequired());
      }
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(GoogleCalendarEventError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Authenticates with Google Calendar
  Future<void> authenticate() async {
    emit(const GoogleCalendarEventAuthenticating());

    try {
      final success = await authenticateGoogleCalendarUseCase.execute();
      
      if (success) {
        emit(const GoogleCalendarEventAuthenticated(userInfo: {}));
      } else {
        emit(
          const GoogleCalendarEventError(
            failure: CalendarFailure('Authentication was cancelled by user'),
            canRetry: true,
          ),
        );
      }
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(GoogleCalendarEventError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Creates Google Calendar event
  Future<void> createEvent({
    required String title,
    required DateTime startDateTime,
    required DateTime endDateTime,
    String? description,
    String? location,
    String? calendarId,
    List<int>? reminders,
  }) async {
    // Validate form data first
    final validationErrors = _validateForm(
      title: title,
      startDateTime: startDateTime,
      endDateTime: endDateTime,
    );

    if (validationErrors.isNotEmpty) {
      emit(
        GoogleCalendarEventError(
          failure: ValidationFailure('Validation failed: ${validationErrors.join(', ')}'),
          canRetry: false,
        ),
      );
      return;
    }

    emit(const GoogleCalendarEventLoading());

    try {
      // Ensure user is authenticated before creating event
      // This prevents redundant authentication prompts in the use case
      await _ensureAuthenticated();

      // Create the event entity
      final event = GoogleCalendarEventEntity(
        title: title.trim(),
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        description: description?.trim().isEmpty == true ? null : description?.trim(),
        location: location?.trim().isEmpty == true ? null : location?.trim(),
        calendarId: calendarId,
        reminders: reminders,
      );

      // Execute create event use case
      final createdEvent = await createGoogleCalendarEventUseCase.execute(
        CreateGoogleCalendarEventParams(event: event),
      );

      // Emit success state
      emit(GoogleCalendarEventSuccess(event: createdEvent));
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(GoogleCalendarEventError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          GoogleCalendarEventError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Signs out from Google Calendar
  Future<void> signOut() async {
    try {
      await signOutGoogleCalendarUseCase.execute();
      emit(const GoogleCalendarEventAuthRequired());
    } catch (e) {
      // Even if sign out fails, reset to auth required state
      emit(const GoogleCalendarEventAuthRequired());
    }
  }

  /// Resets to initial state
  void reset() {
    emit(const GoogleCalendarEventInitial());
  }

  /// Ensure the user is authenticated before performing operations
  ///
  /// This method checks authentication status and only triggers authentication
  /// if the user is not currently authenticated. This prevents redundant
  /// authentication prompts within the same session.
  ///
  /// Throws [AppFailure] if authentication fails or is cancelled by user
  Future<void> _ensureAuthenticated() async {
    // Check current authentication status
    final isAuthenticated = await checkGoogleCalendarAuthUseCase.execute();

    if (!isAuthenticated) {
      // User is not authenticated, attempt to authenticate
      final authSuccess = await authenticateGoogleCalendarUseCase.execute();

      if (!authSuccess) {
        throw AppFailureConstructors.googleCalendarAuthFailed(
          'Authentication required to create calendar events',
        );
      }
    }

    // If we reach here, user is authenticated
  }

  /// Validate form data
  List<String> _validateForm({
    required String title,
    required DateTime startDateTime,
    required DateTime endDateTime,
  }) {
    final errors = <String>[];

    if (title.trim().isEmpty) {
      errors.add('Title is required');
    }

    if (!endDateTime.isAfter(startDateTime)) {
      errors.add('End time must be after start time');
    }

    return errors;
  }
}
