import 'package:equatable/equatable.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_info_entity.dart';

/// Calendar event screen specific state classes designed for UI needs
/// These states are focused specifically on the calendar event creation flow
abstract class CalendarEventState extends Equatable {
  const CalendarEventState();
}

/// Initial state - calendar event screen is ready for user input
class CalendarEventInitial extends CalendarEventState {
  const CalendarEventInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state - calendar event creation in progress
class CalendarEventLoading extends CalendarEventState {
  const CalendarEventLoading();

  @override
  List<Object?> get props => [];
}

/// Success state - calendar event created successfully
class CalendarEventSuccess extends CalendarEventState {
  const CalendarEventSuccess({
    required this.event,
  });

  final CalendarEventEntity event;

  @override
  List<Object?> get props => [event];
}

/// Error state - calendar event creation failed
class CalendarEventError extends CalendarEventState {
  const CalendarEventError({
    required this.failure,
    this.canRetry = true,
  });

  final AppFailure failure;
  final bool canRetry;

  @override
  List<Object?> get props => [failure, canRetry];
}

/// Form validation state - form has validation errors
class CalendarEventFormValidation extends CalendarEventState {
  const CalendarEventFormValidation({
    required this.errors,
  });

  final Map<String, String> errors;

  @override
  List<Object?> get props => [errors];
}

/// Calendar selection required state (Android only)
/// UI should show calendar selection dialog when this state is emitted
class CalendarEventCalendarSelectionRequired extends CalendarEventState {
  const CalendarEventCalendarSelectionRequired({
    required this.availableCalendars,
    required this.eventData,
  });

  final List<CalendarInfoEntity> availableCalendars;
  final CalendarEventData eventData;

  @override
  List<Object?> get props => [availableCalendars, eventData];
}

/// Data class to hold event information for calendar selection
class CalendarEventData extends Equatable {
  const CalendarEventData({
    required this.title,
    required this.startDateTime,
    required this.endDateTime,
    this.description,
    this.location,
  });

  final String title;
  final DateTime startDateTime;
  final DateTime endDateTime;
  final String? description;
  final String? location;

  @override
  List<Object?> get props => [title, startDateTime, endDateTime, description, location];
}

/// Extension methods for convenient state checking
extension CalendarEventStateExtension on CalendarEventState {
  /// Check if calendar event creation is in progress
  bool get isLoading => this is CalendarEventLoading;

  /// Check if calendar event creation was successful
  bool get isSuccess => this is CalendarEventSuccess;

  /// Check if there's an error
  bool get hasError => this is CalendarEventError;

  /// Check if state is initial
  bool get isInitial => this is CalendarEventInitial;

  /// Check if state is form validation
  bool get isFormValidation => this is CalendarEventFormValidation;

  /// Check if calendar selection is required
  bool get isCalendarSelectionRequired => this is CalendarEventCalendarSelectionRequired;

  /// Get created event (null if not successful)
  CalendarEventEntity? get event {
    if (this is CalendarEventSuccess) {
      return (this as CalendarEventSuccess).event;
    }
    return null;
  }

  /// Get error failure (null if no error)
  AppFailure? get failure {
    if (this is CalendarEventError) {
      return (this as CalendarEventError).failure;
    }
    return null;
  }

  /// Get form validation errors (empty if no validation errors)
  Map<String, String> get validationErrors {
    if (this is CalendarEventFormValidation) {
      return (this as CalendarEventFormValidation).errors;
    }
    return {};
  }

  /// Check if error can be retried
  bool get canRetryError {
    if (this is CalendarEventError) {
      return (this as CalendarEventError).canRetry;
    }
    return false;
  }
}
