import 'package:flutter/material.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/extensions/extensions.dart';

/// Reusable date/time picker field widget for calendar events
/// 
/// This widget provides a consistent date/time selection interface
/// with proper styling, validation, and user interaction handling
class CalendarDateTimeField extends StatelessWidget {
  const CalendarDateTimeField({
    super.key,
    required this.label,
    required this.value,
    required this.onTap,
    required this.icon,
    this.errorText,
    this.placeholder = 'Select date and time',
  });

  /// Field label text
  final String label;

  /// Current selected date/time value
  final DateTime? value;

  /// Callback when field is tapped
  final VoidCallback onTap;

  /// Icon to display in the field
  final IconData icon;

  /// Error text to display (if any)
  final String? errorText;

  /// Placeholder text when no value is selected
  final String placeholder;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Field label
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: ColorConstants.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        
        // Date/time field container
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(Dimensions.textFieldBorderRadius),
            boxShadow: [
              BoxShadow(
                color: ColorConstants.textFieldShadow.withValues(alpha: 0.24),
                offset: const Offset(0, 1),
                blurRadius: 2,
                spreadRadius: 0,
              ),
            ],
          ),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(Dimensions.textFieldBorderRadius),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: Dimensions.textFieldHorizontalPadding,
                vertical: Dimensions.textFieldVerticalPadding,
              ),
              decoration: BoxDecoration(
                color: ColorConstants.textFieldBackground,
                borderRadius: BorderRadius.circular(Dimensions.textFieldBorderRadius),
                border: Border.all(
                  color: errorText != null
                      ? ColorConstants.inputError
                      : ColorConstants.textFieldBorder,
                  width: errorText != null ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  // Leading icon
                  Icon(
                    icon,
                    color: ColorConstants.textFieldIcon,
                    size: 18,
                  ),
                  const SizedBox(width: 12),
                  
                  // Date/time text or placeholder
                  Expanded(
                    child: Text(
                      value != null ? value!.toCalendarEventFormat() : placeholder,
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: value != null
                            ? ColorConstants.textFieldText
                            : ColorConstants.textFieldPlaceholder,
                        letterSpacing: -0.14,
                      ),
                    ),
                  ),
                  
                  // Trailing dropdown icon
                  const Icon(
                    Icons.arrow_drop_down,
                    color: ColorConstants.textFieldIcon,
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // Error text (if any)
        if (errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            errorText!,
            style: const TextStyle(
              color: ColorConstants.inputError,
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }
}
