import 'package:flutter/material.dart';

/// Helper class for date/time picker operations
/// 
/// This class provides static methods for showing date and time pickers
/// with consistent behavior and validation logic
class CalendarDateTimePicker {
  CalendarDateTimePicker._();

  /// Show date and time picker for start date/time
  /// 
  /// Returns the selected DateTime or null if cancelled
  /// Automatically sets minimum date to current time
  static Future<DateTime?> showStartDateTimePicker(
    BuildContext context, {
    DateTime? initialDateTime,
  }) async {
    final now = DateTime.now();
    final initialDate = initialDateTime ?? now;

    // Show date picker
    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (date != null && context.mounted) {
      // Show time picker
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );

      if (time != null) {
        return DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );
      }
    }

    return null;
  }

  /// Show date and time picker for end date/time
  /// 
  /// Returns the selected DateTime or null if cancelled
  /// Sets minimum date based on start date/time
  static Future<DateTime?> showEndDateTimePicker(
    BuildContext context, {
    DateTime? initialDateTime,
    DateTime? startDateTime,
  }) async {
    final now = DateTime.now();
    final minDate = startDateTime ?? now;
    final initialDate = initialDateTime ?? minDate.add(const Duration(hours: 1));

    // Show date picker
    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: minDate,
      lastDate: now.add(const Duration(days: 365)),
    );

    if (date != null && context.mounted) {
      // Show time picker
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );

      if (time != null) {
        return DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );
      }
    }

    return null;
  }

  /// Auto-adjust end time if it's before start time
  /// 
  /// Returns adjusted end time that is at least 1 hour after start time
  static DateTime autoAdjustEndTime(DateTime startTime, DateTime? currentEndTime) {
    if (currentEndTime == null || !currentEndTime.isAfter(startTime)) {
      return startTime.add(const Duration(hours: 1));
    }
    return currentEndTime;
  }
}
