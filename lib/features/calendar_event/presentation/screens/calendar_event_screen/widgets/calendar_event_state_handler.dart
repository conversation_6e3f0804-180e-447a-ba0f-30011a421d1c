import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/constants/constants.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_state.dart';
import 'package:glidic_app/features/calendar_event/presentation/widgets/calendar_selection_dialog.dart';

/// State handler widget for calendar event screen
/// 
/// This widget handles all state-related UI reactions such as
/// success messages, error handling, and calendar selection dialogs
class CalendarEventStateHandler extends StatelessWidget {
  const CalendarEventStateHandler({
    super.key,
    required this.child,
  });

  /// Child widget to wrap with state handling
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocListener<CalendarEventCubit, CalendarEventState>(
      listener: (context, state) {
        if (state.isSuccess) {
          _handleSuccess(context);
        } else if (state.hasError) {
          _handleError(context, state.failure!);
        } else if (state.isCalendarSelectionRequired) {
          _handleCalendarSelection(
            context,
            state as CalendarEventCalendarSelectionRequired,
          );
        }
      },
      child: child,
    );
  }

  /// Handle successful event creation
  void _handleSuccess(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Event created successfully!',
        ), // TODO: Move to ARB localization
        backgroundColor: ColorConstants.successColor,
      ),
    );

    // Navigate back after a short delay
    Future.delayed(const Duration(seconds: 1), () {
      if (context.mounted) {
        AppNavigator.pop();
      }
    });
  }

  /// Handle error during event creation
  void _handleError(BuildContext context, AppFailure failure) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(failure.message),
        backgroundColor: ColorConstants.errorColor,
        action: failure.canRetry
            ? SnackBarAction(
                label: 'Retry', // TODO: Move to ARB localization
                textColor: ColorConstants.onError,
                onPressed: () {
                  context.read<CalendarEventCubit>().retry();
                },
              )
            : null,
      ),
    );
  }

  /// Handle calendar selection requirement (Android only)
  Future<void> _handleCalendarSelection(
    BuildContext context,
    CalendarEventCalendarSelectionRequired state,
  ) async {
    // Find default selection (prefer Google Calendar)
    String? defaultCalendarId;
    final googleCalendars = state.availableCalendars
        .where((cal) => cal.isGoogleCalendar)
        .toList();
    if (googleCalendars.isNotEmpty) {
      defaultCalendarId = googleCalendars.first.id;
    }

    // Get cubit reference before async operation
    final cubit = context.read<CalendarEventCubit>();

    // Show calendar selection dialog
    final selectedCalendar = await CalendarSelectionDialog.show(
      context,
      calendars: state.availableCalendars,
      selectedCalendarId: defaultCalendarId,
      title: 'Select Calendar', // TODO: Move to ARB localization
    );

    if (selectedCalendar != null) {
      // User selected a calendar
      cubit.onCalendarSelected(selectedCalendar, state.eventData);
    } else {
      // User cancelled the dialog
      cubit.onCalendarSelectionCancelled();
    }
  }
}
