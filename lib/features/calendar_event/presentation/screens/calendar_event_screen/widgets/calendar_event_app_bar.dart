import 'package:flutter/material.dart';
import 'package:glidic_app/core/presentation/router/app_navigator.dart';

/// Custom app bar for calendar event screen
/// 
/// This widget provides a consistent app bar with proper navigation
/// and title for the calendar event creation screen
class CalendarEventAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CalendarEventAppBar({
    super.key,
    this.title = 'Create Calendar Event',
  });

  /// App bar title text
  final String title;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title), // TODO: Move to ARB localization
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => AppNavigator.pop(),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
