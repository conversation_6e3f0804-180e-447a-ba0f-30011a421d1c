import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/presentation/widgets/buttons/primary_button.dart';
import 'package:glidic_app/core/presentation/widgets/inputs/primary_text_field.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_state.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/widgets/calendar_date_time_field.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/widgets/calendar_date_time_picker.dart';

/// Main calendar event form widget
/// 
/// This widget contains all form fields and handles user input
/// for creating calendar events
class CalendarEventForm extends StatefulWidget {
  const CalendarEventForm({super.key});

  @override
  State<CalendarEventForm> createState() => _CalendarEventFormState();
}

class _CalendarEventFormState extends State<CalendarEventForm> {
  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  // Date/time values
  DateTime? _startDateTime;
  DateTime? _endDateTime;

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Widget-specific constants
  static const double _fieldSpacing = 16.0;
  static const double _sectionSpacing = 24.0;
  static const double _horizontalPadding = 16.0;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarEventCubit, CalendarEventState>(
      builder: (context, state) {
        return Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(_horizontalPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: _sectionSpacing),

                // Title field
                PrimaryTextField(
                  controller: _titleController,
                  labelText: 'Event Title', // TODO: Move to ARB localization
                  hintText: 'Enter event title', // TODO: Move to ARB localization
                  errorText: state.validationErrors['title'],
                  prefixIcon: const Icon(Icons.title),
                  onChanged: (_) => _clearValidationIfNeeded(state),
                ),

                const SizedBox(height: _fieldSpacing),

                // Start date/time field
                CalendarDateTimeField(
                  label: 'Start Date & Time', // TODO: Move to ARB localization
                  value: _startDateTime,
                  errorText: state.validationErrors['startDateTime'],
                  onTap: _selectStartDateTime,
                  icon: Icons.event_available,
                  placeholder: 'Select start date and time', // TODO: Move to ARB localization
                ),

                const SizedBox(height: _fieldSpacing),

                // End date/time field
                CalendarDateTimeField(
                  label: 'End Date & Time', // TODO: Move to ARB localization
                  value: _endDateTime,
                  errorText: state.validationErrors['endDateTime'],
                  onTap: _selectEndDateTime,
                  icon: Icons.event_busy,
                  placeholder: 'Select end date and time', // TODO: Move to ARB localization
                ),

                const SizedBox(height: _fieldSpacing),

                // Description field (optional)
                PrimaryTextField(
                  controller: _descriptionController,
                  labelText: 'Description (Optional)', // TODO: Move to ARB localization
                  hintText: 'Enter event description', // TODO: Move to ARB localization
                  prefixIcon: const Icon(Icons.description),
                  maxLines: 3,
                ),

                const SizedBox(height: _fieldSpacing),

                // Location field (optional)
                PrimaryTextField(
                  controller: _locationController,
                  labelText: 'Location (Optional)', // TODO: Move to ARB localization
                  hintText: 'Enter event location', // TODO: Move to ARB localization
                  prefixIcon: const Icon(Icons.location_on),
                ),

                const SizedBox(height: _sectionSpacing),

                // Create event button
                PrimaryButton(
                  text: 'Create Event', // TODO: Move to ARB localization
                  onPressed: _canSubmit(state) ? _submitForm : null,
                  isLoading: state.isLoading,
                  isEnabled: _canSubmit(state),
                  icon: Icons.event,
                ),

                const SizedBox(height: _sectionSpacing),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Select start date and time
  Future<void> _selectStartDateTime() async {
    final selectedDateTime = await CalendarDateTimePicker.showStartDateTimePicker(
      context,
      initialDateTime: _startDateTime,
    );

    if (selectedDateTime != null) {
      setState(() {
        _startDateTime = selectedDateTime;
        
        // Auto-adjust end time if needed
        _endDateTime = CalendarDateTimePicker.autoAdjustEndTime(
          _startDateTime!,
          _endDateTime,
        );
      });

      if (mounted) {
        _clearValidationIfNeeded(context.read<CalendarEventCubit>().state);
      }
    }
  }

  /// Select end date and time
  Future<void> _selectEndDateTime() async {
    final selectedDateTime = await CalendarDateTimePicker.showEndDateTimePicker(
      context,
      initialDateTime: _endDateTime,
      startDateTime: _startDateTime,
    );

    if (selectedDateTime != null) {
      setState(() {
        _endDateTime = selectedDateTime;
      });

      if (mounted) {
        _clearValidationIfNeeded(context.read<CalendarEventCubit>().state);
      }
    }
  }

  /// Check if form can be submitted
  ///
  /// Returns true if all required fields are filled and form is not loading
  bool _canSubmit(CalendarEventState state) {
    return !state.isLoading &&
        _titleController.text.trim().isNotEmpty &&
        _startDateTime != null &&
        _endDateTime != null;
  }

  /// Submit the form
  Future<void> _submitForm() async {
    if (_startDateTime == null || _endDateTime == null) {
      return;
    }

    final cubit = context.read<CalendarEventCubit>();

    await cubit.createEventWithPlatformSelection(
      title: _titleController.text.trim(),
      startDateTime: _startDateTime!,
      endDateTime: _endDateTime!,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      location: _locationController.text.trim().isEmpty
          ? null
          : _locationController.text.trim(),
    );
  }

  /// Clear validation errors if needed
  ///
  /// This should be called when user makes changes to form fields
  void _clearValidationIfNeeded(CalendarEventState state) {
    if (state.isFormValidation) {
      context.read<CalendarEventCubit>().clearValidation();
    }
  }
}
