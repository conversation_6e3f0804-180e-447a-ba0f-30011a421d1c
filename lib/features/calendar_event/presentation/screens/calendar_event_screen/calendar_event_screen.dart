import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_cubit.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/widgets/calendar_event_app_bar.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/widgets/calendar_event_form.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/widgets/calendar_event_state_handler.dart';

/// Calendar event screen (View in MVVM pattern)
/// 
/// This screen demonstrates Clean Architecture implementation with MVVM pattern.
/// The screen is now refactored into smaller, focused widgets for better
/// maintainability, reusability, and readability.
/// 
/// Components:
/// - CalendarEventAppBar: Custom app bar with navigation
/// - CalendarEventForm: Main form with all input fields
/// - CalendarEventStateHandler: Handles state reactions (success, error, calendar selection)
@RoutePage()
class CalendarEventScreen extends StatelessWidget implements AutoRouteWrapper {
  const CalendarEventScreen({super.key});

  @override
  Widget wrappedRoute(BuildContext context) {
    return BlocProvider(
      create: (context) => CalendarEventCubit(
        createCalendarEventUseCase: sl(),
        getAvailableCalendarsUseCase: sl(),
      ),
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return const CalendarEventStateHandler(
      child: Scaffold(
        appBar: CalendarEventAppBar(),
        body: CalendarEventForm(),
      ),
    );
  }
}
