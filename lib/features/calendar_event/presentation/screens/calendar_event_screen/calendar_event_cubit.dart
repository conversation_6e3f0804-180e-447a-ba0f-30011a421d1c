import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_info_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/create_calendar_event_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/get_available_calendars_usecase.dart';
import 'package:glidic_app/features/calendar_event/presentation/screens/calendar_event_screen/calendar_event_state.dart';

/// Calendar event screen cubit (ViewModel in MVVM pattern)
/// Manages the state and business logic specific to the calendar event screen
class CalendarEventCubit extends Cubit<CalendarEventState> {
  CalendarEventCubit({
    required this.createCalendarEventUseCase,
    required this.getAvailableCalendarsUseCase,
  }) : super(const CalendarEventInitial());

  final CreateCalendarEventUseCase createCalendarEventUseCase;
  final GetAvailableCalendarsUseCase getAvailableCalendarsUseCase;

  /// Create a calendar event with the provided data
  Future<void> createEvent({
    required String title,
    required DateTime startDateTime,
    required DateTime endDateTime,
    String? description,
    String? location,
    String? calendarId,
  }) async {
    // Validate form data
    final validationErrors = _validateForm(
      title: title,
      startDateTime: startDateTime,
      endDateTime: endDateTime,
    );

    if (validationErrors.isNotEmpty) {
      emit(CalendarEventFormValidation(errors: validationErrors));
      return;
    }

    // Emit loading state
    emit(const CalendarEventLoading());

    try {
      // Create the event entity
      final event = CalendarEventEntity(
        title: title.trim(),
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        description:
            description?.trim().isEmpty == true ? null : description?.trim(),
        location: location?.trim().isEmpty == true ? null : location?.trim(),
      );

      // Execute create event use case
      final createdEvent = await createCalendarEventUseCase.execute(
        CreateCalendarEventParams(
          event: event,
          calendarId: calendarId,
        ),
      );

      // Emit success state
      emit(CalendarEventSuccess(event: createdEvent));
    } catch (e) {
      // Handle different types of failures
      if (e is AppFailure) {
        emit(
          CalendarEventError(
            failure: e,
            canRetry: e.canRetry,
          ),
        );
      } else {
        // Handle unexpected errors
        emit(
          CalendarEventError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Create event with platform-specific calendar selection logic
  ///
  /// This method handles the business logic for calendar selection:
  /// - On Android with multiple calendars: Emits calendar selection required state
  /// - On Android with single calendar: Uses it directly
  /// - On iOS: Bypasses all calendar selection logic and uses system default calendar
  /// - Handles all error scenarios gracefully
  Future<void> createEventWithPlatformSelection({
    required String title,
    required DateTime startDateTime,
    required DateTime endDateTime,
    String? description,
    String? location,
  }) async {
    // Validate form data first
    final validationErrors = _validateForm(
      title: title,
      startDateTime: startDateTime,
      endDateTime: endDateTime,
    );

    if (validationErrors.isNotEmpty) {
      emit(CalendarEventFormValidation(errors: validationErrors));
      return;
    }

    // Emit loading state
    emit(const CalendarEventLoading());

    try {
      // iOS: Bypass all calendar selection logic and use system default
      if (Platform.isIOS) {
        await _createEventDirectly(
          title: title,
          startDateTime: startDateTime,
          endDateTime: endDateTime,
          description: description,
          location: location,
        );
        return;
      }

      // Android: Handle calendar selection logic
      final calendars = await getAvailableCalendarsUseCase.execute();

      if (calendars.isEmpty) {
        // No calendars available
        emit(
          CalendarEventError(
            failure: AppFailureConstructors.calendarAccessFailed(
              'No writable calendars found on this device. Please add a calendar account and try again.',
            ),
            canRetry: true,
          ),
        );
        return;
      }

      // Create event data for calendar selection
      final eventData = CalendarEventData(
        title: title,
        startDateTime: startDateTime,
        endDateTime: endDateTime,
        description: description,
        location: location,
      );

      if (calendars.length == 1) {
        // Only one calendar available, use it directly
        await _createEventInCalendar(eventData, calendars.first.id);
      } else {
        // Multiple calendars on Android - emit state for UI to show selection dialog
        emit(
          CalendarEventCalendarSelectionRequired(
            availableCalendars: calendars,
            eventData: eventData,
          ),
        );
      }
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(CalendarEventError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          CalendarEventError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Handle calendar selection from UI
  /// Called when user selects a calendar from the selection dialog
  Future<void> onCalendarSelected(
      CalendarInfoEntity selectedCalendar, CalendarEventData eventData,) async {
    emit(const CalendarEventLoading());

    try {
      await _createEventInCalendar(eventData, selectedCalendar.id);
    } catch (e) {
      // Handle errors
      if (e is AppFailure) {
        emit(CalendarEventError(failure: e, canRetry: e.canRetry));
      } else {
        emit(
          CalendarEventError(
            failure: AppFailureConstructors.fromException(e),
            canRetry: true,
          ),
        );
      }
    }
  }

  /// Handle calendar selection cancellation
  /// Called when user cancels the calendar selection dialog
  void onCalendarSelectionCancelled() {
    emit(const CalendarEventInitial());
  }

  /// Create event directly without calendar selection (iOS only)
  ///
  /// This method bypasses all calendar selection logic and directly calls
  /// the CreateCalendarEventUseCase, letting iOS handle calendar selection natively
  Future<void> _createEventDirectly({
    required String title,
    required DateTime startDateTime,
    required DateTime endDateTime,
    String? description,
    String? location,
  }) async {
    // Create the event entity
    final event = CalendarEventEntity(
      title: title.trim(),
      startDateTime: startDateTime,
      endDateTime: endDateTime,
      description: description?.trim().isEmpty == true ? null : description?.trim(),
      location: location?.trim().isEmpty == true ? null : location?.trim(),
    );

    // Execute create event use case without specifying calendarId
    // This lets iOS use the system default calendar
    final createdEvent = await createCalendarEventUseCase.execute(
      CreateCalendarEventParams(
        event: event,
        calendarId: null, // Let iOS handle calendar selection
      ),
    );

    // Emit success state
    emit(CalendarEventSuccess(event: createdEvent));
  }

  /// Create event in specific calendar using event data
  Future<void> _createEventInCalendar(
    CalendarEventData eventData,
    String calendarId,
  ) async {
    // Create the event entity
    final event = CalendarEventEntity(
      title: eventData.title.trim(),
      startDateTime: eventData.startDateTime,
      endDateTime: eventData.endDateTime,
      description: eventData.description?.trim().isEmpty == true
          ? null
          : eventData.description?.trim(),
      location: eventData.location?.trim().isEmpty == true
          ? null
          : eventData.location?.trim(),
    );

    // Execute create event use case
    final createdEvent = await createCalendarEventUseCase.execute(
      CreateCalendarEventParams(
        event: event,
        calendarId: calendarId,
      ),
    );

    // Emit success state
    emit(CalendarEventSuccess(event: createdEvent));
  }

  /// Reset to initial state
  void reset() {
    emit(const CalendarEventInitial());
  }

  /// Clear form validation errors
  void clearValidation() {
    if (state.isFormValidation) {
      emit(const CalendarEventInitial());
    }
  }

  /// Retry the last failed operation
  void retry() {
    if (state.hasError && state.canRetryError) {
      emit(const CalendarEventInitial());
    }
  }

  /// Validate form data and return validation errors
  Map<String, String> _validateForm({
    required String title,
    required DateTime startDateTime,
    required DateTime endDateTime,
  }) {
    final errors = <String, String>{};

    // Validate title
    if (title.trim().isEmpty) {
      errors['title'] =
          'Event title is required'; // TODO: Move to ARB localization
    }

    // Validate date/time logic
    if (!endDateTime.isAfter(startDateTime)) {
      errors['endDateTime'] =
          'End time must be after start time'; // TODO: Move to ARB localization
    }

    // Validate that start time is not in the past (optional business rule)
    final now = DateTime.now();
    if (startDateTime.isBefore(now.subtract(const Duration(minutes: 1)))) {
      errors['startDateTime'] =
          'Start time cannot be in the past'; // TODO: Move to ARB localization
    }

    return errors;
  }
}
