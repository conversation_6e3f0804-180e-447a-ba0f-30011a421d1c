import 'package:glidic_app/features/calendar_event/data/datasources/interfaces/google_calendar_datasource.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/google_calendar_repository.dart';

/// Implementation of GoogleCalendarRepository
class GoogleCalendarRepositoryImpl implements GoogleCalendarRepository {
  const GoogleCalendarRepositoryImpl({
    required GoogleCalendarDataSource dataSource,
  }) : _dataSource = dataSource;

  final GoogleCalendarDataSource _dataSource;

  @override
  Future<bool> authenticate() async {
    try {
      return await _dataSource.authenticate();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      return await _dataSource.isAuthenticated();
    } catch (e) {
      // Return false if there's any error checking authentication
      return false;
    }
  }

  @override
  Future<bool> signOut() async {
    try {
      return await _dataSource.signOut();
    } catch (e) {
      // Return false if there's any error signing out
      return false;
    }
  }

  @override
  Future<GoogleCalendarEventEntity> createEvent(
    GoogleCalendarEventEntity event,
  ) async {
    try {
      return await _dataSource.createEvent(event);
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<String> getPrimaryCalendarId() async {
    try {
      return await _dataSource.getPrimaryCalendarId();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getCalendarInfo() async {
    try {
      return await _dataSource.getCalendarInfo();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<List<GoogleCalendarEventEntity>> getEvents({
    String? calendarId,
    DateTime? startDate,
    DateTime? endDate,
    int? maxResults,
  }) async {
    try {
      return await _dataSource.getEvents(
        calendarId: calendarId,
        startDate: startDate,
        endDate: endDate,
        maxResults: maxResults,
      );
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<GoogleCalendarEventEntity> getEvent(
    String eventId, {
    String? calendarId,
  }) async {
    try {
      return await _dataSource.getEvent(eventId, calendarId: calendarId);
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<GoogleCalendarEventEntity> updateEvent(
    GoogleCalendarEventEntity event,
  ) async {
    try {
      return await _dataSource.updateEvent(event);
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<bool> deleteEvent(String eventId, {String? calendarId}) async {
    try {
      return await _dataSource.deleteEvent(eventId, calendarId: calendarId);
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getCalendars() async {
    try {
      return await _dataSource.getCalendars();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }
}
