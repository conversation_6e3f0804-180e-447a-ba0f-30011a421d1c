import 'package:glidic_app/features/calendar_event/data/datasources/interfaces/calendar_datasource.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/calendar_event_repository.dart';

/// Implementation of CalendarEventRepository
/// This class implements the repository interface defined in the domain layer
/// Following the dependency inversion principle of Clean Architecture
class CalendarEventRepositoryImpl implements CalendarEventRepository {
  const CalendarEventRepositoryImpl({
    required CalendarDataSource dataSource,
  }) : _dataSource = dataSource;

  final CalendarDataSource _dataSource;

  @override
  Future<CalendarEventEntity> createEvent(CalendarEventEntity event) async {
    try {
      // Get the default calendar ID
      final calendarId = await _dataSource.getDefaultCalendarId();

      // Create the event using the data source
      return await _dataSource.createEvent(event, calendarId);
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<CalendarEventEntity> createEventInCalendar(
    CalendarEventEntity event,
    String calendarId,
  ) async {
    try {
      // Create the event in the specified calendar using the data source
      return await _dataSource.createEvent(event, calendarId);
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<bool> hasCalendarPermissions() async {
    try {
      return await _dataSource.hasCalendarPermissions();
    } catch (e) {
      // If permission check fails, assume no permissions
      return false;
    }
  }

  @override
  Future<bool> requestCalendarPermissions() async {
    try {
      return await _dataSource.requestCalendarPermissions();
    } catch (e) {
      // If permission request fails, return false
      return false;
    }
  }

  @override
  Future<String> getDefaultCalendarId() async {
    try {
      return await _dataSource.getDefaultCalendarId();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<String> getGoogleCalendarId() async {
    try {
      return await _dataSource.getGoogleCalendarId();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAvailableCalendars() async {
    try {
      return await _dataSource.getAvailableCalendars();
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }

  @override
  Future<List<CalendarEventEntity>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  }) async {
    try {
      return await _dataSource.getCalendarEvents(
        startDate: startDate,
        endDate: endDate,
        calendarIds: calendarIds,
      );
    } catch (e) {
      // Re-throw any AppFailure exceptions
      rethrow;
    }
  }
}
