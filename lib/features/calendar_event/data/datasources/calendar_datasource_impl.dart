import 'dart:io';

import 'package:device_calendar/device_calendar.dart';
import 'package:glidic_app/core/common/constants/error_codes.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/data/datasources/interfaces/calendar_datasource.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';

/// Implementation of [CalendarDataSource] using device_calendar plugin
/// This service handles all device calendar operations using the device_calendar plugin
class CalendarDataSourceImpl implements CalendarDataSource {
  CalendarDataSourceImpl() : _deviceCalendarPlugin = DeviceCalendarPlugin();

  final DeviceCalendarPlugin _deviceCalendarPlugin;

  @override
  Future<bool> hasCalendarPermissions() async {
    try {
      final result = await _deviceCalendarPlugin.hasPermissions();
      return result.isSuccess && (result.data ?? false);
    } catch (e) {
      // If permission check fails, assume no permissions
      return false;
    }
  }

  @override
  Future<bool> requestCalendarPermissions() async {
    try {
      // First check if permissions are already granted
      final hasPermResult = await _deviceCalendarPlugin.hasPermissions();
      if (hasPermResult.isSuccess && (hasPermResult.data ?? false)) {
        return true;
      }

      // Request permissions using device_calendar plugin
      final requestResult = await _deviceCalendarPlugin.requestPermissions();
      return requestResult.isSuccess && (requestResult.data ?? false);
    } catch (e) {
      // If permission request fails, return false
      return false;
    }
  }

  @override
  Future<String> getDefaultCalendarId() async {
    try {
      // Check permissions first before accessing calendars
      final hasPermissions = await hasCalendarPermissions();
      if (!hasPermissions) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }

      final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();

      if (calendarsResult.isSuccess && calendarsResult.data != null) {
        final calendars = calendarsResult.data!;

        // On Android, prefer Google Calendar if available
        if (Platform.isAndroid) {
          try {
            final googleCalendarId = await _findGoogleCalendarId(calendars);
            return googleCalendarId;
          } catch (e) {
            // If Google Calendar is not found or not available, fall back to default logic
            // Log the issue but continue with fallback
            if (e is AppFailure &&
                (e.code == ErrorCodes.googleCalendarNotFound ||
                    e.code == ErrorCodes.googleCalendarNotAvailable)) {
              // Continue to fallback logic below
            } else {
              rethrow;
            }
          }
        }

        // Fallback logic: Find the default calendar (usually the first writable calendar)
        final defaultCalendar = calendars.firstWhere(
          (calendar) =>
              calendar.isDefault == true && calendar.isReadOnly == false,
          orElse: () => calendars.firstWhere(
            (calendar) => calendar.isReadOnly == false,
            orElse: () => throw AppFailureConstructors.calendarAccessFailed(
              'No writable calendar found on device',
            ),
          ),
        );

        return defaultCalendar.id!;
      } else {
        throw AppFailureConstructors.calendarAccessFailed(
          'Failed to retrieve calendars: ${calendarsResult.errors.join(', ')}',
        );
      }
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarAccessFailed(
        'Failed to get default calendar: ${e.toString()}',
      );
    }
  }

  @override
  Future<String> getGoogleCalendarId() async {
    try {
      // Check permissions first before accessing calendars
      final hasPermissions = await hasCalendarPermissions();
      if (!hasPermissions) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }

      // This method is primarily for Android, but we'll check platform
      if (!Platform.isAndroid) {
        throw AppFailureConstructors.calendarAccessFailed(
          'Google Calendar detection is only supported on Android',
        );
      }

      final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();

      if (calendarsResult.isSuccess && calendarsResult.data != null) {
        final calendars = calendarsResult.data!;
        return await _findGoogleCalendarId(calendars);
      } else {
        throw AppFailureConstructors.calendarAccessFailed(
          'Failed to retrieve calendars: ${calendarsResult.errors.join(', ')}',
        );
      }
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarAccessFailed(
        'Failed to get Google Calendar: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getAvailableCalendars() async {
    try {
      // Check permissions first before accessing calendars
      final hasPermissions = await hasCalendarPermissions();
      if (!hasPermissions) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }

      final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();

      if (calendarsResult.isSuccess && calendarsResult.data != null) {
        final calendars = calendarsResult.data!;

        return calendars
            .map(
              (calendar) => {
                'id': calendar.id,
                'name': calendar.name,
                'accountName': calendar.accountName,
                'accountType': calendar.accountType,
                'isDefault': calendar.isDefault,
                'isReadOnly': calendar.isReadOnly,
                'color': calendar.color,
              },
            )
            .toList();
      } else {
        throw AppFailureConstructors.calendarAccessFailed(
          'Failed to retrieve calendars: ${calendarsResult.errors.join(', ')}',
        );
      }
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarAccessFailed(
        'Failed to get available calendars: ${e.toString()}',
      );
    }
  }

  @override
  Future<CalendarEventEntity> createEvent(
    CalendarEventEntity eventEntity,
    String calendarId,
  ) async {
    try {
      // Check permissions first before creating event
      final hasPermissions = await hasCalendarPermissions();
      if (!hasPermissions) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }

      // Convert domain entity to device_calendar Event
      final event = Event(
        calendarId,
        title: eventEntity.title,
        description: eventEntity.description,
        start: TZDateTime.from(eventEntity.startDateTime, getLocation('UTC')),
        end: TZDateTime.from(eventEntity.endDateTime, getLocation('UTC')),
        location: eventEntity.location,
      );

      // Create the event
      final createResult =
          await _deviceCalendarPlugin.createOrUpdateEvent(event);

      if (createResult?.isSuccess == true) {
        // Return the original entity (device calendar doesn't return the created event details)
        return eventEntity;
      } else {
        final errorMessage =
            createResult?.errors.join(', ') ?? 'Unknown error occurred';
        throw AppFailureConstructors.calendarEventCreationFailed(errorMessage);
      }
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarEventCreationFailed(
        'Failed to create calendar event: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<CalendarEventEntity>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  }) async {
    try {
      // Check permissions first before retrieving events
      final hasPermissions = await hasCalendarPermissions();
      if (!hasPermissions) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }

      // Set default date range if not provided (30 days ago to 30 days from now)
      final now = DateTime.now();
      final defaultStartDate =
          startDate ?? now.subtract(const Duration(days: 30));
      final defaultEndDate = endDate ?? now.add(const Duration(days: 30));

      // Get calendars to retrieve events from
      List<String> targetCalendarIds;
      if (calendarIds != null && calendarIds.isNotEmpty) {
        targetCalendarIds = calendarIds;
      } else {
        // Get all available calendars
        final calendarsResult = await _deviceCalendarPlugin.retrieveCalendars();
        if (calendarsResult.isSuccess && calendarsResult.data != null) {
          targetCalendarIds =
              calendarsResult.data!.map((calendar) => calendar.id!).toList();
        } else {
          throw AppFailureConstructors.calendarAccessFailed(
            'Failed to retrieve calendars: ${calendarsResult.errors.join(', ')}',
          );
        }
      }

      // Retrieve events from all target calendars
      final List<CalendarEventEntity> allEvents = [];

      for (final calendarId in targetCalendarIds) {
        final eventsResult = await _deviceCalendarPlugin.retrieveEvents(
          calendarId,
          RetrieveEventsParams(
            startDate: defaultStartDate,
            endDate: defaultEndDate,
          ),
        );

        if (eventsResult.isSuccess && eventsResult.data != null) {
          // Convert device_calendar Events to CalendarEventEntity
          final events = eventsResult.data!
              .map((event) => _convertToCalendarEventEntity(event))
              .toList();
          allEvents.addAll(events);
        }
      }

      // Sort events by start date
      allEvents.sort((a, b) => a.startDateTime.compareTo(b.startDateTime));

      return allEvents;
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarEventRetrievalFailed(
        e.toString(),
      );
    }
  }

  /// Find Google Calendar ID from the list of available calendars
  ///
  /// This method attempts to identify Google Calendar by examining calendar properties
  /// such as account name, account type, and calendar name patterns.
  ///
  /// Throws [AppFailure] if Google Calendar is not found or not available
  Future<String> _findGoogleCalendarId(List<Calendar> calendars) async {
    // Common patterns to identify Google Calendar
    final googlePatterns = [
      'gmail.com',
      'google.com',
      '@gmail.com',
      '@google.com',
    ];

    // First, try to find calendars with Google account patterns
    // Priority 1: Strict Google account type matching (most reliable)
    final strictGoogleCalendars = calendars.where((calendar) {
      final accountType = calendar.accountType?.toLowerCase() ?? '';
      return accountType == 'com.google' ||
          accountType == 'com.google.android.gm';
    }).toList();

    // Priority 2: Google account name patterns (fallback)
    final patternGoogleCalendars = calendars.where((calendar) {
      final accountName = calendar.accountName?.toLowerCase() ?? '';
      final accountType = calendar.accountType?.toLowerCase() ?? '';

      // Exclude LOCAL calendars explicitly
      if (accountType == 'local' || accountType.isEmpty) {
        return false;
      }

      // Check if account name contains Google patterns
      final hasGoogleAccount = googlePatterns.any(
        (pattern) => accountName.contains(pattern),
      );

      // Check if account type indicates Google (but not strict match)
      final hasGoogleAccountType =
          accountType.contains('google') || accountType.contains('gmail');

      return hasGoogleAccount || hasGoogleAccountType;
    }).toList();

    // Combine results with priority (strict matches first)
    final googleCalendars = <Calendar>[];
    googleCalendars.addAll(strictGoogleCalendars);

    // Add pattern matches only if they're not already in strict matches
    for (final calendar in patternGoogleCalendars) {
      if (!strictGoogleCalendars.any((strict) => strict.id == calendar.id)) {
        googleCalendars.add(calendar);
      }
    }

    if (googleCalendars.isEmpty) {
      throw AppFailureConstructors.googleCalendarNotFound();
    }

    // Find the best writable Google calendar with priority selection
    Calendar? selectedCalendar;

    // Priority 1: Strict Google calendars that are writable
    for (final calendar in strictGoogleCalendars) {
      if (calendar.isReadOnly == false) {
        selectedCalendar = calendar;
        break;
      }
    }

    // Priority 2: Pattern-matched Google calendars that are writable (fallback)
    if (selectedCalendar == null) {
      for (final calendar in patternGoogleCalendars) {
        if (calendar.isReadOnly == false &&
            !strictGoogleCalendars.any((strict) => strict.id == calendar.id)) {
          selectedCalendar = calendar;
          break;
        }
      }
    }

    // If no writable calendar found, throw appropriate error
    if (selectedCalendar == null) {
      throw AppFailureConstructors.googleCalendarNotAvailable();
    }

    // Ensure the selected calendar has a valid ID
    if (selectedCalendar.id == null || selectedCalendar.id!.isEmpty) {
      throw AppFailureConstructors.calendarAccessFailed(
        'Google Calendar found but has invalid ID',
      );
    }

    return selectedCalendar.id!;
  }

  /// Convert device_calendar Event to CalendarEventEntity
  CalendarEventEntity _convertToCalendarEventEntity(Event event) {
    return CalendarEventEntity(
      title: event.title ?? 'Untitled Event',
      startDateTime: event.start?.toLocal() ?? DateTime.now(),
      endDateTime:
          event.end?.toLocal() ?? DateTime.now().add(const Duration(hours: 1)),
      description: event.description,
      location: event.location,
    );
  }
}
