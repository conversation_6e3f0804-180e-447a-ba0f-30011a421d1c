import 'dart:io';

import 'package:extension_google_sign_in_as_googleapis_auth/extension_google_sign_in_as_googleapis_auth.dart';
import 'package:glidic_app/core/common/config/env_config.dart';
import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/data/datasources/interfaces/google_calendar_datasource.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:googleapis/calendar/v3.dart' as calendar;

/// Implementation of [GoogleCalendarDataSource] using Google APIs
class GoogleCalendarDataSourceImpl implements GoogleCalendarDataSource {
  GoogleCalendarDataSourceImpl() {
    _initializeGoogleSignIn();
  }

  // =============================================================================
  // CONSTANTS
  // =============================================================================

  /// OAuth Configuration Constants
  static const String _kClientIdPlaceholder =
      'your_google_oauth_client_id_here';
  static const String _kServerClientIdPlaceholder =
      'your_google_oauth_server_client_id_here';
  static const String _kGoogleAuthDomain = '.apps.googleusercontent.com';

  /// Calendar API Constants
  static const String _kPrimaryCalendarId = 'primary';
  static const String _kUtcTimeZone = 'UTC';
  static const String _kReminderMethodPopup = 'popup';
  static const String _kEventOrderBy = 'startTime';
  static const int _kDefaultMaxResults = 100;
  static const int _kDefaultDateRangeDays = 30;
  static const int _kDefaultEventDurationHours = 1;

  /// Default Values
  static const String _kDefaultEventTitle = 'Untitled Event';

  /// Error Keywords for Detection
  static const String _kErrorCanceled = 'canceled';
  static const String _kErrorCancelled = 'cancelled';
  static const String _kErrorUserCanceled = 'user_canceled';
  static const String _kErrorUserCancelled = 'user_cancelled';
  static const String _kErrorActivityCancelled =
      'activity is cancelled by the user';
  static const String _kErrorSignInCanceled = 'sign_in_canceled';
  static const String _kErrorSignInCancelled = 'sign_in_cancelled';
  static const String _kErrorOperationCanceled = 'operation_canceled';
  static const String _kErrorOperationCancelled = 'operation_cancelled';
  static const String _kErrorNetwork = 'network';
  static const String _kErrorConnection = 'connection';
  static const String _kErrorTimeout = 'timeout';
  static const String _kErrorConfiguration = 'configuration';
  static const String _kErrorNotSupported = 'not supported';
  static const String _kErrorQuota = 'quota';
  static const String _kErrorRateLimit = 'rate limit';
  static const String _kErrorPermission = 'permission';
  static const String _kErrorAccessDenied = 'access denied';

  /// Error Messages
  static const String _kErrorEventCreationNoId =
      'Event was created but no ID was returned';
  static const String _kErrorUpdateWithoutId =
      'Cannot update event without event ID';

  /// Calendar Info Map Keys
  static const String _kCalendarInfoKeyId = 'id';
  static const String _kCalendarInfoKeySummary = 'summary';
  static const String _kCalendarInfoKeyDescription = 'description';
  static const String _kCalendarInfoKeyTimeZone = 'timeZone';
  static const String _kCalendarInfoKeyPrimary = 'primary';
  static const String _kCalendarInfoKeyAccessRole = 'accessRole';
  static const String _kCalendarInfoKeyBackgroundColor = 'backgroundColor';
  static const String _kCalendarInfoKeyForegroundColor = 'foregroundColor';

  // =============================================================================
  // INSTANCE VARIABLES
  // =============================================================================

  /// Google Sign-In instance
  late final GoogleSignIn _googleSignIn;

  /// Google Calendar API client
  calendar.CalendarApi? _calendarApi;

  /// Authentication state cache
  bool? _isAuthenticatedCache;

  /// Calendar API scopes
  static const List<String> _calendarScopes = [
    calendar.CalendarApi.calendarScope, // Full access to Calendar API
  ];

  /// Gets Google OAuth Client ID based on platform
  String get _clientId {
    if (Platform.isIOS) {
      return EnvConfig.googleOAuthIosClientId;
    } else if (Platform.isAndroid) {
      return EnvConfig.googleOAuthClientId;
    }
    return EnvConfig.googleOAuthClientId; // Fallback
  }

  /// Gets Google OAuth Server Client ID
  /// Returns null for iOS platform since it's not required for iOS Google OAuth implementation
  String? get _serverClientId {
    if (Platform.isIOS) {
      return null;
    }
    // Required for Android and other platforms
    return EnvConfig.googleOAuthServerClientId;
  }

  // =============================================================================
  // CORE AUTHENTICATION METHODS
  // =============================================================================

  @override
  Future<bool> authenticate() async {
    try {
      // Clear any existing authentication cache since we're re-authenticating
      _clearAuthenticationCache();

      // Validate OAuth configuration before proceeding
      _validateOAuthConfiguration();

      // Initialize Google Sign-In with both Client ID and Server Client ID (required for v7.x)
      await _googleSignIn.initialize(
        clientId: _clientId,
        serverClientId: _serverClientId,
      );

      // Check if authentication is supported on this platform
      if (!_googleSignIn.supportsAuthenticate()) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      // First, try lightweight authentication to see if user is already signed in
      GoogleSignInAccount? user =
          await _googleSignIn.attemptLightweightAuthentication();

      // Authenticate the user with full flow and Calendar scope hints if needed
      // This helps Google show a combined consent screen for both sign-in and Calendar access
      // Uses fallback strategy: full scopes -> read-only scopes -> no scope hints
      user ??= await _authenticateWithScopeHints();

      // Use the helper method to handle scope authorization with fallback
      GoogleSignInClientAuthorization? authorization;
      try {
        authorization = await _authorizeCalendarScopes(user);

        // Check if user cancelled the authorization (null return indicates cancellation)
        if (authorization == null) {
          return false; // User cancelled scope authorization, not an error
        }
      } catch (scopeError) {
        // Handle scope authorization cancellation specifically
        final scopeErrorMessage = scopeError.toString().toLowerCase();
        if (_isCancellationError(scopeErrorMessage)) {
          return false; // User cancelled scope authorization, not an error
        }

        // Re-throw other scope authorization errors
        rethrow;
      }

      // Create authenticated client using the extension with Calendar scopes
      final authClient = authorization.authClient(scopes: _calendarScopes);

      // Create Calendar API client with authenticated client
      _calendarApi = calendar.CalendarApi(authClient);

      // Cache the successful authentication
      _updateAuthenticationCache(true);

      return true;
    } catch (e) {
      return _handleAuthenticationError(e);
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    // Check if we have a cached authentication state
    // This avoids redundant checks within the same session
    if (_isAuthenticationCacheValid()) {
      return _isAuthenticatedCache!;
    }

    try {
      // Validate OAuth configuration before proceeding
      _validateOAuthConfiguration();

      // Initialize Google Sign-In with both Client ID and Server Client ID (required for v7.x)
      await _googleSignIn.initialize(
        clientId: _clientId,
        serverClientId: _serverClientId,
      );

      // Use lightweight authentication to check current session
      // This leverages Google Sign-In SDK's native session management
      final user = await _googleSignIn.attemptLightweightAuthentication();
      if (user == null) {
        _updateAuthenticationCache(false);
        return false;
      }

      // Check if we have valid authorization for Calendar API scopes
      // This respects Google's token management and scope validation
      final authorization = await user.authorizationClient
          .authorizationForScopes(_calendarScopes);

      if (authorization == null || authorization.accessToken.isEmpty) {
        _updateAuthenticationCache(false);
        return false;
      }

      // Initialize Calendar API client if not already done
      if (_calendarApi == null) {
        final authClient = authorization.authClient(scopes: _calendarScopes);
        _calendarApi = calendar.CalendarApi(authClient);
      }

      // Cache the successful authentication state
      // Let Google Sign-In SDK manage token expiration and refresh
      _updateAuthenticationCache(true);
      return true;
    } catch (e) {
      // Cache the failed authentication state
      _updateAuthenticationCache(false);
      return false;
    }
  }

  @override
  Future<bool> signOut() async {
    try {
      await _googleSignIn.signOut();
      _calendarApi = null;
      _clearAuthenticationCache(); // Clear cache on sign out
      return true;
    } catch (e) {
      _clearAuthenticationCache(); // Clear cache even if sign out fails
      return false;
    }
  }

  // =============================================================================
  // PRIMARY CALENDAR OPERATIONS
  // =============================================================================

  @override
  Future<GoogleCalendarEventEntity> createEvent(
    GoogleCalendarEventEntity event,
  ) async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      // Get primary calendar ID
      final calendarId = await getPrimaryCalendarId();

      // Create Google Calendar Event
      final googleEvent = calendar.Event()
        ..summary = event.title
        ..description = event.description
        ..location = event.location
        ..start = calendar.EventDateTime()
        ..end = calendar.EventDateTime();

      // Set start time
      googleEvent.start!.dateTime = event.startDateTime;
      googleEvent.start!.timeZone = _kUtcTimeZone;

      // Set end time
      googleEvent.end!.dateTime = event.endDateTime;
      googleEvent.end!.timeZone = _kUtcTimeZone;

      // Set reminders if provided
      if (event.reminders != null && event.reminders!.isNotEmpty) {
        googleEvent.reminders = calendar.EventReminders()
          ..useDefault = false
          ..overrides = event.reminders!
              .map(
                (minutes) => calendar.EventReminder()
                  ..method = _kReminderMethodPopup
                  ..minutes = minutes,
              )
              .toList();
      }

      // Create the event
      final createdEvent =
          await _calendarApi!.events.insert(googleEvent, calendarId);

      if (createdEvent.id == null) {
        throw AppFailureConstructors.googleCalendarEventCreationFailed(
          _kErrorEventCreationNoId,
        );
      }

      // Return the event with the Google Calendar event ID
      return event.copyWith(
        eventId: createdEvent.id,
        calendarId: calendarId,
      );
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }

      // Handle specific Google API errors
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains(_kErrorQuota) ||
          errorMessage.contains(_kErrorRateLimit)) {
        throw AppFailureConstructors.googleCalendarQuotaExceeded();
      } else if (errorMessage.contains(_kErrorPermission) ||
          errorMessage.contains(_kErrorAccessDenied)) {
        throw AppFailureConstructors.googleCalendarAccessDenied();
      } else if (errorMessage.contains(_kErrorNetwork) ||
          errorMessage.contains(_kErrorConnection)) {
        throw AppFailureConstructors.noConnection();
      } else if (errorMessage.contains(_kErrorTimeout)) {
        throw AppFailureConstructors.timeout();
      }

      throw AppFailureConstructors.googleCalendarEventCreationFailed(
        e.toString(),
      );
    }
  }

  @override
  Future<List<GoogleCalendarEventEntity>> getEvents({
    String? calendarId,
    DateTime? startDate,
    DateTime? endDate,
    int? maxResults,
  }) async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      final targetCalendarId = calendarId ?? await getPrimaryCalendarId();

      // Set default date range if not provided (next 30 days)
      final now = DateTime.now();
      final defaultStartDate = startDate ?? now;
      final defaultEndDate =
          endDate ?? now.add(const Duration(days: _kDefaultDateRangeDays));

      final events = await _calendarApi!.events.list(
        targetCalendarId,
        timeMin: defaultStartDate,
        timeMax: defaultEndDate,
        maxResults: maxResults ?? _kDefaultMaxResults,
        singleEvents: true,
        orderBy: _kEventOrderBy,
      );

      if (events.items == null) {
        return [];
      }

      return events.items!.map((event) => _convertToEntity(event)).toList();
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarEventRetrievalFailed(e.toString());
    }
  }

  @override
  Future<GoogleCalendarEventEntity> getEvent(
    String eventId, {
    String? calendarId,
  }) async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      final targetCalendarId = calendarId ?? await getPrimaryCalendarId();
      final event = await _calendarApi!.events.get(targetCalendarId, eventId);

      return _convertToEntity(event);
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarEventRetrievalFailed(e.toString());
    }
  }

  @override
  Future<GoogleCalendarEventEntity> updateEvent(
    GoogleCalendarEventEntity event,
  ) async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      if (event.eventId == null) {
        throw AppFailureConstructors.googleCalendarEventCreationFailed(
          _kErrorUpdateWithoutId,
        );
      }

      final targetCalendarId = event.calendarId ?? await getPrimaryCalendarId();

      // Create Google Calendar Event from entity
      final googleEvent = calendar.Event()
        ..summary = event.title
        ..description = event.description
        ..location = event.location
        ..start = calendar.EventDateTime()
        ..end = calendar.EventDateTime();

      // Set start time
      googleEvent.start!.dateTime = event.startDateTime;
      googleEvent.start!.timeZone = _kUtcTimeZone;

      // Set end time
      googleEvent.end!.dateTime = event.endDateTime;
      googleEvent.end!.timeZone = _kUtcTimeZone;

      // Set reminders if provided
      if (event.reminders != null && event.reminders!.isNotEmpty) {
        googleEvent.reminders = calendar.EventReminders()
          ..useDefault = false
          ..overrides = event.reminders!
              .map(
                (minutes) => calendar.EventReminder()
                  ..method = _kReminderMethodPopup
                  ..minutes = minutes,
              )
              .toList();
      }

      // Update the event
      final updatedEvent = await _calendarApi!.events.update(
        googleEvent,
        targetCalendarId,
        event.eventId!,
      );

      return _convertToEntity(updatedEvent);
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.googleCalendarEventCreationFailed(
        e.toString(),
      );
    }
  }

  @override
  Future<bool> deleteEvent(String eventId, {String? calendarId}) async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      final targetCalendarId = calendarId ?? await getPrimaryCalendarId();
      await _calendarApi!.events.delete(targetCalendarId, eventId);

      return true;
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.calendarEventRetrievalFailed(e.toString());
    }
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  @override
  Future<String> getPrimaryCalendarId() async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      // Get the primary calendar (usually 'primary')
      return _kPrimaryCalendarId;
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.googleCalendarAccessDenied();
    }
  }

  @override
  Future<Map<String, dynamic>> getCalendarInfo() async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      final calendarId = await getPrimaryCalendarId();
      final calendarInfo = await _calendarApi!.calendars.get(calendarId);

      return {
        _kCalendarInfoKeyId: calendarInfo.id,
        _kCalendarInfoKeySummary: calendarInfo.summary,
        _kCalendarInfoKeyDescription: calendarInfo.description,
        _kCalendarInfoKeyTimeZone: calendarInfo.timeZone,
      };
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.googleCalendarAccessDenied();
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getCalendars() async {
    try {
      if (_calendarApi == null) {
        throw AppFailureConstructors.googleCalendarNotConfigured();
      }

      final calendarList = await _calendarApi!.calendarList.list();

      if (calendarList.items == null) {
        return [];
      }

      return calendarList.items!
          .map(
            (calendar) => {
              _kCalendarInfoKeyId: calendar.id,
              _kCalendarInfoKeySummary: calendar.summary,
              _kCalendarInfoKeyDescription: calendar.description,
              _kCalendarInfoKeyPrimary: calendar.primary ?? false,
              _kCalendarInfoKeyAccessRole: calendar.accessRole,
              _kCalendarInfoKeyBackgroundColor: calendar.backgroundColor,
              _kCalendarInfoKeyForegroundColor: calendar.foregroundColor,
            },
          )
          .toList();
    } catch (e) {
      if (e is AppFailure) {
        rethrow;
      }
      throw AppFailureConstructors.googleCalendarAccessDenied();
    }
  }

  /// Convert Google Calendar Event to domain entity
  ///
  /// Transforms a Google Calendar API event into our domain entity format
  /// with proper fallback values for missing data
  GoogleCalendarEventEntity _convertToEntity(calendar.Event googleEvent) {
    // Extract reminder minutes from event reminders
    final reminderMinutes = _extractReminderMinutes(googleEvent);

    // Provide fallback values for required fields
    final now = DateTime.now();
    final defaultEndTime =
        now.add(const Duration(hours: _kDefaultEventDurationHours));

    return GoogleCalendarEventEntity(
      title: googleEvent.summary ?? _kDefaultEventTitle,
      startDateTime: googleEvent.start?.dateTime ?? now,
      endDateTime: googleEvent.end?.dateTime ?? defaultEndTime,
      description: googleEvent.description,
      location: googleEvent.location,
      eventId: googleEvent.id,
      calendarId: googleEvent
          .organizer?.email, // Use organizer email as calendar identifier
      reminders: reminderMinutes,
    );
  }

  /// Extract reminder minutes from Google Calendar event reminders
  List<int>? _extractReminderMinutes(calendar.Event googleEvent) {
    final eventReminders = googleEvent.reminders;
    if (eventReminders?.overrides == null ||
        eventReminders!.overrides!.isEmpty) {
      return null;
    }

    return eventReminders.overrides!
        .where((reminder) => reminder.minutes != null)
        .map((reminder) => reminder.minutes!)
        .toList();
  }

  // =============================================================================
  // PRIVATE HELPER METHODS
  // =============================================================================

  /// Initialize Google Sign-In instance
  /// Configuration will be done during initialize() call with Client ID from environment
  void _initializeGoogleSignIn() {
    _googleSignIn = GoogleSignIn.instance;
  }

  /// Check if we have a cached authentication state
  ///
  /// Returns true if we have a cached authentication state.
  /// Relies on Google Sign-In SDK's native session management for validity.
  bool _isAuthenticationCacheValid() {
    return _isAuthenticatedCache != null;
  }

  /// Update the authentication cache with the current state
  ///
  /// Stores the authentication state without time-based expiration.
  /// Relies on Google Sign-In SDK's native session management.
  ///
  /// @param isAuthenticated The current authentication state to cache
  void _updateAuthenticationCache(bool isAuthenticated) {
    _isAuthenticatedCache = isAuthenticated;
  }

  /// Clear the authentication cache
  ///
  /// This should be called when explicit authentication state changes occur:
  /// - User signs out
  /// - Authentication fails
  /// - Re-authentication is triggered
  void _clearAuthenticationCache() {
    _isAuthenticatedCache = null;
  }

  /// Validate Google OAuth configuration (Client ID and Server Client ID)
  /// Throws [AppFailure] if configuration is invalid or missing
  void _validateOAuthConfiguration() {
    final clientId = _clientId;
    final serverClientId = _serverClientId;

    // Validate Client ID configuration
    _validateClientId(clientId);

    // Validate Server Client ID configuration (required for Android, optional for iOS)
    if (serverClientId != null) {
      _validateServerClientId(serverClientId);
    } else if (!Platform.isIOS) {
      // Server Client ID is required for non-iOS platforms
      throw AppFailureConstructors.googleCalendarNotConfigured();
    }
  }

  /// Validate the Google OAuth Client ID
  void _validateClientId(String clientId) {
    if (clientId.isEmpty ||
        clientId.contains(_kClientIdPlaceholder) ||
        !clientId.contains(_kGoogleAuthDomain)) {
      throw AppFailureConstructors.googleCalendarNotConfigured();
    }
  }

  /// Validate the Google OAuth Server Client ID
  void _validateServerClientId(String serverClientId) {
    if (serverClientId.isEmpty ||
        serverClientId.contains(_kServerClientIdPlaceholder) ||
        !serverClientId.contains(_kGoogleAuthDomain)) {
      throw AppFailureConstructors.googleCalendarNotConfigured();
    }
  }

  /// Helper method to handle scope authorization with improved error handling
  Future<GoogleSignInClientAuthorization?> _authorizeCalendarScopes(
    GoogleSignInAccount user,
  ) async {
    // First, check if we already have authorization for Calendar scopes
    var authorization =
        await user.authorizationClient.authorizationForScopes(_calendarScopes);
    if (authorization != null && authorization.accessToken.isNotEmpty) {
      return authorization;
    }

    // Try to authorize Calendar scopes with improved error handling
    try {
      authorization =
          await user.authorizationClient.authorizeScopes(_calendarScopes);
      return authorization;
    } catch (scopeError) {
      // Check if user cancelled the authorization
      final errorMessage = scopeError.toString().toLowerCase();
      if (_isCancellationError(errorMessage)) {
        return null; // Indicate cancellation
      }

      // If it's not a cancellation error, throw the error
      rethrow;
    }
  }

  /// Helper method to check if an error message indicates user cancellation
  bool _isCancellationError(String errorMessage) {
    return errorMessage.contains(_kErrorCanceled) ||
        errorMessage.contains(_kErrorCancelled) ||
        errorMessage.contains(_kErrorUserCanceled) ||
        errorMessage.contains(_kErrorUserCancelled) ||
        errorMessage.contains(_kErrorActivityCancelled) ||
        errorMessage.contains(_kErrorSignInCanceled) ||
        errorMessage.contains(_kErrorSignInCancelled) ||
        errorMessage.contains(_kErrorOperationCanceled) ||
        errorMessage.contains(_kErrorOperationCancelled);
  }

  /// Handle authentication errors and return appropriate response
  bool _handleAuthenticationError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    // Handle network-related errors
    if (errorMessage.contains(_kErrorNetwork) ||
        errorMessage.contains(_kErrorConnection)) {
      throw AppFailureConstructors.noConnection();
    }

    // Handle timeout errors
    if (errorMessage.contains(_kErrorTimeout)) {
      throw AppFailureConstructors.timeout();
    }

    // Handle user cancellation (not an error, just user choice)
    if (_isCancellationError(errorMessage)) {
      return false;
    }

    // Handle configuration errors
    if (errorMessage.contains(_kErrorConfiguration) ||
        errorMessage.contains(_kErrorNotSupported)) {
      throw AppFailureConstructors.googleCalendarNotConfigured();
    }

    // Default error handling
    throw AppFailureConstructors.googleCalendarAuthFailed(error.toString());
  }

  /// Helper method to authenticate with scope hints and fallback strategy
  Future<GoogleSignInAccount> _authenticateWithScopeHints() async {
    try {
      // Try authentication with Calendar scope hints first
      return await _googleSignIn.authenticate(
        scopeHint: _calendarScopes,
      );
    } catch (scopeAuthError) {
      // If user cancelled, don't try fallback - respect their choice
      if (_isCancellationError(scopeAuthError.toString().toLowerCase())) {
        rethrow;
      }

      // If scope hints fail, try without scope hints
      return _googleSignIn.authenticate();
    }
  }
}
