import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';

/// Abstract interface for calendar data source
/// Handles device calendar operations and permissions
abstract class CalendarDataSource {
  /// Check if calendar permissions are granted
  Future<bool> hasCalendarPermissions();

  /// Request calendar permissions from the user
  Future<bool> requestCalendarPermissions();

  /// Get the default calendar ID for the device
  /// On Android, this will prefer Google Calendar if available
  Future<String> getDefaultCalendarId();

  /// Get Google Calendar ID specifically (Android only)
  ///
  /// Throws [CalendarFailure] if Google Calendar is not found or not available
  /// Returns the Google Calendar ID if found and writable
  Future<String> getGoogleCalendarId();

  /// Get all available calendars on the device
  ///
  /// Returns a list of calendar information for debugging and selection purposes
  /// Each map contains: id, name, accountName, accountType, isDefault, isReadOnly
  Future<List<Map<String, dynamic>>> getAvailableCalendars();

  /// Create a calendar event in the specified calendar
  Future<CalendarEventEntity> createEvent(
    CalendarEventEntity eventEntity,
    String calendarId,
  );

  /// Retrieve calendar events from the device's calendars
  ///
  /// [startDate] - Optional start date filter for events
  /// [endDate] - Optional end date filter for events
  /// [calendarIds] - Optional list of specific calendar IDs to retrieve events from
  ///
  /// Returns a list of calendar event entities
  Future<List<CalendarEventEntity>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  });
}
