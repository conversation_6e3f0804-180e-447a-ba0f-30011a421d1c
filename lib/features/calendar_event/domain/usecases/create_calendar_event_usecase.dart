import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/calendar_event_repository.dart';

/// Use case for creating calendar events
/// This encapsulates the business logic for creating a calendar event
/// Following the single responsibility principle
class CreateCalendarEventUseCase {
  const CreateCalendarEventUseCase(this._repository);

  final CalendarEventRepository _repository;

  /// Execute the use case to create a calendar event
  ///
  /// This method handles the complete flow of creating a calendar event with
  /// intelligent calendar selection:
  /// 1. Validates the event data
  /// 2. Checks and requests permissions if needed
  /// 3. Attempts to use Google Calendar on Android (with fallback to default)
  /// 4. Creates the event in the selected calendar
  ///
  /// The calendar selection strategy:
  /// - Android: Prefers Google Calendar, falls back to default calendar
  /// - iOS: Uses default system calendar
  ///
  /// Throws [ValidationFailure] if event data is invalid
  /// Throws [CalendarFailure] if permissions are denied or calendar access fails
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns the created event entity on success
  Future<CalendarEventEntity> execute(CreateCalendarEventParams params) async {
    // Validate event data
    if (!params.event.isValid) {
      throw ValidationFailure(
        'Invalid event data: ${_getValidationErrors(params.event)}',
      );
    }

    // Check if we have calendar permissions
    final hasPermissions = await _repository.hasCalendarPermissions();
    if (!hasPermissions) {
      // Request permissions
      final permissionsGranted = await _repository.requestCalendarPermissions();
      if (!permissionsGranted) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }
    }

    // Create the event using specified calendar or smart selection
    return _createEventWithCalendarSelection(params.event, params.calendarId);
  }

  /// Create calendar event with calendar selection
  ///
  /// If calendarId is provided, uses that specific calendar
  /// Otherwise uses smart calendar selection (Google Calendar preference)
  Future<CalendarEventEntity> _createEventWithCalendarSelection(
    CalendarEventEntity event,
    String? calendarId,
  ) async {
    if (calendarId != null) {
      // Use specific calendar ID
      return _repository.createEventInCalendar(event, calendarId);
    } else {
      // Use smart calendar selection (existing behavior)
      return _repository.createEvent(event);
    }
  }

  /// Get validation error messages for the event
  String _getValidationErrors(CalendarEventEntity event) {
    final errors = <String>[];

    if (event.title.trim().isEmpty) {
      errors.add('Title cannot be empty');
    }

    if (!event.endDateTime.isAfter(event.startDateTime)) {
      errors.add('End time must be after start time');
    }

    return errors.join(', ');
  }
}

/// Parameters for the CreateCalendarEventUseCase
class CreateCalendarEventParams {
  const CreateCalendarEventParams({
    required this.event,
    this.calendarId,
  });

  /// The event to create
  final CalendarEventEntity event;

  /// Optional specific calendar ID to use
  /// If null, will use smart calendar selection
  final String? calendarId;
}
