import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/google_calendar_repository.dart';

/// Use case for updating Google Calendar events
class UpdateGoogleCalendarEventUseCase {
  const UpdateGoogleCalendarEventUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Execute the use case to update a Google Calendar event
  ///
  /// This method handles the complete flow of updating a Google Calendar event:
  /// 1. Validates the event data
  /// 2. Checks authentication status
  /// 3. Authenticates if needed
  /// 4. Updates the event via Google Calendar API
  ///
  /// Throws [ValidationFailure] if event data is invalid
  /// Throws [CalendarFailure] if authentication fails or API access is denied
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns the updated event entity
  Future<GoogleCalendarEventEntity> execute(
    UpdateGoogleCalendarEventParams params,
  ) async {
    // Validate event data
    if (!params.event.isValid) {
      throw ValidationFailure(
        'Invalid event data: ${_getValidationErrors(params.event)}',
      );
    }

    // Validate that event has an ID for updating
    if (params.event.eventId == null || params.event.eventId!.isEmpty) {
      throw const ValidationFailure(
        'Cannot update event without event ID',
      );
    }

    // Check if user is authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (!isAuthenticated) {
      // User is not authenticated - this should be handled at the presentation layer
      // to avoid redundant authentication prompts
      throw AppFailureConstructors.googleCalendarAuthFailed(
        'User must be authenticated to update calendar events',
      );
    }

    // Update the event
    return _repository.updateEvent(params.event);
  }

  /// Get validation errors for the event
  String _getValidationErrors(GoogleCalendarEventEntity event) {
    final errors = <String>[];

    if (event.title.trim().isEmpty) {
      errors.add('Title is required');
    }

    if (!event.endDateTime.isAfter(event.startDateTime)) {
      errors.add('End time must be after start time');
    }

    return errors.join(', ');
  }
}

/// Parameters for UpdateGoogleCalendarEventUseCase
class UpdateGoogleCalendarEventParams {
  const UpdateGoogleCalendarEventParams({
    required this.event,
  });

  /// Event entity to update (must have eventId populated)
  final GoogleCalendarEventEntity event;
}

/// Use case for deleting Google Calendar events
class DeleteGoogleCalendarEventUseCase {
  const DeleteGoogleCalendarEventUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Execute the use case to delete a Google Calendar event
  ///
  /// This method handles the complete flow of deleting a Google Calendar event:
  /// 1. Validates the event ID
  /// 2. Checks authentication status
  /// 3. Authenticates if needed
  /// 4. Deletes the event via Google Calendar API
  ///
  /// Throws [ValidationFailure] if event ID is invalid
  /// Throws [CalendarFailure] if authentication fails or API access is denied
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns true if deletion was successful
  Future<bool> execute(DeleteGoogleCalendarEventParams params) async {
    // Validate event ID
    if (params.eventId.trim().isEmpty) {
      throw const ValidationFailure(
        'Event ID is required for deletion',
      );
    }

    // Check if user is authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (!isAuthenticated) {
      // User is not authenticated - this should be handled at the presentation layer
      // to avoid redundant authentication prompts
      throw AppFailureConstructors.googleCalendarAuthFailed(
        'User must be authenticated to delete calendar events',
      );
    }

    // Delete the event
    return _repository.deleteEvent(
      params.eventId,
      calendarId: params.calendarId,
    );
  }
}

/// Parameters for DeleteGoogleCalendarEventUseCase
class DeleteGoogleCalendarEventParams {
  const DeleteGoogleCalendarEventParams({
    required this.eventId,
    this.calendarId,
  });

  /// Event ID to delete
  final String eventId;

  /// Calendar ID where the event is located (defaults to primary calendar)
  final String? calendarId;
}
