import 'package:glidic_app/core/common/constants/error_codes.dart';
import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/calendar_event_repository.dart';

/// Use case for retrieving calendar events
/// This encapsulates the business logic for fetching calendar events from the device
/// Following the single responsibility principle
class GetCalendarEventsUseCase {
  const GetCalendarEventsUseCase(this._repository);

  final CalendarEventRepository _repository;

  /// Execute the use case to retrieve calendar events
  ///
  /// This method handles the complete flow of retrieving calendar events:
  /// 1. Checks and requests permissions if needed
  /// 2. Validates date range parameters
  /// 3. Retrieves events from the device calendar
  ///
  /// Throws [ValidationFailure] if date parameters are invalid
  /// Throws [CalendarFailure] if permissions are denied or calendar access fails
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns a list of calendar event entities
  Future<List<CalendarEventEntity>> execute(GetCalendarEventsParams params) async {
    // Validate date range if provided
    if (params.startDate != null && params.endDate != null) {
      if (!params.endDate!.isAfter(params.startDate!)) {
        throw const ValidationFailure(
          'End date must be after start date',
          code: ErrorCodes.invalidDateRange,
        );
      }
    }

    // Check if we have calendar permissions
    final hasPermissions = await _repository.hasCalendarPermissions();
    if (!hasPermissions) {
      // Request permissions
      final permissionsGranted = await _repository.requestCalendarPermissions();
      if (!permissionsGranted) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }
    }

    // Retrieve the events
    return _repository.getCalendarEvents(
      startDate: params.startDate,
      endDate: params.endDate,
      calendarIds: params.calendarIds,
    );
  }
}

/// Parameters for GetCalendarEventsUseCase
/// Encapsulates all input parameters for retrieving calendar events
class GetCalendarEventsParams {
  const GetCalendarEventsParams({
    this.startDate,
    this.endDate,
    this.calendarIds,
  });

  /// Optional start date filter for events
  /// If null, defaults to 30 days ago
  final DateTime? startDate;

  /// Optional end date filter for events
  /// If null, defaults to 30 days from now
  final DateTime? endDate;

  /// Optional list of specific calendar IDs to retrieve events from
  /// If null, retrieves from all available calendars
  final List<String>? calendarIds;

  /// Create a copy of this params with updated values
  GetCalendarEventsParams copyWith({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  }) {
    return GetCalendarEventsParams(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      calendarIds: calendarIds ?? this.calendarIds,
    );
  }

  @override
  String toString() {
    return 'GetCalendarEventsParams(startDate: $startDate, endDate: $endDate, calendarIds: $calendarIds)';
  }
}
