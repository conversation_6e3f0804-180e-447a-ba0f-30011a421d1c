import 'package:glidic_app/core/common/errors/failures.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/calendar_info_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/calendar_event_repository.dart';

/// Use case for getting available calendars on the device
class GetAvailableCalendarsUseCase {
  const GetAvailableCalendarsUseCase(this._repository);

  final CalendarEventRepository _repository;

  /// Execute the use case to get available calendars
  ///
  /// This method:
  /// 1. Checks calendar permissions
  /// 2. Retrieves all available calendars from the device
  /// 3. Filters out read-only calendars
  /// 4. Converts to CalendarInfoEntity objects
  /// 5. Sorts calendars with Google Calendars first
  ///
  /// Throws [CalendarFailure] if permissions are denied or calendar access fails
  /// Returns list of writable calendars sorted by preference
  Future<List<CalendarInfoEntity>> execute() async {
    // Check if we have calendar permissions
    final hasPermissions = await _repository.hasCalendarPermissions();
    if (!hasPermissions) {
      // Request permissions
      final permissionsGranted = await _repository.requestCalendarPermissions();
      if (!permissionsGranted) {
        throw AppFailureConstructors.calendarPermissionDenied();
      }
    }

    // Get available calendars from repository
    final calendarsData = await _repository.getAvailableCalendars();

    // Convert to entities and filter writable calendars
    final calendars = calendarsData
        .map((data) => CalendarInfoEntity.fromMap(data))
        .where((calendar) => !calendar.isReadOnly)
        .toList();

    // Sort calendars with Google Calendars first, then by name
    calendars.sort((a, b) {
      // Google calendars first
      if (a.isGoogleCalendar && !b.isGoogleCalendar) return -1;
      if (!a.isGoogleCalendar && b.isGoogleCalendar) return 1;

      // Default calendars next
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;

      // Then sort by name
      return a.displayName.compareTo(b.displayName);
    });

    return calendars;
  }
}
