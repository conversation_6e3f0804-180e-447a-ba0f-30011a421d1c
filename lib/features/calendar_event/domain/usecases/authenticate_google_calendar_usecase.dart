import 'package:glidic_app/features/calendar_event/domain/repositories/google_calendar_repository.dart';

/// Use case for Google Calendar authentication
class AuthenticateGoogleCalendarUseCase {
  const AuthenticateGoogleCalendarUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Authenticates with Google Calendar
  Future<bool> execute() async {
    // Check if already authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (isAuthenticated) {
      return true;
    }

    // Attempt to authenticate
    return _repository.authenticate();
  }
}

/// Use case for checking Google Calendar authentication status
class CheckGoogleCalendarAuthUseCase {
  const CheckGoogleCalendarAuthUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Checks authentication status
  Future<bool> execute() async {
    return _repository.isAuthenticated();
  }
}

/// Use case for signing out from Google Calendar
class SignOutGoogleCalendarUseCase {
  const SignOutGoogleCalendarUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Signs out from Google Calendar
  Future<bool> execute() async {
    return _repository.signOut();
  }
}
