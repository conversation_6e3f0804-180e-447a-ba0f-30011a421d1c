import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/google_calendar_repository.dart';

/// Parameters for creating a Google Calendar event
class CreateGoogleCalendarEventParams {
  const CreateGoogleCalendarEventParams({
    required this.event,
  });

  final GoogleCalendarEventEntity event;
}

/// Use case for creating Google Calendar events
class CreateGoogleCalendarEventUseCase {
  const CreateGoogleCalendarEventUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Creates a Google Calendar event
  Future<GoogleCalendarEventEntity> execute(CreateGoogleCalendarEventParams params) async {
    // Validate event data
    if (!params.event.isValid) {
      throw ValidationFailure(
        'Invalid event data: ${_getValidationErrors(params.event)}',
      );
    }

    // Check if user is authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (!isAuthenticated) {
      // User is not authenticated - this should be handled at the presentation layer
      // to avoid redundant authentication prompts
      throw AppFailureConstructors.googleCalendarAuthFailed(
        'User must be authenticated to create calendar events',
      );
    }

    // Create the event
    return _repository.createEvent(params.event);
  }

  /// Get validation errors for the event
  String _getValidationErrors(GoogleCalendarEventEntity event) {
    final errors = <String>[];

    if (event.title.trim().isEmpty) {
      errors.add('Title is required');
    }

    if (!event.endDateTime.isAfter(event.startDateTime)) {
      errors.add('End time must be after start time');
    }

    return errors.join(', ');
  }
}
