import 'package:glidic_app/core/common/errors/app_failure.dart';
import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/google_calendar_repository.dart';

/// Use case for retrieving Google Calendar events
class GetGoogleCalendarEventsUseCase {
  const GetGoogleCalendarEventsUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Execute the use case to retrieve Google Calendar events
  ///
  /// This method handles the complete flow of retrieving Google Calendar events:
  /// 1. Checks authentication status
  /// 2. Authenticates if needed
  /// 3. Retrieves events from the specified calendar within the date range
  ///
  /// Throws [CalendarFailure] if authentication fails or API access is denied
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns a list of event entities
  Future<List<GoogleCalendarEventEntity>> execute(GetGoogleCalendarEventsParams params) async {
    // Check if user is authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (!isAuthenticated) {
      // Attempt to authenticate
      final authSuccess = await _repository.authenticate();
      if (!authSuccess) {
        throw AppFailureConstructors.googleCalendarAuthFailed(
          'Failed to authenticate with Google Calendar',
        );
      }
    }

    // Retrieve events
    return _repository.getEvents(
      calendarId: params.calendarId,
      startDate: params.startDate,
      endDate: params.endDate,
      maxResults: params.maxResults,
    );
  }
}

/// Parameters for GetGoogleCalendarEventsUseCase
class GetGoogleCalendarEventsParams {
  const GetGoogleCalendarEventsParams({
    this.calendarId,
    this.startDate,
    this.endDate,
    this.maxResults,
  });

  /// Calendar ID to retrieve events from (defaults to primary calendar)
  final String? calendarId;

  /// Start date for event retrieval (defaults to current date)
  final DateTime? startDate;

  /// End date for event retrieval (defaults to 30 days from now)
  final DateTime? endDate;

  /// Maximum number of events to retrieve (defaults to 100)
  final int? maxResults;
}

/// Use case for retrieving a specific Google Calendar event
class GetGoogleCalendarEventUseCase {
  const GetGoogleCalendarEventUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Execute the use case to retrieve a specific Google Calendar event
  ///
  /// This method handles the complete flow of retrieving a Google Calendar event:
  /// 1. Checks authentication status
  /// 2. Authenticates if needed
  /// 3. Retrieves the event by ID
  ///
  /// Throws [CalendarFailure] if authentication fails or event not found
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns the event entity
  Future<GoogleCalendarEventEntity> execute(GetGoogleCalendarEventParams params) async {
    // Check if user is authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (!isAuthenticated) {
      // Attempt to authenticate
      final authSuccess = await _repository.authenticate();
      if (!authSuccess) {
        throw AppFailureConstructors.googleCalendarAuthFailed(
          'Failed to authenticate with Google Calendar',
        );
      }
    }

    // Retrieve the event
    return _repository.getEvent(params.eventId, calendarId: params.calendarId);
  }
}

/// Parameters for GetGoogleCalendarEventUseCase
class GetGoogleCalendarEventParams {
  const GetGoogleCalendarEventParams({
    required this.eventId,
    this.calendarId,
  });

  /// Event ID to retrieve
  final String eventId;

  /// Calendar ID where the event is located (defaults to primary calendar)
  final String? calendarId;
}

/// Use case for getting available Google Calendars
class GetGoogleCalendarsUseCase {
  const GetGoogleCalendarsUseCase(this._repository);

  final GoogleCalendarRepository _repository;

  /// Execute the use case to retrieve available Google Calendars
  ///
  /// This method handles the complete flow of retrieving Google Calendars:
  /// 1. Checks authentication status
  /// 2. Authenticates if needed
  /// 3. Retrieves the list of available calendars
  ///
  /// Throws [CalendarFailure] if authentication fails or API access is denied
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns a list of calendar information maps
  Future<List<Map<String, dynamic>>> execute() async {
    // Check if user is authenticated
    final isAuthenticated = await _repository.isAuthenticated();
    if (!isAuthenticated) {
      // Attempt to authenticate
      final authSuccess = await _repository.authenticate();
      if (!authSuccess) {
        throw AppFailureConstructors.googleCalendarAuthFailed(
          'Failed to authenticate with Google Calendar',
        );
      }
    }

    // Retrieve calendars
    return _repository.getCalendars();
  }
}
