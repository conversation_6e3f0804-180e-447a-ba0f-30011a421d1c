import 'package:equatable/equatable.dart';

/// Calendar event entity representing a calendar event in the domain layer
/// This entity follows the clean architecture principles and contains
/// only the essential business logic properties
class CalendarEventEntity extends Equatable {
  const CalendarEventEntity({
    required this.title,
    required this.startDateTime,
    required this.endDateTime,
    this.description,
    this.location,
  });

  /// Event title (required)
  final String title;

  /// Event start date and time (required)
  final DateTime startDateTime;

  /// Event end date and time (required)
  final DateTime endDateTime;

  /// Optional event description
  final String? description;

  /// Optional event location
  final String? location;

  /// Validation method to ensure the event data is valid
  bool get isValid {
    return title.trim().isNotEmpty && endDateTime.isAfter(startDateTime);
  }

  /// Get event duration in minutes
  int get durationInMinutes {
    return endDateTime.difference(startDateTime).inMinutes;
  }

  /// Check if the event is happening today
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate =
        DateTime(startDateTime.year, startDateTime.month, startDateTime.day);
    return eventDate == today;
  }

  /// Check if the event is in the future
  bool get isFuture {
    return startDateTime.isAfter(DateTime.now());
  }

  /// Create a copy of this entity with updated values
  CalendarEventEntity copyWith({
    String? title,
    DateTime? startDateTime,
    DateTime? endDateTime,
    String? description,
    String? location,
  }) {
    return CalendarEventEntity(
      title: title ?? this.title,
      startDateTime: startDateTime ?? this.startDateTime,
      endDateTime: endDateTime ?? this.endDateTime,
      description: description ?? this.description,
      location: location ?? this.location,
    );
  }

  @override
  List<Object?> get props => [
        title,
        startDateTime,
        endDateTime,
        description,
        location,
      ];

  @override
  String toString() {
    return 'CalendarEventEntity(title: $title, startDateTime: $startDateTime, endDateTime: $endDateTime, description: $description, location: $location)';
  }
}
