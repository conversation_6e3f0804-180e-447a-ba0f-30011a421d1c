/// Entity representing calendar information for selection
class CalendarInfoEntity {
  const CalendarInfoEntity({
    required this.id,
    this.name,
    this.accountName,
    this.accountType,
    required this.isDefault,
    required this.isReadOnly,
    this.color,
  });

  /// Calendar ID
  final String id;

  /// Calendar display name
  final String? name;

  /// Account name associated with the calendar
  final String? accountName;

  /// Account type (e.g., 'com.google', 'local')
  final String? accountType;

  /// Whether this is the default calendar
  final bool isDefault;

  /// Whether this calendar is read-only
  final bool isReadOnly;

  /// Calendar color (if available)
  final int? color;

  /// Check if this is a Google Calendar
  bool get isGoogleCalendar {
    final accountType = this.accountType?.toLowerCase() ?? '';
    return accountType == 'com.google' || accountType == 'com.google.android.gm';
  }

  /// Get calendar type for display
  CalendarType get calendarType {
    if (isGoogleCalendar) {
      return CalendarType.google;
    }
    
    final accountType = this.accountType?.toLowerCase() ?? '';
    if (accountType == 'local' || accountType.isEmpty) {
      return CalendarType.local;
    }
    
    return CalendarType.other;
  }

  /// Get display name for the calendar
  String get displayName {
    final calendarName = name;
    if (calendarName != null && calendarName.isNotEmpty) {
      return calendarName;
    }
    return accountName ?? 'Unknown Calendar';
  }

  /// Get account display name
  String get accountDisplayName {
    final account = accountName;
    if (account != null && account.isNotEmpty) {
      return account;
    }
    return 'Local Account';
  }

  /// Create from Map (from data source)
  factory CalendarInfoEntity.fromMap(Map<String, dynamic> map) {
    return CalendarInfoEntity(
      id: map['id'] as String? ?? '',
      name: map['name'] as String?,
      accountName: map['accountName'] as String?,
      accountType: map['accountType'] as String?,
      isDefault: map['isDefault'] as bool? ?? false,
      isReadOnly: map['isReadOnly'] as bool? ?? false,
      color: map['color'] as int?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalendarInfoEntity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CalendarInfoEntity(id: $id, name: $name, accountName: $accountName, accountType: $accountType)';
  }
}

/// Enum representing different calendar types
enum CalendarType {
  google,
  local,
  other,
}

/// Extension for calendar type display
extension CalendarTypeExtension on CalendarType {
  String get displayName {
    switch (this) {
      case CalendarType.google:
        return 'Google Calendar';
      case CalendarType.local:
        return 'Local Calendar';
      case CalendarType.other:
        return 'Other Account';
    }
  }

  String get description {
    switch (this) {
      case CalendarType.google:
        return 'Recommended';
      case CalendarType.local:
        return 'Device only';
      case CalendarType.other:
        return 'Third-party account';
    }
  }
}
