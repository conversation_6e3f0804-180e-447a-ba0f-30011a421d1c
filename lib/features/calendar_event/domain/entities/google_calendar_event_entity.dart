import 'package:equatable/equatable.dart';

/// Google Calendar event entity for domain layer
/// Contains essential properties for Google Calendar API integration
class GoogleCalendarEventEntity extends Equatable {
  const GoogleCalendarEventEntity({
    required this.title,
    required this.startDateTime,
    required this.endDateTime,
    this.description,
    this.location,
    this.eventId,
    this.calendarId,
    this.reminders,
  });

  /// Event title
  final String title;

  /// Event start date and time
  final DateTime startDateTime;

  /// Event end date and time
  final DateTime endDateTime;

  /// Event description
  final String? description;

  /// Event location
  final String? location;

  /// Google Calendar event ID
  final String? eventId;

  /// Google Calendar ID
  final String? calendarId;

  /// Event reminders in minutes before event
  final List<int>? reminders;

  /// Validates event data
  bool get isValid {
    return title.trim().isNotEmpty && endDateTime.isAfter(startDateTime);
  }

  /// Get event duration in minutes
  int get durationInMinutes {
    return endDateTime.difference(startDateTime).inMinutes;
  }

  /// Check if the event is happening today
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate =
        DateTime(startDateTime.year, startDateTime.month, startDateTime.day);
    return eventDate == today;
  }

  /// Check if the event is in the future
  bool get isFuture {
    return startDateTime.isAfter(DateTime.now());
  }

  /// Creates a copy with updated values
  GoogleCalendarEventEntity copyWith({
    String? title,
    DateTime? startDateTime,
    DateTime? endDateTime,
    String? description,
    String? location,
    String? eventId,
    String? calendarId,
    List<int>? reminders,
  }) {
    return GoogleCalendarEventEntity(
      title: title ?? this.title,
      startDateTime: startDateTime ?? this.startDateTime,
      endDateTime: endDateTime ?? this.endDateTime,
      description: description ?? this.description,
      location: location ?? this.location,
      eventId: eventId ?? this.eventId,
      calendarId: calendarId ?? this.calendarId,
      reminders: reminders ?? this.reminders,
    );
  }

  @override
  List<Object?> get props => [
        title,
        startDateTime,
        endDateTime,
        description,
        location,
        eventId,
        calendarId,
        reminders,
      ];

  @override
  String toString() {
    return 'GoogleCalendarEventEntity(title: $title, startDateTime: $startDateTime, endDateTime: $endDateTime, description: $description, location: $location, eventId: $eventId, calendarId: $calendarId, reminders: $reminders)';
  }
}
