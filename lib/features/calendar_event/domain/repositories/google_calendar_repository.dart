import 'package:glidic_app/features/calendar_event/domain/entities/google_calendar_event_entity.dart';

/// Repository interface for Google Calendar operations
abstract class GoogleCalendarRepository {
  /// Authenticates with Google Calendar API
  Future<bool> authenticate();

  /// Checks authentication status
  Future<bool> isAuthenticated();

  /// Signs out from Google Calendar API
  Future<bool> signOut();

  /// Creates a new Google Calendar event
  Future<GoogleCalendarEventEntity> createEvent(GoogleCalendarEventEntity event);

  /// Gets the primary Google Calendar ID
  Future<String> getPrimaryCalendarId();

  /// Gets user's Google Calendar information
  Future<Map<String, dynamic>> getCalendarInfo();

  /// Retrieves Google Calendar events
  Future<List<GoogleCalendarEventEntity>> getEvents({
    String? calendarId,
    DateTime? startDate,
    DateTime? endDate,
    int? maxResults,
  });

  /// Gets a specific Google Calendar event by ID
  Future<GoogleCalendarEventEntity> getEvent(String eventId, {String? calendarId});

  /// Updates an existing Google Calendar event
  Future<GoogleCalendarEventEntity> updateEvent(GoogleCalendarEventEntity event);

  /// Deletes a Google Calendar event
  Future<bool> deleteEvent(String eventId, {String? calendarId});

  /// Gets list of available calendars
  Future<List<Map<String, dynamic>>> getCalendars();
}
