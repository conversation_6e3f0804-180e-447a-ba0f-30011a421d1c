import 'package:glidic_app/features/calendar_event/domain/entities/calendar_event_entity.dart';

/// Abstract repository interface for calendar event operations
/// This defines the contract that the data layer must implement
/// Following the dependency inversion principle of Clean Architecture
abstract class CalendarEventRepository {
  /// Create a new calendar event in the device's default calendar
  ///
  /// Throws [CalendarPermissionFailure] if calendar permissions are denied
  /// Throws [CalendarAccessFailure] if calendar access fails
  /// Throws [ValidationFailure] if event data is invalid
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns the created event entity on success
  Future<CalendarEventEntity> createEvent(CalendarEventEntity event);

  /// Create a calendar event in a specific calendar
  ///
  /// Throws [CalendarPermissionFailure] if calendar permissions are denied
  /// Throws [CalendarAccessFailure] if calendar access fails or calendar not found
  /// Throws [ValidationFailure] if event data is invalid
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns the created event entity on success
  Future<CalendarEventEntity> createEventInCalendar(
    CalendarEventEntity event,
    String calendarId,
  );

  /// Check if calendar permissions are granted
  ///
  /// Returns true if permissions are granted, false otherwise
  /// This method does not throw exceptions
  Future<bool> hasCalendarPermissions();

  /// Request calendar permissions from the user
  ///
  /// Returns true if permissions are granted, false if denied
  /// This method does not throw exceptions
  Future<bool> requestCalendarPermissions();

  /// Get the default calendar ID for the device
  /// On Android, this will prefer Google Calendar if available
  ///
  /// Throws [CalendarAccessFailure] if no default calendar is found
  /// Throws [CalendarPermissionFailure] if permissions are not granted
  ///
  /// Returns the default calendar ID as a string
  Future<String> getDefaultCalendarId();

  /// Get Google Calendar ID specifically (Android only)
  ///
  /// Throws [CalendarFailure] if Google Calendar is not found or not available
  /// Throws [CalendarPermissionFailure] if permissions are not granted
  ///
  /// Returns the Google Calendar ID if found and writable
  Future<String> getGoogleCalendarId();

  /// Get all available calendars on the device
  ///
  /// Throws [CalendarPermissionFailure] if permissions are not granted
  /// Throws [CalendarAccessFailure] if calendar access fails
  ///
  /// Returns a list of calendar information for debugging and selection purposes
  Future<List<Map<String, dynamic>>> getAvailableCalendars();

  /// Retrieve calendar events from the device's calendars
  ///
  /// [startDate] - Optional start date filter for events (defaults to 30 days ago)
  /// [endDate] - Optional end date filter for events (defaults to 30 days from now)
  /// [calendarIds] - Optional list of specific calendar IDs to retrieve events from
  ///                 If null, retrieves from all available calendars
  ///
  /// Throws [CalendarPermissionFailure] if calendar permissions are denied
  /// Throws [CalendarAccessFailure] if calendar access fails
  /// Throws [UnknownFailure] for unexpected errors
  ///
  /// Returns a list of calendar event entities
  Future<List<CalendarEventEntity>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? calendarIds,
  });
}
