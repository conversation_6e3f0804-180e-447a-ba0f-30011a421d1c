import 'package:glidic_app/core/di/service_locator.dart';
import 'package:glidic_app/features/calendar_event/data/datasources/calendar_datasource_impl.dart';
import 'package:glidic_app/features/calendar_event/data/datasources/google_calendar_datasource_impl.dart';
import 'package:glidic_app/features/calendar_event/data/datasources/interfaces/calendar_datasource.dart';
import 'package:glidic_app/features/calendar_event/data/datasources/interfaces/google_calendar_datasource.dart';
import 'package:glidic_app/features/calendar_event/data/repositories/calendar_event_repository_impl.dart';
import 'package:glidic_app/features/calendar_event/data/repositories/google_calendar_repository_impl.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/calendar_event_repository.dart';
import 'package:glidic_app/features/calendar_event/domain/repositories/google_calendar_repository.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/authenticate_google_calendar_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/create_calendar_event_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/create_google_calendar_event_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/get_available_calendars_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/get_calendar_events_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/get_google_calendar_events_usecase.dart';
import 'package:glidic_app/features/calendar_event/domain/usecases/update_google_calendar_event_usecase.dart';

/// Dependency injection configuration for Calendar Event feature
/// This class encapsulates all Calendar Event-related dependency registrations
/// Following the project's DI patterns with factory registration for use cases
class CalendarEventDI {
  /// Initialize Calendar Event feature dependencies
  static Future<void> init() async {
    // Data Sources
    sl.registerLazySingleton<CalendarDataSource>(
      () => CalendarDataSourceImpl(),
    );
    sl.registerLazySingleton<GoogleCalendarDataSource>(
      () => GoogleCalendarDataSourceImpl(),
    );

    // Repository
    sl.registerLazySingleton<CalendarEventRepository>(
      () => CalendarEventRepositoryImpl(
        dataSource: sl(),
      ),
    );
    sl.registerLazySingleton<GoogleCalendarRepository>(
      () => GoogleCalendarRepositoryImpl(
        dataSource: sl(),
      ),
    );

    // Use Cases - using factory registration as preferred in the project
    // Local calendar
    sl.registerFactory(() => CreateCalendarEventUseCase(sl()));
    sl.registerFactory(() => GetCalendarEventsUseCase(sl()));
    sl.registerFactory(() => GetAvailableCalendarsUseCase(sl()));
    // Google calendar
    sl.registerFactory(() => CreateGoogleCalendarEventUseCase(sl()));
    sl.registerFactory(() => AuthenticateGoogleCalendarUseCase(sl()));
    sl.registerFactory(() => CheckGoogleCalendarAuthUseCase(sl()));
    sl.registerFactory(() => SignOutGoogleCalendarUseCase(sl()));
    sl.registerFactory(() => GetGoogleCalendarEventsUseCase(sl()));
    sl.registerFactory(() => GetGoogleCalendarEventUseCase(sl()));
    sl.registerFactory(() => GetGoogleCalendarsUseCase(sl()));
    sl.registerFactory(() => UpdateGoogleCalendarEventUseCase(sl()));
    sl.registerFactory(() => DeleteGoogleCalendarEventUseCase(sl()));
  }
}
