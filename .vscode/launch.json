{"version": "0.2.0", "configurations": [{"name": "Development", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "development", "--dart-define=ENVIRONMENT=development"], "env": {"FLUTTER_BUILD_MODE": "debug"}, "console": "debugConsole", "flutterMode": "debug"}, {"name": "Staging", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "staging", "--dart-define=ENVIRONMENT=staging"], "env": {"FLUTTER_BUILD_MODE": "profile"}, "console": "debugConsole", "flutterMode": "profile"}, {"name": "Production", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "production", "--dart-define=ENVIRONMENT=production"], "env": {"FLUTTER_BUILD_MODE": "release"}, "console": "debugConsole", "flutterMode": "release"}, {"name": "Development (Profile)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "development", "--dart-define=ENVIRONMENT=development"], "env": {"FLUTTER_BUILD_MODE": "profile"}, "console": "debugConsole", "flutterMode": "profile"}, {"name": "Staging (Debug)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "staging", "--dart-define=ENVIRONMENT=staging"], "env": {"FLUTTER_BUILD_MODE": "debug"}, "console": "debugConsole", "flutterMode": "debug"}]}