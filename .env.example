# Environment Configuration Template
# Copy this file to .env and fill in your actual values
# For environment-specific configurations, use:
# - .env.development for development environment
# - .env.staging for staging environment
# - .env.production for production environment
# DO NOT commit any .env files to version control

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG_MODE=true
ENABLE_LOGGING=true

# =============================================================================
# API CONFIGURATION
# =============================================================================
BASE_URL=https://api.example.com
API_VERSION=v1
CONNECT_TIMEOUT_SECONDS=30
RECEIVE_TIMEOUT_SECONDS=30
SEND_TIMEOUT_SECONDS=30

# =============================================================================
# THIRD-PARTY API KEYS
# =============================================================================
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
FIREBASE_API_KEY=your_firebase_api_key_here
ANALYTICS_TRACKING_ID=your_analytics_tracking_id_here

# =============================================================================
# GOOGLE OAUTH CONFIGURATION
# =============================================================================
# Google OAuth Client ID for authentication
# Get this from Google Cloud Console -> APIs & Services -> Credentials
GOOGLE_OAUTH_CLIENT_ID=your_google_oauth_client_id_here.apps.googleusercontent.com

# Google OAuth Server Client ID (required for Android)
# This should be the Web application Client ID from Google Cloud Console
# Often the same as GOOGLE_OAUTH_CLIENT_ID if you only have one credential
GOOGLE_OAUTH_SERVER_CLIENT_ID=your_google_oauth_server_client_id_here.apps.googleusercontent.com

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_FEATURE_X=false
ENABLE_ANALYTICS=true

# =============================================================================
# ENVIRONMENT-SPECIFIC SETTINGS
# =============================================================================
# Development settings
ENABLE_MOCK_DATA=false
ENABLE_DEBUG_OVERLAY=false
SKIP_AUTH=false

# Performance and monitoring
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_CRASH_REPORTING=false
ENABLE_SECURITY_MONITORING=false

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================
# Add your custom environment variables here
# CUSTOM_API_KEY=your_custom_api_key_here
# CUSTOM_SETTING=your_custom_setting_here
