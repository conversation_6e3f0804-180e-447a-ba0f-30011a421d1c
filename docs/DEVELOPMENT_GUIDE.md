# Development Guide

## Getting Started

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code with Flutter extensions
- Git

### Initial Setup

1. **<PERSON>lone and Setup**
   ```bash
   git clone <repository-url>
   cd flutter-base-project
   flutter pub get
   ```

2. **Environment Configuration**
   ```bash
   # Copy environment files
   cp .env.example .env.development
   cp .env.example .env.staging
   cp .env.example .env.production

   # Edit environment files with your configuration
   # See docs/ENVIRONMENT_CONFIGURATION.md for details

   # Switch to development environment
   ./scripts/switch_env.sh dev
   ```

3. **IDE Configuration**
   - Install Flutter and Dart plugins
   - Configure code formatting (dartfmt)
   - Enable linting rules
   - Set up debugging configuration

4. **Run the Application**
   ```bash
   flutter run
   ```

## Adding New Features

### Step-by-Step Feature Implementation

#### 1. Create Feature Structure
```bash
mkdir -p lib/features/your_feature/{data/{datasources,models,repositories},domain/{entities,repositories,usecases},presentation/{cubit,pages,widgets},di}
```

#### 2. Domain Layer Implementation

**Create Entity:**
```dart
// lib/features/your_feature/domain/entities/your_entity.dart
import 'package:equatable/equatable.dart';

class YourEntity extends Equatable {
  const YourEntity({
    required this.id,
    required this.name,
  });

  final String id;
  final String name;

  @override
  List<Object?> get props => [id, name];
}
```

**Create Repository Interface:**
```dart
// lib/features/your_feature/domain/repositories/your_repository.dart
import '../../../../core/common/errors/app_failure.dart';
import '../entities/your_entity.dart';

abstract class YourRepository {
  /// Throws [AppFailure] on error
  Future<YourEntity> getYourData(String id);
  /// Throws [AppFailure] on error
  Future<List<YourEntity>> getAllYourData();
}
```

**Create Use Case:**
```dart
// lib/features/your_feature/domain/usecases/get_your_data_usecase.dart
import '../../../../core/common/errors/app_failure.dart';
import '../entities/your_entity.dart';
import '../repositories/your_repository.dart';

class GetYourDataUseCase {
  const GetYourDataUseCase(this._repository);

  final YourRepository _repository;

  /// Execute the use case with the given input
  /// Throws [AppFailure] or its subclasses on error:
  /// - [NetworkFailure] for network-related errors
  /// - [ValidationFailure] for input validation errors
  /// - [UnknownFailure] for unexpected errors
  Future<YourEntity> execute(String id) async {
    return await _repository.getYourData(id);
  }
}
```

#### 3. Data Layer Implementation

**Create Model:**
```dart
// lib/features/your_feature/data/models/your_model.dart
import '../../domain/entities/your_entity.dart';

class YourModel extends YourEntity {
  const YourModel({
    required super.id,
    required super.name,
  });

  factory YourModel.fromJson(Map<String, dynamic> json) {
    return YourModel(
      id: json['id'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  factory YourModel.fromEntity(YourEntity entity) {
    return YourModel(
      id: entity.id,
      name: entity.name,
    );
  }

  YourEntity toEntity() {
    return YourEntity(
      id: id,
      name: name,
    );
  }
}
```

**Create API Service (Retrofit):**
```dart
// lib/features/your_feature/data/datasources/api_services/your_api_service.dart
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../../../../../core/constants/network_constants.dart';
import '../../models/your_model.dart';

part 'your_api_service.g.dart';

@RestApi()
abstract class YourApiService {
  factory YourApiService(Dio dio) = _YourApiService;

  @GET('/your-endpoint/{id}')
  Future<YourModel> getYourData(@Path('id') String id);

  @GET('/your-endpoint')
  Future<List<YourModel>> getAllYourData();
}
```

**Create Data Source:**
```dart
// lib/features/your_feature/data/datasources/your_remote_datasource.dart
import '../models/your_model.dart';
import 'api_services/your_api_service.dart';

abstract class YourRemoteDataSource {
  Future<YourModel> getYourData(String id);
  Future<List<YourModel>> getAllYourData();
}

class YourRemoteDataSourceImpl implements YourRemoteDataSource {
  const YourRemoteDataSourceImpl(this._yourApiService);

  final YourApiService _yourApiService;

  @override
  Future<YourModel> getYourData(String id) async {
    try {
      return await _yourApiService.getYourData(id);
    } catch (e) {
      throw Exception('Failed to get your data: ${e.toString()}');
    }
  }

  @override
  Future<List<YourModel>> getAllYourData() async {
    try {
      return await _yourApiService.getAllYourData();
    } catch (e) {
      throw Exception('Failed to get all your data: ${e.toString()}');
    }
  }
}
```

**Create Repository Implementation:**
```dart
// lib/features/your_feature/data/repositories/your_repository_impl.dart
import '../../../../core/common/errors/app_failure.dart';
import '../../../../core/common/errors/exception_handler.dart';
import '../../domain/entities/your_entity.dart';
import '../../domain/repositories/your_repository.dart';
import '../datasources/your_remote_datasource.dart';

class YourRepositoryImpl implements YourRepository {
  const YourRepositoryImpl({
    required this.remoteDataSource,
  });

  final YourRemoteDataSource remoteDataSource;

  @override
  Future<YourEntity> getYourData(String id) async {
    try {
      final result = await remoteDataSource.getYourData(id);
      return result.toEntity();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }

  @override
  Future<List<YourEntity>> getAllYourData() async {
    try {
      final result = await remoteDataSource.getAllYourData();
      return result.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw ExceptionHandler.handle(e);
    }
  }
}
```

#### 4. Presentation Layer Implementation

**Create Cubit:**
```dart
// lib/features/your_feature/presentation/cubit/your_cubit.dart
import '../../../../core/cubit/base_cubit.dart';
import '../../domain/entities/your_entity.dart';
import '../../domain/usecases/get_your_data_usecase.dart';

class YourCubit extends BaseCubit<YourEntity> {
  YourCubit({
    required this.getYourDataUseCase,
  });

  final GetYourDataUseCase getYourDataUseCase;

  Future<void> loadYourData(String id) async {
    await executeUseCase(
      () => getYourDataUseCase.execute(id),
    );
  }

  YourEntity? get yourData => state.data;
}
```

**Create Page:**
```dart
// lib/features/your_feature/presentation/pages/your_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/di/service_locator.dart';
import '../cubit/your_cubit.dart';

class YourPage extends StatelessWidget {
  const YourPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<YourCubit>()..loadYourData('example-id'),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Your Feature'),
        ),
        body: BlocConsumer<YourCubit, dynamic>(
          listener: (context, state) {
            if (state.isError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.failure?.message ?? 'An error occurred'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state.isError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Text(
                      'Failed to load data',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    ElevatedButton(
                      onPressed: () => context.read<YourCubit>().loadYourData('example-id'),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            final data = state.data;
            if (data == null) {
              return const Center(child: Text('No data available'));
            }

            return Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ID: ${data.id}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    'Name: ${data.name}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
```

#### 5. Dependency Injection Setup

**Create Feature DI:**
```dart
// lib/features/your_feature/di/your_di.dart
import '../../../core/di/service_locator.dart';
import '../data/datasources/your_remote_datasource.dart';
import '../data/repositories/your_repository_impl.dart';
import '../domain/repositories/your_repository.dart';
import '../domain/usecases/get_your_data_usecase.dart';
import '../presentation/cubit/your_cubit.dart';

class YourDI {
  static Future<void> init() async {
    // API Service
    sl.registerLazySingleton<YourApiService>(
      () => YourApiService(sl()),
    );

    // Data Sources
    sl.registerLazySingleton<YourRemoteDataSource>(
      () => YourRemoteDataSourceImpl(sl()),
    );

    // Repository
    sl.registerLazySingleton<YourRepository>(
      () => YourRepositoryImpl(
        remoteDataSource: sl(),
        networkInfo: sl(),
      ),
    );

    // Use Cases
    sl.registerLazySingleton(() => GetYourDataUseCase(sl()));

    // Cubit
    sl.registerFactory(() => YourCubit(getYourDataUseCase: sl()));
  }
}
```

**Register in Main DI:**
```dart
// lib/core/di/service_locator.dart
import '../../features/your_feature/di/your_di.dart';

Future<void> initializeDependencies() async {
  // ... existing code ...
  
  // Initialize feature dependencies
  await YourDI.init();
}
```

#### 6. Add Navigation

**Update Route Names:**
```dart
// lib/core/router/route_names.dart
class RouteNames {
  // ... existing routes ...
  static const String yourFeature = '/your-feature';
}
```

**Update Router:**
```dart
// lib/core/router/app_router.dart
import '../../features/your_feature/presentation/pages/your_page.dart';

// Add to routes array:
GoRoute(
  path: RouteNames.yourFeature,
  name: RouteNames.yourFeature,
  builder: (context, state) => const YourPage(),
),
```

**Add Navigation Extension:**
```dart
// lib/core/router/app_router.dart
extension AppRouterExtension on BuildContext {
  // ... existing methods ...
  void goToYourFeature() => go(RouteNames.yourFeature);
}
```

## Testing

### Unit Tests

**Test Use Case:**
```dart
// test/features/your_feature/domain/usecases/get_your_data_usecase_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  late GetYourDataUseCase useCase;
  late MockYourRepository mockRepository;

  setUp(() {
    mockRepository = MockYourRepository();
    useCase = GetYourDataUseCase(mockRepository);
  });

  group('GetYourDataUseCase', () {
    test('should return YourEntity when repository call is successful', () async {
      // Arrange
      const testId = 'test-id';
      const testEntity = YourEntity(id: testId, name: 'Test Name');
      when(mockRepository.getYourData(testId))
          .thenAnswer((_) async => const Right(testEntity));

      // Act
      final result = await useCase(testId);

      // Assert
      expect(result, const Right(testEntity));
      verify(mockRepository.getYourData(testId));
      verifyNoMoreInteractions(mockRepository);
    });
  });
}
```

**Test Cubit:**
```dart
// test/features/your_feature/presentation/cubit/your_cubit_test.dart
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

void main() {
  late YourCubit cubit;
  late MockGetYourDataUseCase mockUseCase;

  setUp(() {
    mockUseCase = MockGetYourDataUseCase();
    cubit = YourCubit(getYourDataUseCase: mockUseCase);
  });

  group('YourCubit', () {
    blocTest<YourCubit, BaseState<YourEntity>>(
      'emits [LoadingState, SuccessState] when loadYourData is successful',
      build: () {
        when(mockUseCase('test-id'))
            .thenAnswer((_) async => const Right(YourEntity(id: 'test-id', name: 'Test')));
        return cubit;
      },
      act: (cubit) => cubit.loadYourData('test-id'),
      expect: () => [
        isA<LoadingState<YourEntity>>(),
        isA<SuccessState<YourEntity>>(),
      ],
    );
  });
}
```

## Code Quality

### Linting Rules
The project uses strict linting rules defined in `analysis_options.yaml`:

```yaml
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - avoid_print
    - prefer_single_quotes
    - require_trailing_commas
```

### Code Formatting
Use `dart format` to format code consistently:

```bash
dart format lib/ test/
```

### Static Analysis
Run static analysis to catch potential issues:

```bash
flutter analyze
```

## Best Practices

1. **Follow Clean Architecture**: Keep layers separated and dependencies pointing inward
2. **Use Exception-Based Error Handling**: Handle errors with try-catch and AppFailure hierarchy
3. **Write Tests**: Maintain high test coverage for business logic
4. **Document Code**: Add meaningful comments and documentation
5. **Use Consistent Naming**: Follow Dart naming conventions
6. **Keep Widgets Small**: Break down complex widgets into smaller components
7. **Handle Loading States**: Always show loading indicators for async operations
8. **Validate Input**: Validate user input at the presentation layer
9. **Use Constants**: Define reusable constants for spacing, colors, etc.
10. **Follow Git Conventions**: Use meaningful commit messages and branch names
