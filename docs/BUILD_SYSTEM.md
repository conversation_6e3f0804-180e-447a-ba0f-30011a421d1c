# Build System Configuration

This document describes the complete build system configuration for the Glidic Flutter application, supporting multiple environments with proper app identification and configuration management.

## Overview

The build system supports three environments:
- **Development** - Local development with debug features
- **Staging** - Pre-production testing environment
- **Production** - Live production environment

Each environment has:
- Unique application/bundle identifiers
- Environment-specific app names
- Dedicated configuration files
- Proper build optimizations

## Android Build Flavors

### Configuration

Android build flavors are configured in `android/app/build.gradle`:

```gradle
productFlavors {
    development {
        applicationId = "jp.co.iotbank.glidici.dev"
        resValue "string", "app_name", "Glidic Dev"
        buildConfigField "String", "ENVIRONMENT", '"development"'
    }

    staging {
        applicationId = "jp.co.iotbank.glidici.staging"
        resValue "string", "app_name", "Glidic Staging"
        buildConfigField "String", "ENVIRONMENT", '"staging"'
    }

    production {
        applicationId = "jp.co.iotbank.glidici"
        resValue "string", "app_name", "Glidic"
        buildConfigField "String", "ENVIRONMENT", '"production"'
    }
}
```

### Application IDs

| Environment | Application ID | App Name |
|-------------|----------------|----------|
| Development | `jp.co.iotbank.glidici.dev` | Glidic Dev |
| Staging | `jp.co.iotbank.glidici.staging` | Glidic Staging |
| Production | `jp.co.iotbank.glidici` | Glidic |

### Build Commands

```bash
# Development
flutter build apk --debug --flavor development --dart-define=ENVIRONMENT=development

# Staging
flutter build apk --release --flavor staging --dart-define=ENVIRONMENT=staging
flutter build appbundle --release --flavor staging --dart-define=ENVIRONMENT=staging

# Production
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info
flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info
```

## iOS Build Schemes

### Configuration Files

iOS build configurations are defined in `.xcconfig` files:

- `ios/Flutter/Development.xcconfig`
- `ios/Flutter/Staging.xcconfig`
- `ios/Flutter/Production.xcconfig`

### Bundle Identifiers

| Environment | Bundle Identifier | Display Name |
|-------------|-------------------|--------------|
| Development | `jp.co.iotbank.glidici.dev` | Glidic Dev |
| Staging | `jp.co.iotbank.glidici.staging` | Glidic Staging |
| Production | `jp.co.iotbank.glidici` | Glidic |

### Manual Xcode Setup Required

Since Xcode project files are binary, manual setup is required:

1. **Run the setup script:**
   ```bash
   ./scripts/setup_ios_schemes.sh
   ```

2. **Follow the instructions** to create build configurations and schemes in Xcode

3. **Update team IDs** in the `.xcconfig` files

### Build Commands

```bash
# Development
flutter build ios --debug --flavor development --dart-define=ENVIRONMENT=development

# Staging
flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging

# Production
flutter build ios --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info
```

## Build Scripts

### Automated Build Scripts

| Script | Purpose | Environment |
|--------|---------|-------------|
| `scripts/build_dev.sh` | Development builds | Development |
| `scripts/build_staging.sh` | Staging builds | Staging |
| `scripts/build_production.sh` | Production builds | Production |

### Usage

```bash
# Development build
./scripts/build_dev.sh

# Staging build
./scripts/build_staging.sh

# Production build (with validation)
./scripts/build_production.sh
```

### Features

- **Environment validation** before building
- **Automatic environment file switching**
- **Cross-platform support** (Android + iOS)
- **Build optimization** per environment
- **Code obfuscation** for production
- **Debug info separation** for crash reporting

## IDE Integration

### VS Code

Launch configurations are available in `.vscode/launch.json`:

- **Development** - Debug mode with development flavor
- **Staging** - Profile mode with staging flavor
- **Production** - Release mode with production flavor
- **Development (Profile)** - Profile mode for performance testing
- **Staging (Debug)** - Debug mode for staging troubleshooting

### Android Studio / IntelliJ

Run configurations are available in `.idea/runConfigurations/`:

- `Development.xml`
- `Staging.xml`
- `Production.xml`

## Environment Configuration Loading

### Automatic Detection

The `EnvConfig` class automatically detects the environment using:

1. **Build flavor detection** via `--dart-define=ENVIRONMENT=<env>`
2. **Fallback to .env file** if no build flavor is specified
3. **Default to development** in debug mode

### Environment Files

| Environment | File | Purpose |
|-------------|------|---------|
| Development | `.env.development` | Local development settings |
| Staging | `.env.staging` | Pre-production settings |
| Production | `.env.production` | Production settings |

### Loading Process

```dart
// Initialize environment configuration
await EnvConfig.init();

// Access configuration
String apiUrl = EnvConfig.baseUrl;
bool isDebug = EnvConfig.isDebugMode;
Environment env = EnvConfig.currentEnvironment;
```

## Build Optimizations

### Development

- **Debug mode** enabled
- **Logging** enabled
- **Debug symbols** included
- **No obfuscation**
- **Fast compilation**

### Staging

- **Release mode** with debug info
- **Logging** enabled for troubleshooting
- **Analytics** enabled
- **Code minification** enabled
- **Debug symbols** for crash reporting

### Production

- **Full release optimization**
- **Code obfuscation** enabled
- **Debug info separated** for crash reporting
- **Logging** disabled
- **Maximum security** settings

## Security Features

### Code Signing

- **Development**: Debug signing
- **Staging**: Release signing (configurable)
- **Production**: Release signing with proper certificates

### Obfuscation

Production builds include:
- **Dart code obfuscation**
- **Symbol stripping**
- **Debug info separation**
- **Proguard optimization** (Android)

### Environment Validation

- **Placeholder detection** in production
- **Required variable validation**
- **Environment-specific security rules**

## Troubleshooting

### Common Issues

1. **"Flavor not found" error**
   - Ensure Android flavors are properly configured
   - Check `android/app/build.gradle` syntax

2. **iOS scheme not found**
   - Run `./scripts/setup_ios_schemes.sh`
   - Complete manual Xcode setup

3. **Environment not loading**
   - Verify `.env.*` files exist
   - Check `--dart-define` parameters in build commands

4. **Build failures**
   - Run `flutter clean && flutter pub get`
   - Validate environment files with `./scripts/validate_env.sh`

### Debug Commands

```bash
# Check current environment
./scripts/switch_env.sh current

# Validate all environments
./scripts/validate_env.sh all

# Test build without deployment
flutter build apk --debug --flavor development --dart-define=ENVIRONMENT=development
```

## CI/CD Integration

### GitHub Actions Example

```yaml
- name: Build Development
  run: ./scripts/build_dev.sh

- name: Build Staging
  run: ./scripts/build_staging.sh

- name: Build Production
  run: ./scripts/build_production.sh
```

### Environment Variables

Set these in your CI/CD system:
- `ANDROID_SIGNING_KEY` (base64 encoded)
- `ANDROID_SIGNING_PASSWORD`
- `IOS_CERTIFICATE` (base64 encoded)
- `IOS_PROVISIONING_PROFILE`

## Next Steps

1. **Complete iOS setup** using `./scripts/setup_ios_schemes.sh`
2. **Configure signing certificates** for staging and production
3. **Set up CI/CD pipeline** with build scripts
4. **Test all environments** thoroughly
5. **Deploy to app stores** using appropriate flavors/schemes
