# Android Release Build Guide

This guide provides comprehensive instructions for configuring and building Android release APKs and App Bundles for Google Play Store submission.

## 🔐 Keystore Setup (CRITICAL - Do This First!)

### 1. Generate Production Keystore

**⚠️ IMPORTANT**: You must create a production keystore before building release versions. The debug keystore is NOT suitable for production!

```bash
# Navigate to the android directory
cd android

# Generate a new keystore (replace 'release' with your preferred alias name)
keytool -genkey -v -keystore release-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias release

# You'll be prompted for:
# - Keystore password (STORE THIS SECURELY!)
# - Key password (can be same as keystore password)
# - Your details (name, organization, city, state, country)
```

### 2. Configure Keystore Properties

```bash
# Copy the template file
cp keystore.properties.example keystore.properties

# Edit keystore.properties with your actual values
# Example content:
storeFile=../release-keystore.jks
storePassword=your_secure_keystore_password
keyAlias=release
keyPassword=your_secure_key_password
```

### 3. Security Best Practices

- ✅ Store keystore file in a secure location (consider using a separate secure directory)
- ✅ Use strong, unique passwords for keystore and key
- ✅ Keep backups of keystore and passwords in a secure password manager
- ✅ Never commit `keystore.properties` or `.jks` files to version control
- ✅ Add `keystore.properties` and `*.jks` to `.gitignore`

## 🏗️ Build Configuration

The project is configured with the following optimizations:

### Release Build Features
- ✅ **Code Obfuscation**: Enabled with R8/ProGuard
- ✅ **Resource Shrinking**: Removes unused resources
- ✅ **Code Minification**: Reduces APK/Bundle size
- ✅ **Security Hardening**: Removes debug information and logging
- ✅ **App Bundle Support**: Optimized for Google Play Store
- ✅ **Multi-APK Generation**: Supports different architectures and densities

### Environment Configurations

#### Development Flavor
- **Application ID**: `jp.co.iotbank.glidici.dev`
- **App Name**: "Glidic (Dev)"
- **Environment**: Development
- **Debug Mode**: Enabled (in app logic)
- **Logging**: Enabled
- **Signing**: Production keystore (for release builds)

#### Staging Flavor
- **Application ID**: `jp.co.iotbank.glidici.staging`
- **App Name**: "Glidic (Staging)"
- **Environment**: Staging
- **Debug Mode**: Disabled
- **Logging**: Enabled
- **Analytics**: Enabled
- **Signing**: Production keystore (for release builds)

#### Production Flavor
- **Application ID**: `jp.co.iotbank.glidici`
- **App Name**: "Glidic"
- **Environment**: Production
- **Debug Mode**: Disabled
- **Logging**: Disabled for security
- **Signing**: Production keystore (for release builds)

## 📱 Building Release Versions

**Important**: All release builds (development, staging, production) now use the same secure keystore for signing, as they are all intended for app store distribution.

### Option 1: Using Build Scripts (Recommended)

#### Production Release (Play Store)
```bash
# Build production release (includes validation and secure keystore)
./scripts/build_production.sh
```

This script will:
- Validate environment configuration
- Clean previous builds
- Build both APK and App Bundle
- Enable code obfuscation
- Use production keystore for secure signing
- Require keystore.properties for secure signing

#### Development Release (App Store Distribution)
```bash
# Build development release with production keystore
./scripts/build_dev_release.sh

# Or use the main script with release parameter
./scripts/build_dev.sh release
```

This script will:
- Use development environment configuration
- Build release APK and App Bundle
- Use production keystore for secure signing
- Enable debug features and logging
- Application ID: `jp.co.iotbank.glidici.dev`

#### Staging Release (App Store Distribution)
```bash
# Build staging release with production keystore
./scripts/build_staging_release.sh

# Or use the main script with release parameter (default)
./scripts/build_staging.sh release
```

This script will:
- Use staging environment configuration
- Build release APK and App Bundle
- Use production keystore for secure signing
- Enable analytics and monitoring features
- Application ID: `jp.co.iotbank.glidici.staging`

### Option 2: Manual Flutter Commands

**Note**: All release builds require `android/keystore.properties` for secure signing.

#### Development Release
```bash
# APK
flutter build apk --release --flavor development --dart-define=ENVIRONMENT=development

# App Bundle
flutter build appbundle --release --flavor development --dart-define=ENVIRONMENT=development
```

#### Staging Release
```bash
# APK
flutter build apk --release --flavor staging --dart-define=ENVIRONMENT=staging

# App Bundle
flutter build appbundle --release --flavor staging --dart-define=ENVIRONMENT=staging
```

#### Production Release
```bash
# APK
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info

# App Bundle (Recommended for Play Store)
flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info

# Split APKs (for different architectures)
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info --split-per-abi
```

## 📦 Output Files

After successful build, you'll find the files in:

### APK Files
```
build/app/outputs/flutter-apk/
# Development
├── app-development-release.apk

# Staging
├── app-staging-release.apk

# Production
├── app-production-release.apk          # Universal APK
├── app-armeabi-v7a-production-release.apk  # ARM 32-bit (if split-per-abi used)
├── app-arm64-v8a-production-release.apk    # ARM 64-bit (if split-per-abi used)
└── app-x86_64-production-release.apk       # x86 64-bit (if split-per-abi used)
```

### App Bundle Files (Recommended for App Stores)
```
build/app/outputs/bundle/
# Development
├── developmentRelease/
│   └── app-development-release.aab

# Staging
├── stagingRelease/
│   └── app-staging-release.aab

# Production
└── productionRelease/
    └── app-production-release.aab
```

## 🚀 Google Play Store Submission

### 1. Upload App Bundle (Recommended)
- Use `app-production-release.aab` for Play Store submission
- App Bundles enable Dynamic Delivery for smaller downloads
- Google Play generates optimized APKs for each device

### 2. Alternative: Upload APK
- Use `app-production-release.apk` if you prefer APK submission
- Larger file size but works on all compatible devices

### 3. Pre-Submission Checklist
- ✅ Test the release build on multiple devices
- ✅ Verify app functionality in production environment
- ✅ Check app signing and certificate
- ✅ Validate app bundle/APK integrity
- ✅ Review app permissions and privacy policy
- ✅ Test in-app purchases (if applicable)

## 🔍 Verification Commands

### Verify App Bundle
```bash
# Install bundletool (if not already installed)
# Download from: https://github.com/google/bundletool/releases

# Generate APKs from bundle for testing
java -jar bundletool.jar build-apks --bundle=build/app/outputs/bundle/productionRelease/app-production-release.aab --output=test.apks

# Install on connected device
java -jar bundletool.jar install-apks --apks=test.apks
```

### Verify APK Signing
```bash
# Check APK signature
jarsigner -verify -verbose -certs build/app/outputs/flutter-apk/app-production-release.apk

# Alternative using apksigner
apksigner verify --verbose build/app/outputs/flutter-apk/app-production-release.apk
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. "No keystore.properties found"
**Solution**: Create `android/keystore.properties` with proper signing configuration.

#### 2. "Keystore file not found"
**Solution**: Verify the `storeFile` path in `keystore.properties` is correct.

#### 3. "Wrong keystore password"
**Solution**: Double-check passwords in `keystore.properties`.

#### 4. "Build failed with ProGuard errors"
**Solution**: Check `android/app/proguard-rules.pro` for conflicting rules.

### Debug Commands
```bash
# Clean build cache
flutter clean
cd android && ./gradlew clean && cd ..

# Verbose build output
flutter build apk --release --flavor production --verbose

# Check Gradle configuration
cd android && ./gradlew app:dependencies && cd ..
```

## 📋 Environment Requirements

- ✅ Flutter SDK (latest stable)
- ✅ Android SDK (API level 29+)
- ✅ Java 11 or higher
- ✅ Production keystore configured
- ✅ Environment files (`.env.production`) configured

## 🔒 Security Notes

- The release build automatically removes all debug information
- Logging is disabled in production builds
- Code is obfuscated to prevent reverse engineering
- Unused resources are removed to reduce app size
- Stack traces are removed from production builds

---

**⚠️ CRITICAL REMINDER**: Always test your release builds thoroughly before submitting to Google Play Store. The production environment should be fully configured and validated before building.
