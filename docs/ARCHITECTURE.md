# Architecture Documentation

## Overview

This Flutter application implements **Clean Architecture** with a **feature-first** folder structure and strict dependency rules. The architecture ensures separation of concerns, testability, and maintainability while following SOLID principles.

## Architecture Layers and Dependency Flow

The application follows a **two-layer dependency structure**:

```
Features → Core
```

- **Features Layer**: Contains all business features (auth, home, etc.)
- **Core Layer**: Contains shared infrastructure, utilities, and services
- **Dependency Rule**: Features can depend on Core, but Core cannot depend on Features
- **Feature Isolation**: Features are completely independent of each other

## Clean Architecture Layers

### 1. Domain Layer (Business Logic)
The innermost layer containing business logic and rules.

**Components:**
- **Entities**: Core business objects with business rules
- **Repository Interfaces**: Abstract contracts for data access
- **Use Cases**: Application-specific business rules

**Key Principles:**
- No dependencies on external layers
- Contains pure business logic
- Framework-independent
- Highly testable

**Example Structure:**
```
domain/
├── entities/           # Business objects
│   └── user_entity.dart
├── repositories/       # Abstract repository contracts
│   └── auth_repository.dart
└── usecases/          # Business use cases
    ├── login_usecase.dart
    └── logout_usecase.dart
```

### 2. Data Layer (Data Access)
Handles data retrieval and storage from various sources.

**Components:**
- **Repository Implementations**: Concrete implementations of domain repositories
- **Data Sources**: Remote (API) and local (database, cache) data sources
- **Models**: Data transfer objects with serialization/deserialization

**Key Principles:**
- Implements domain repository interfaces
- Handles data transformation between external and internal formats
- Manages multiple data sources
- Error handling and network connectivity

**Example Structure:**
```
data/
├── datasources/       # Data source implementations
│   ├── auth_remote_datasource.dart
│   └── auth_local_datasource.dart
├── models/           # Data models with JSON serialization
│   └── user_model.dart
└── repositories/     # Repository implementations
    └── auth_repository_impl.dart
```

### 3. Presentation Layer (UI)
Handles user interface and user interactions following MVVM pattern.

**Components:**
- **Screens**: Individual screen implementations following MVVM pattern
- **Widgets**: Reusable UI components (shared and screen-specific)
- **Cubits**: State management using flutter_bloc (ViewModels in MVVM)
- **State Classes**: UI state representations

**MVVM Structure:**
Each screen follows the MVVM pattern with:
- **View**: The main screen widget (e.g., LoginScreen)
- **ViewModel**: Cubit class managing screen state (e.g., LoginCubit)
- **State**: Dedicated state classes for the screen (e.g., LoginState)

**Key Principles:**
- Depends only on domain layer
- Each screen has its own dedicated widgets folder
- Shared widgets remain at feature level
- Screen-specific state management with dedicated cubits
- Reactive UI updates based on state changes

**Example Structure:**
```
presentation/
├── screens/          # Screen-specific MVVM implementations
│   └── login_screen/
│       ├── login_screen.dart      # View
│       ├── login_cubit.dart       # ViewModel
│       ├── login_state.dart       # State
│       └── widgets/               # Screen-specific widgets
│           ├── login_form.dart
│           └── login_header.dart
└── widgets/          # Shared feature widgets
    ├── custom_button.dart
    └── custom_text_field.dart
```

## Core Layer Structure

The Core layer provides foundational services and utilities used across all features:

```
core/
├── common/           # Common utilities and configurations
│   ├── config/       # Environment and app configuration
│   ├── constants/    # Application constants (API, UI, database, etc.)
│   ├── errors/       # Error handling and failure types
│   ├── extensions/   # Dart/Flutter extensions
│   └── utils/        # Utility functions and helpers
├── controller/       # Core controllers and state management
├── data/             # Core data layer infrastructure
│   ├── datasources/  # Database, network, and storage services
│   ├── models/       # Core data models and DTOs
│   └── repositories/ # Base repository implementations
├── di/               # Dependency injection configuration
├── domain/           # Core domain layer
│   ├── entities/     # Core business entities
│   ├── repositories/ # Abstract repository interfaces
│   └── usecases/     # Base use case classes
├── generated/        # Generated code (localization, etc.)
├── l10n/             # Localization files (ARB format)
└── presentation/     # Core presentation layer
    ├── controller/   # Base controllers
    ├── router/       # Navigation and routing configuration
    ├── theme/        # App theming and styling
    └── widgets/      # Reusable UI components
```

## Feature-First Structure

Each feature is organized as a complete module with its own Clean Architecture layers:

```
features/
├── auth/             # Authentication feature
│   ├── controller/   # Feature-specific controllers
│   ├── data/         # Data layer
│   ├── domain/       # Domain layer
│   ├── presentation/ # Presentation layer
│   └── di/           # Dependency injection
└── home/             # Home feature
    ├── controller/   # Feature-specific controllers
    ├── data/         # Data layer
    ├── domain/       # Domain layer
    ├── presentation/ # Presentation layer
    └── di/           # Dependency injection
```

**Benefits:**
- **Modularity**: Each feature is self-contained
- **Scalability**: Easy to add new features without affecting existing ones
- **Team Collaboration**: Different teams can work on different features
- **Code Organization**: Related code is grouped together

## MVVM Pattern Implementation

### Screen Organization
Each screen in the presentation layer follows the MVVM (Model-View-ViewModel) pattern:

**View (Screen)**: The main UI component that displays data and handles user interactions
```dart
class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit(loginUseCase: sl()),
      child: // Screen UI implementation
    );
  }
}
```

**ViewModel (Cubit)**: Manages screen state and business logic
```dart
class LoginCubit extends Cubit<LoginState> {
  LoginCubit({required this.loginUseCase}) : super(const LoginInitial());

  Future<void> login({required String email, required String password}) async {
    emit(const LoginLoading());
    // Business logic implementation
  }
}
```

**State**: Defines all possible states for the screen
```dart
abstract class LoginState extends Equatable {
  const LoginState();
}

class LoginInitial extends LoginState { /* ... */ }
class LoginLoading extends LoginState { /* ... */ }
class LoginSuccess extends LoginState { /* ... */ }
class LoginError extends LoginState { /* ... */ }
```

### Widget Organization
- **Screen-specific widgets**: Located in `features/{feature}/presentation/screens/{screen_name}/widgets/`
- **Feature-level widgets**: Located in `features/{feature}/presentation/widgets/`
- **Core widgets**: Located in `core/presentation/widgets/` for cross-feature reuse

## Core Components

### Base Classes

#### BaseCubit
Provides consistent state management across all features:

```dart
abstract class BaseCubit<T> extends Cubit<BaseState<T>> {
  BaseCubit() : super(const InitialState());

  Future<void> executeUseCase<R>(
    Future<R> Function() useCase, {
    bool showLoading = true,
  }) async {
    // Handles loading, success, and error states automatically using try-catch
  }
}
```

#### Use Cases
Use cases implement business logic directly without inheritance constraints:

```dart
class YourUseCase {
  const YourUseCase(this._repository);

  final YourRepository _repository;

  /// Execute the use case with natural method calls
  /// Throws AppFailure on error, returns success value directly
  Future<YourEntity> execute(String input) async {
    return await _repository.getData(input);
  }
}
```

#### BaseState
Provides consistent state representation:

```dart
abstract class BaseState<T> extends Equatable {
  const BaseState();
  
  // State type checking methods
  bool get isInitial => this is InitialState<T>;
  bool get isLoading => this is LoadingState<T>;
  bool get isSuccess => this is SuccessState<T>;
  bool get isError => this is ErrorState<T>;
}
```

### Error Handling

#### Failure Types
Standardized error representation:

```dart
abstract class Failure extends Equatable {
  const Failure({required this.message});
  final String message;
}

class NetworkFailure extends AppFailure { /* ... */ }
class AuthFailure extends AppFailure { /* ... */ }
class ValidationFailure extends AppFailure { /* ... */ }
```

#### Exception-Based Error Handling
Modern exception-based error handling with AppFailure hierarchy:

```dart
// Use case throws AppFailure on error, returns success value directly
Future<UserEntity> login(LoginParams params) async {
  try {
    final user = await repository.login(params);
    return user;
  } catch (e) {
    if (e is AppFailure) rethrow;
    throw ExceptionHandler.handle(e);
  }
}
```

### Dependency Injection

#### Service Locator Pattern
Using GetIt for dependency management:

```dart
// Global service locator
final GetIt sl = GetIt.instance;

// Feature-specific DI configuration
class AuthDI {
  static Future<void> init() async {
    // Register data sources
    sl.registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(sl()),
    );
    
    // Register repositories
    sl.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
      ),
    );
    
    // Register use cases
    sl.registerLazySingleton(() => LoginUseCase(sl()));
    
    // Register cubits
    sl.registerFactory(() => AuthCubit(loginUseCase: sl()));
  }
}
```

### Navigation

#### Declarative Routing
Using go_router for type-safe navigation:

```dart
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: RouteNames.login,
    routes: [
      GoRoute(
        path: RouteNames.login,
        name: RouteNames.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: RouteNames.home,
        name: RouteNames.home,
        builder: (context, state) => const HomeScreen(),
      ),
    ],
  );
}
```

#### Navigation Extensions
Convenient navigation methods:

```dart
extension AppRouterExtension on BuildContext {
  void goToLogin() => go(RouteNames.login);
  void goToHome() => go(RouteNames.home);
  void goBack() => pop();
}
```

## Data Flow

### Typical Request Flow

1. **User Interaction**: User taps login button
2. **Presentation Layer**: LoginPage calls AuthCubit.login()
3. **State Management**: AuthCubit calls LoginUseCase
4. **Domain Layer**: LoginUseCase validates input and calls AuthRepository
5. **Data Layer**: AuthRepositoryImpl coordinates between remote and local data sources
6. **Network Layer**: AuthRemoteDataSource makes API call using Retrofit AuthApiService
7. **Response Handling**: Data flows back through layers with proper error handling
8. **UI Update**: AuthCubit emits new state, UI rebuilds reactively

### State Management Flow

```
User Action → Cubit Method → Use Case → Repository → Data Source
     ↓              ↓           ↓           ↓           ↓
UI Update ← State Emission ← Either Result ← Data Model ← API Response
```

## Dependency Rules and Architectural Decisions

### Strict Dependency Rules

1. **Features → Core**: Features can depend on Core layer components
2. **Core Independence**: Core layer has zero dependencies on Features
3. **Feature Isolation**: Features cannot depend on other Features
4. **Interface Segregation**: Dependencies are through abstractions, not concrete implementations

### Architectural Decisions and Rationale

#### 1. Two-Layer Architecture (Features → Core)
**Decision**: Use a simplified two-layer architecture instead of three-layer (Features → Shared → Core)

**Rationale**:
- **Simplicity**: Easier to understand and maintain
- **Clear Boundaries**: Obvious separation between feature-specific and shared code
- **Reduced Complexity**: Fewer layers mean less cognitive overhead
- **Practical**: Most shared functionality naturally belongs in infrastructure (Core)

#### 2. Feature-First Organization
**Decision**: Organize code by features rather than by technical layers

**Rationale**:
- **Business Alignment**: Code structure reflects business domains
- **Team Scalability**: Different teams can work on different features independently
- **Maintainability**: Related code is co-located
- **Testability**: Each feature can be tested in isolation

#### 3. Core as Infrastructure Layer
**Decision**: Core layer contains all shared infrastructure, utilities, and services

**Rationale**:
- **Single Source of Truth**: All shared functionality in one place
- **Consistency**: Ensures consistent behavior across features
- **Reusability**: Common services available to all features
- **Maintainability**: Changes to shared functionality in one location

#### 4. Dependency Injection with GetIt
**Decision**: Use GetIt service locator pattern for dependency injection

**Rationale**:
- **Simplicity**: Easy to understand and implement
- **Performance**: Compile-time registration, runtime resolution
- **Flexibility**: Supports both singleton and factory patterns
- **Testing**: Easy to replace dependencies with mocks

#### 5. MVVM with Cubit
**Decision**: Use MVVM pattern with flutter_bloc Cubit for state management

**Rationale**:
- **Separation of Concerns**: Clear separation between View and business logic
- **Testability**: ViewModels (Cubits) can be tested independently
- **Reactive UI**: Automatic UI updates based on state changes
- **Predictable State**: Immutable state objects with clear state transitions

## Current Project Structure

### Implemented Features

#### Authentication Feature (`features/auth/`)
- **Domain**: User entities, authentication repository interfaces, login/logout use cases
- **Data**: Remote API data sources, local storage, repository implementations
- **Presentation**: Login screen with MVVM pattern (LoginScreen, LoginCubit, LoginState)
- **Controller**: Authentication-specific controllers and state management
- **DI**: Feature-specific dependency injection configuration

#### Home Feature (`features/home/<USER>
- **Domain**: Home-specific business logic and entities
- **Data**: Home data sources and repository implementations
- **Presentation**: Home dashboard with MVVM pattern
- **Controller**: Home-specific controllers
- **DI**: Home feature dependency injection

### Core Infrastructure Components

#### Data Infrastructure (`core/data/`)
- **Database**: Drift ORM with SQLite, DAOs, and table definitions
- **Network**: Dio HTTP client with interceptors (auth, error, logging)
- **Storage**: Secure storage and shared preferences services

#### Common Utilities (`core/common/`)
- **Configuration**: Environment configuration and app settings
- **Constants**: Organized constants (API endpoints, UI values, database config)
- **Error Handling**: Centralized error types and failure classes
- **Extensions**: Dart/Flutter extensions for enhanced functionality

#### Presentation Infrastructure (`core/presentation/`)
- **Router**: Auto-route configuration with type-safe navigation
- **Theme**: App theming and styling configuration
- **Widgets**: Reusable UI components (buttons, inputs, layouts)

#### Dependency Injection (`core/di/`)
- **Service Locator**: GetIt configuration with modular registration
- **Initialization**: Centralized dependency setup and configuration

### Design Patterns Used

1. **Repository Pattern**: Abstract data access with multiple data sources
2. **Use Case Pattern**: Encapsulate business logic in single-responsibility classes
3. **MVVM Pattern**: Separate presentation logic from UI components
4. **Service Locator Pattern**: Centralized dependency management
5. **Exception Pattern**: Modern exception-based error handling with AppFailure hierarchy
6. **Observer Pattern**: Reactive state management with flutter_bloc

## Development Guidelines

### Adding New Features

1. **Create Feature Structure**: Follow the established feature-first structure
2. **Implement Domain Layer**: Start with entities, repository interfaces, and use cases
3. **Implement Data Layer**: Create data sources, models, and repository implementations
4. **Implement Presentation Layer**: Create screens, cubits, states, and widgets
5. **Configure Dependencies**: Set up feature-specific dependency injection
6. **Register in Core DI**: Add feature DI initialization to core service locator

### Using Core Components

1. **Check Existing Components**: Before creating new components, check if similar functionality exists in Core
2. **Extend Core When Needed**: Add new shared functionality to appropriate Core modules
3. **Follow Dependency Rules**: Features can use Core, but Core cannot depend on Features
4. **Maintain Consistency**: Use established patterns and conventions

### Code Quality Standards

1. **Clean Architecture**: Maintain clear separation between layers
2. **SOLID Principles**: Follow single responsibility, open/closed, and dependency inversion principles
3. **Naming Conventions**: Use meaningful, descriptive names for classes, methods, and variables
4. **Documentation**: Add comprehensive documentation for public APIs
5. **Testing**: Maintain high test coverage, especially for business logic
6. **Error Handling**: Use Either types for consistent error handling across layers

### Performance Considerations

1. **Lazy Loading**: Use lazy singletons for expensive-to-create services
2. **State Management**: Keep state classes immutable and use Equatable for performance
3. **Database Optimization**: Use appropriate indexes and query optimization
4. **Network Optimization**: Implement proper caching and request deduplication
5. **Memory Management**: Dispose of resources properly and avoid memory leaks

This architecture provides a solid foundation for building scalable, maintainable Flutter applications while ensuring clear separation of concerns and testability across all layers.

## Testing Strategy

### Unit Testing
- **Domain Layer**: Test use cases and entities in isolation
- **Data Layer**: Test repository implementations with mocked data sources
- **Presentation Layer**: Test cubits with mocked use cases

### Integration Testing
- Test complete feature flows
- Test navigation between screens
- Test API integration with mock servers

### Widget Testing
- Test individual widgets and pages
- Test user interactions and state changes
- Test responsive design and accessibility

## Best Practices

### Code Organization
1. **Single Responsibility**: Each class has one reason to change
2. **Dependency Inversion**: Depend on abstractions, not concretions
3. **Interface Segregation**: Create specific interfaces for specific needs
4. **Open/Closed Principle**: Open for extension, closed for modification

### Error Handling
1. Use exception-based pattern with AppFailure hierarchy for all operations that can fail
2. Create specific failure types for different error scenarios (NetworkFailure, AuthFailure, etc.)
3. Handle errors at the appropriate layer using try-catch blocks
4. Provide meaningful error messages to users

### State Management
1. Use BaseCubit for consistent state handling
2. Keep state classes immutable
3. Use Equatable for value equality
4. Emit states only when necessary

### Testing
1. Write tests for all business logic
2. Mock external dependencies
3. Test error scenarios
4. Maintain high test coverage

This architecture provides a solid foundation for building scalable, maintainable Flutter applications while following industry best practices and clean code principles.
