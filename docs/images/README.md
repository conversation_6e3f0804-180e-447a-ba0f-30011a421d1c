# Images Directory

This directory contains visual guides for setting up and running the Flutter project in Android Studio.

## Image Files

### android-studio-edit-configurations.png
Screenshot showing how to access the "Edit Configurations..." menu in Android Studio:
- Shows the dropdown menu next to the Run button
- Highlights the "Edit Configurations..." option
- Displays the available build configurations (Development, Production, Staging)

### android-studio-development-config.png
Screenshot showing the detailed configuration for the Development environment:
- Shows the Run/Debug Configurations dialog
- Displays the Development configuration settings
- Highlights important fields:
  - Additional run args: `--flavor development --dart-define=ENVIRONMENT=development`
  - Build flavor: `development`
  - Other configuration options

## Usage

These images are referenced in the main README.md file to provide visual guidance for developers setting up their Android Studio environment for the Flutter project.

## Adding New Images

When adding new screenshots:
1. Save images in PNG format for best quality
2. Use descriptive filenames
3. Update this README with descriptions
4. Reference images in the main README.md with appropriate Vietnamese captions
