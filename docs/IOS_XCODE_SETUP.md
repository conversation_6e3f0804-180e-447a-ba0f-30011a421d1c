# iOS Xcode Setup Guide - Multi-Environment Build Configuration

This guide provides detailed step-by-step instructions for manually setting up iOS build schemes and configurations in Xcode to support the Flutter multi-environment build system.

## Prerequisites

Before starting, ensure you have:
- ✅ Completed the Android build flavors setup
- ✅ Created the iOS `.xcconfig` files (Development.xcconfig, Staging.xcconfig, Production.xcconfig)
- ✅ Xcode installed and your Flutter project ready
- ✅ Apple Developer account (for code signing)

## Overview

We will create:
- **3 Build Configurations**: Development, Staging, Production
- **3 Schemes**: Runner Development, Runner Staging, Runner Production
- **Proper linking** between configurations and .xcconfig files
- **Code signing** setup for each environment

---

## Step 1: Open Xcode Project

1. **Navigate to your Flutter project directory**
2. **Open the iOS workspace** (NOT the .xcodeproj file):
   ```bash
   open ios/Runner.xcworkspace
   ```
   
   ⚠️ **Important**: Always use `Runner.xcworkspace`, not `Runner.xcodeproj`

---

## Step 2: Create Build Configurations

### 2.1 Access Project Settings

1. **Select the project** in the Project Navigator:
   - Click on "Runner" at the very top of the left sidebar (the blue project icon)
   - Make sure you select the **PROJECT** "Runner", not the target

2. **Go to the Info tab**:
   - In the main editor area, click the "Info" tab
   - You should see "Configurations" section

### 2.2 Create New Configurations

**Current configurations** you should see:
- Debug
- Release
- Profile

**Create Development Configuration:**
1. Click the **"+"** button under Configurations
2. Select **"Duplicate 'Debug' Configuration"**
3. **Rename** it to: `Development`

**Create Staging Configuration:**
1. Click the **"+"** button again
2. Select **"Duplicate 'Release' Configuration"**
3. **Rename** it to: `Staging`

**Create Production Configuration:**
1. Click the **"+"** button again
2. Select **"Duplicate 'Release' Configuration"**
3. **Rename** it to: `Production`

**Final result** - You should now have 6 configurations:
- Debug
- Release
- Profile
- Development
- Staging
- Production

---

## Step 3: Link .xcconfig Files to Configurations

### 3.1 Assign Configuration Files

For each of the NEW configurations (Development, Staging, Production):

1. **Find the configuration** in the list
2. **Expand the configuration** by clicking the disclosure triangle
3. **For both Runner and Pods projects**, set the configuration file:

**Development Configuration:**
- Runner: Select `Flutter/Development`
- Pods: Select `Flutter/Development`

**Staging Configuration:**
- Runner: Select `Flutter/Staging`
- Pods: Select `Flutter/Staging`

**Production Configuration:**
- Runner: Select `Flutter/Production`
- Pods: Select `Flutter/Production`

### 3.2 Verify Configuration Files

The dropdown should show:
- `Flutter/Development`
- `Flutter/Staging`
- `Flutter/Production`

If these don't appear, the .xcconfig files weren't created properly.

---

## Step 4: Create Schemes

### 4.1 Access Scheme Manager

1. **Go to Product menu** → **Scheme** → **Manage Schemes...**
2. You should see the current "Runner" scheme

### 4.2 Create Development Scheme

1. **Select the "Runner" scheme**
2. **Click the gear icon** → **Duplicate**
3. **Rename** to: `Runner Development`
4. **Check "Shared"** checkbox (important for team collaboration)
5. **Click "Close"**

### 4.3 Create Staging Scheme

1. **Select the "Runner" scheme** again
2. **Click the gear icon** → **Duplicate**
3. **Rename** to: `Runner Staging`
4. **Check "Shared"** checkbox
5. **Click "Close"**

### 4.4 Create Production Scheme

1. **Select the "Runner" scheme** again
2. **Click the gear icon** → **Duplicate**
3. **Rename** to: `Runner Production`
4. **Check "Shared"** checkbox
5. **Click "Close"**

---

## Step 5: Configure Each Scheme

### 5.1 Configure Development Scheme

1. **Select "Runner Development"** scheme
2. **Click "Edit..."** button
3. **Configure each action**:

**Build:**
- Build Configuration: `Development`

**Run:**
- Build Configuration: `Development`
- Arguments tab → Environment Variables:
  - Add: `ENVIRONMENT` = `development`

**Test:**
- Build Configuration: `Development`

**Profile:**
- Build Configuration: `Development`

**Analyze:**
- Build Configuration: `Development`

**Archive:**
- Build Configuration: `Development`

4. **Click "Close"**

### 5.2 Configure Staging Scheme

1. **Select "Runner Staging"** scheme
2. **Click "Edit..."** button
3. **Configure each action**:

**Build:**
- Build Configuration: `Staging`

**Run:**
- Build Configuration: `Staging`
- Arguments tab → Environment Variables:
  - Add: `ENVIRONMENT` = `staging`

**Test:**
- Build Configuration: `Staging`

**Profile:**
- Build Configuration: `Staging`

**Analyze:**
- Build Configuration: `Staging`

**Archive:**
- Build Configuration: `Staging`

4. **Click "Close"**

### 5.3 Configure Production Scheme

1. **Select "Runner Production"** scheme
2. **Click "Edit..."** button
3. **Configure each action**:

**Build:**
- Build Configuration: `Production`

**Run:**
- Build Configuration: `Production`
- Arguments tab → Environment Variables:
  - Add: `ENVIRONMENT` = `production`

**Test:**
- Build Configuration: `Production`

**Profile:**
- Build Configuration: `Production`

**Analyze:**
- Build Configuration: `Production`

**Archive:**
- Build Configuration: `Production`

4. **Click "Close"**

---

## Step 6: Configure Code Signing

### 6.1 Get Your Development Team ID

1. **Go to Apple Developer Portal**: https://developer.apple.com/account
2. **Sign in** with your Apple ID
3. **Go to Membership** section
4. **Copy your Team ID** (10-character string)

### 6.2 Update .xcconfig Files

**Edit each .xcconfig file** and replace `YOUR_DEVELOPMENT_TEAM_ID`:

**ios/Flutter/Development.xcconfig:**
```
DEVELOPMENT_TEAM = YOUR_ACTUAL_TEAM_ID
```

**ios/Flutter/Staging.xcconfig:**
```
DEVELOPMENT_TEAM = YOUR_ACTUAL_TEAM_ID
```

**ios/Flutter/Production.xcconfig:**
```
DEVELOPMENT_TEAM = YOUR_ACTUAL_TEAM_ID
```

### 6.3 Verify Code Signing Settings

1. **Select the Runner target** (not project)
2. **Go to Build Settings tab**
3. **Search for "Code Signing"**
4. **Verify for each configuration**:
   - Code Signing Identity: `Apple Development` (for Development)
   - Code Signing Identity: `Apple Distribution` (for Staging/Production)
   - Development Team: Should show your team name

---

## Step 7: Verify Build Settings

### 7.1 Check Key Settings

1. **Select Runner target**
2. **Build Settings tab**
3. **Search and verify**:

**Product Bundle Identifier:**
- Development: `jp.co.iotbank.glidici.dev`
- Staging: `jp.co.iotbank.glidici.staging`
- Production: `jp.co.iotbank.glidici`

**Product Name:**
- Development: `$(PRODUCT_NAME)`
- Staging: `$(PRODUCT_NAME)`
- Production: `$(PRODUCT_NAME)`

**Display Name:**
- Development: `$(DISPLAY_NAME)`
- Staging: `$(DISPLAY_NAME)`
- Production: `$(DISPLAY_NAME)`

These values come from the .xcconfig files.

---

## Step 8: Test the Setup

### 8.1 Test Scheme Switching

1. **In Xcode toolbar**, click the scheme selector (next to the stop button)
2. **You should see**:
   - Runner Development
   - Runner Staging
   - Runner Production
3. **Select each scheme** and verify the bundle identifier changes

### 8.2 Test Building

**Test Development Build:**
1. **Select "Runner Development" scheme**
2. **Select a simulator** or device
3. **Press Cmd+B** to build
4. **Verify** it builds successfully

**Test from Command Line:**
```bash
# Development
flutter build ios --debug --flavor development --dart-define=ENVIRONMENT=development

# Staging
flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging

# Production
flutter build ios --release --flavor production --dart-define=ENVIRONMENT=production
```

### 8.3 Verify App Installation

1. **Build and run** each scheme
2. **Check the app name** on the device/simulator:
   - Development: "Glidic Dev"
   - Staging: "Glidic Staging"
   - Production: "Glidic"
3. **Verify** you can install all three versions simultaneously

---

## Step 9: Final Verification

### 9.1 Check Project Structure

Your project should now have:
```
ios/
├── Flutter/
│   ├── Development.xcconfig
│   ├── Staging.xcconfig
│   └── Production.xcconfig
├── Runner.xcworkspace/
└── Runner.xcodeproj/
    └── xcshareddata/
        └── xcschemes/
            ├── Runner Development.xcscheme
            ├── Runner Staging.xcscheme
            └── Runner Production.xcscheme
```

### 9.2 Test Build Scripts

```bash
# Test all build scripts
./scripts/build_dev.sh
./scripts/build_staging.sh
./scripts/build_production.sh
```

---

## Troubleshooting

### Common Issues

**1. "No such module" errors:**
- Clean build folder: Product → Clean Build Folder
- Delete derived data: Xcode → Preferences → Locations → Derived Data → Delete

**2. Code signing errors:**
- Verify Team ID in .xcconfig files
- Check provisioning profiles in Apple Developer Portal
- Ensure bundle IDs are registered

**3. Scheme not found:**
- Ensure schemes are marked as "Shared"
- Check that .xcscheme files exist in xcshareddata/xcschemes/

**4. Configuration file not found:**
- Verify .xcconfig files exist in ios/Flutter/
- Check file paths in project configurations

### Debug Commands

```bash
# Check current schemes
xcodebuild -list -workspace ios/Runner.xcworkspace

# Build specific scheme
xcodebuild -workspace ios/Runner.xcworkspace -scheme "Runner Development" -configuration Development

# Clean everything
flutter clean
cd ios && rm -rf build/ && cd ..
```

---

## Success Checklist

- ✅ 6 build configurations created (Debug, Release, Profile, Development, Staging, Production)
- ✅ 3 custom schemes created and configured
- ✅ .xcconfig files linked to appropriate configurations
- ✅ Code signing configured with valid Team ID
- ✅ All schemes build successfully
- ✅ Apps install with correct names and bundle IDs
- ✅ Command-line builds work with flavors
- ✅ Build scripts execute without errors

**🎉 Congratulations!** Your iOS multi-environment build system is now complete and matches your Android flavor configuration!

---

## Visual Reference Guide

### Xcode Project Navigator Layout
```
┌─ Project Navigator ─────────────────┐
│ 📁 Runner (Project - Blue Icon)     │ ← Click this for project settings
│   📁 Runner (Target - App Icon)     │ ← Click this for target settings
│     📄 AppDelegate.swift            │
│     📄 Info.plist                   │
│   📁 Flutter                        │
│     📄 Development.xcconfig          │
│     📄 Staging.xcconfig             │
│     📄 Production.xcconfig          │
│   📁 Pods                           │
└─────────────────────────────────────┘
```

### Project Info Tab - Configurations Section
```
┌─ Configurations ────────────────────────────────────────┐
│                                                         │
│ Debug          │ Runner: None    │ Pods: None           │
│ Release        │ Runner: None    │ Pods: None           │
│ Profile        │ Runner: None    │ Pods: None           │
│ Development    │ Runner: Flutter/Development │ Pods: Flutter/Development │
│ Staging        │ Runner: Flutter/Staging     │ Pods: Flutter/Staging     │
│ Production     │ Runner: Flutter/Production  │ Pods: Flutter/Production  │
│                                                         │
│ [+] [-] [Duplicate] [Rename]                           │
└─────────────────────────────────────────────────────────┘
```

### Scheme Manager Dialog
```
┌─ Manage Schemes ────────────────────────────────────────┐
│                                                         │
│ ☑ Runner                           [Edit...] [⚙]       │
│ ☑ Runner Development               [Edit...] [⚙]       │
│ ☑ Runner Staging                   [Edit...] [⚙]       │
│ ☑ Runner Production                [Edit...] [⚙]       │
│                                                         │
│ ☑ = Shared    [+] [-] [Duplicate]           [Close]    │
└─────────────────────────────────────────────────────────┘
```

### Scheme Editor - Build Configuration Settings
```
┌─ Edit Scheme: Runner Development ───────────────────────┐
│                                                         │
│ ┌─ Build ─┐ ┌─ Run ─┐ ┌─ Test ─┐ ┌─ Profile ─┐ ┌─ Archive ─┐ │
│ │         │ │       │ │        │ │          │ │           │ │
│                                                         │
│ Build Configuration: [Development ▼]                   │
│                                                         │
│ ┌─ Arguments ─────────────────────────────────────────┐ │
│ │ Environment Variables:                              │ │
│ │ ENVIRONMENT = development                           │ │
│ │ [+] [-]                                            │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│                                    [Cancel] [Close]    │
└─────────────────────────────────────────────────────────┘
```

### Xcode Toolbar - Scheme Selector
```
┌─ Xcode Toolbar ─────────────────────────────────────────┐
│ ▶ ⏹ [Runner Development ▼] [iPhone 15 Pro ▼]          │
│                                                         │
│ Available Schemes:                                      │
│ • Runner Development                                    │
│ • Runner Staging                                        │
│ • Runner Production                                     │
│ • Runner (original)                                     │
└─────────────────────────────────────────────────────────┘
```

---

## Quick Reference Commands

### Terminal Commands for Testing
```bash
# Open Xcode workspace
open ios/Runner.xcworkspace

# List available schemes
xcodebuild -list -workspace ios/Runner.xcworkspace

# Build specific configuration
flutter build ios --debug --flavor development
flutter build ios --release --flavor staging
flutter build ios --release --flavor production

# Run build scripts
./scripts/build_dev.sh      # Development
./scripts/build_staging.sh  # Staging
./scripts/build_production.sh # Production
```

### Expected Bundle Identifiers
```
Development: jp.co.iotbank.glidici.dev → "Glidic Dev"
Staging:     jp.co.iotbank.glidici.staging → "Glidic Staging"
Production:  jp.co.iotbank.glidici     → "Glidic"
```

---

## Final Notes

- **Always use Runner.xcworkspace**, never Runner.xcodeproj
- **Mark schemes as "Shared"** for team collaboration
- **Test each scheme** before committing changes
- **Keep .xcconfig files in version control**
- **Update Team ID** in all .xcconfig files for code signing
- **Clean build folder** if you encounter issues

Your iOS setup now perfectly mirrors the Android flavor system! 🎉
