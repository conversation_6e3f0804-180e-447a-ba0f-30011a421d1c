# Live Activity Setup for iOS

This document describes how to set up iOS Live Activities for the Glidic recording app.

## Overview

Live Activities display real-time recording status in the Dynamic Island and on the lock screen when users are recording audio. The implementation shows a simple "録音中" (Recording) message to indicate active recording sessions.

## Requirements

- iOS 16.1 or later
- Xcode 14.1 or later
- Apple Developer Account (for App Groups capability)

## Setup Steps

### 1. Add Widget Extension to iOS Project

1. Open `ios/Runner.xcworkspace` in Xcode
2. In Xcode, click **File** → **New** → **Target...**
3. Select **Widget Extension** and click **Next**
4. Set the Product Name (e.g., `GlidicWidget`)
5. Ensure "Runner" is selected in "Embed in Application" dropdown
6. Click **Finish**
7. When prompted, click **Activate** to activate the scheme

### 2. Enable Required Capabilities

#### For Runner Target:
1. Select the **Runner** target in Xcode
2. Go to **Signing & Capabilities** tab
3. Add the following capabilities:
   - **Push Notifications**
   - **App Groups**

#### For Widget Extension Target:
1. Select your **Widget Extension** target
2. Go to **Signing & Capabilities** tab
3. Add the following capability:
   - **App Groups**

### 3. Configure App Groups

1. For both **Runner** and **Widget Extension** targets:
   - In the **App Groups** capability
   - Check the box next to `group.jp.co.iotbank.glidici.shared`
   - If this group doesn't exist, create it in your Apple Developer account

### 4. Enable Live Activities in Info.plist

Add the following to **both** `Runner/Info.plist` and `YourWidgetExtension/Info.plist`:

```xml
<key>NSSupportsLiveActivities</key>
<true/>
```

### 5. Implement Widget Extension

Replace the content of your widget extension file (e.g., `GlidicWidgetLiveActivity.swift`) with:

```swift
import ActivityKit
import SwiftUI
import WidgetKit

// Live Activity data model - MUST be named exactly "LiveActivitiesAppAttributes"
struct LiveActivitiesAppAttributes: ActivityAttributes, Identifiable {
    public typealias LiveDeliveryData = ContentState
    
    public struct ContentState: Codable, Hashable {
        var data: [String: String]
    }
    
    var id = UUID()
}

// Helper extension for prefixed keys
extension LiveActivitiesAppAttributes {
    func prefixedKey(_ key: String) -> String {
        return "\(id)_\(key)"
    }
}

@available(iOSApplicationExtension 16.1, *)
struct GlidicWidgetLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: LiveActivitiesAppAttributes.self) { context in
            // Lock screen/banner UI
            RecordingLockScreenView(context: context)
                .activityBackgroundTint(Color.red.opacity(0.1))
                .activitySystemActionForegroundColor(Color.red)
        } dynamicIsland: { context in
            // Dynamic Island UI
            DynamicIsland {
                // Expanded UI
                DynamicIslandExpandedRegion(.leading) {
                    Image(systemName: "record.circle.fill")
                        .foregroundColor(.red)
                        .font(.title2)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text(getRecordingStatus(context: context))
                        .font(.caption)
                        .foregroundColor(.red)
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text(getRecordingTitle(context: context))
                        .font(.caption)
                        .multilineTextAlignment(.center)
                }
            } compactLeading: {
                Image(systemName: "record.circle.fill")
                    .foregroundColor(.red)
            } compactTrailing: {
                Text("録音中")
                    .font(.caption2)
                    .foregroundColor(.red)
            } minimal: {
                Image(systemName: "record.circle.fill")
                    .foregroundColor(.red)
            }
            .keylineTint(Color.red)
        }
    }
}

struct RecordingLockScreenView: View {
    let context: ActivityViewContext<LiveActivitiesAppAttributes>
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "record.circle.fill")
                    .foregroundColor(.red)
                    .font(.title2)
                
                VStack(alignment: .leading) {
                    Text(getRecordingStatus(context: context))
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    if let title = getRecordingTitle(context: context), !title.isEmpty {
                        Text(title)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }
}

// Helper functions to extract data
func getRecordingStatus(context: ActivityViewContext<LiveActivitiesAppAttributes>) -> String {
    // Create shared UserDefaults with app group
    let sharedDefault = UserDefaults(suiteName: "group.jp.co.iotbank.glidici.shared")!
    return sharedDefault.string(forKey: context.attributes.prefixedKey("status")) ?? "録音中"
}

func getRecordingTitle(context: ActivityViewContext<LiveActivitiesAppAttributes>) -> String? {
    let sharedDefault = UserDefaults(suiteName: "group.jp.co.iotbank.glidici.shared")!
    return sharedDefault.string(forKey: context.attributes.prefixedKey("title"))
}
```

### 6. Update App Group ID in Flutter Code

If you're using a different App Group ID, update it in the `LiveActivityService`:

```dart
// In lib/core/services/live_activity_service_ipml.dart
await _liveActivitiesPlugin!.init(
  appGroupId: 'group.jp.co.iotbank.glidici.shared', // Your App Group ID
  urlScheme: 'glidic',
);
```

## Testing

1. Build and run the app on a physical iOS device (iOS 16.1+)
2. Grant microphone permissions when prompted
3. Start a recording from the recording screen
4. You should see the Live Activity appear in:
   - Dynamic Island (on supported devices)
   - Lock screen notification
   - Notification panel

## Troubleshooting

### Live Activity Doesn't Appear

1. **Check iOS Version**: Ensure device is running iOS 16.1 or later
2. **Verify Capabilities**: Ensure both targets have correct App Groups configured
3. **Check App Group ID**: Verify the same App Group ID is used in both Flutter and Swift code
4. **Live Activities Setting**: Check if Live Activities are enabled in device Settings → Notifications
5. **Widget Name**: Ensure the ActivityAttributes struct is named exactly `LiveActivitiesAppAttributes`

### Common Issues

- **Build Errors**: Ensure deployment target is set to iOS 16.1+ for the widget extension
- **No Data Display**: Verify UserDefaults key names match between Flutter and Swift
- **Activity Not Updating**: Check that the live activity service is properly initialized

## App Store Submission

When submitting to the App Store:

1. Ensure all capabilities are properly configured
2. Test Live Activities on multiple device types
3. Include screenshots showing Live Activity functionality
4. Mention Live Activity support in app description if desired

## References

- [Apple's Live Activities Documentation](https://developer.apple.com/documentation/activitykit)
- [live_activities Flutter Package](https://pub.dev/packages/live_activities)
- [iOS Human Interface Guidelines - Live Activities](https://developer.apple.com/design/human-interface-guidelines/live-activities) 