# Environment Configuration Guide

This guide explains how to set up and manage environment configurations for the Glidic Flutter application across development, staging, and production environments.

## Overview

The Glidic app uses a robust environment configuration system that supports:
- **Development**: Local development with debug features enabled
- **Staging**: Pre-production testing environment
- **Production**: Live production environment with security optimizations

## Environment Files

### File Structure
```
.env.example          # Template file (committed to version control)
.env.development      # Development configuration (NOT committed)
.env.staging         # Staging configuration (NOT committed)
.env.production      # Production configuration (NOT committed)
.env                 # Active configuration (NOT committed)
```

### Security
⚠️ **IMPORTANT**: All `.env.*` files (except `.env.example`) are excluded from version control for security reasons.

## Quick Setup

### 1. Initial Setup
```bash
# Copy the example file to create your environment files
cp .env.example .env.development
cp .env.example .env.staging
cp .env.example .env.production
```

### 2. Configure Each Environment
Edit each environment file with appropriate values:

#### Development (.env.development)
- Use local/development API endpoints
- Enable debug features
- Use test API keys
- Enable mock data if needed

#### Staging (.env.staging)
- Use staging API endpoints
- Disable debug features
- Use staging API keys
- Enable analytics and monitoring

#### Production (.env.production)
- Use production API endpoints
- Disable all debug features
- Use production API keys
- Enable all security features

## Configuration Variables

### Required Variables
These variables must be set in all environment files:
- `ENVIRONMENT`: Environment name (development/staging/production)
- `BASE_URL`: API base URL
- `API_VERSION`: API version identifier

### Network Configuration
- `CONNECT_TIMEOUT_SECONDS`: Connection timeout (default: 30)
- `RECEIVE_TIMEOUT_SECONDS`: Receive timeout (default: 30)
- `SEND_TIMEOUT_SECONDS`: Send timeout (default: 30)

### Authentication
- `JWT_SECRET`: JWT secret key
- `OAUTH_CLIENT_ID`: OAuth client ID
- `OAUTH_CLIENT_SECRET`: OAuth client secret

### Database
- `DATABASE_URL`: Database connection URL
- `DATABASE_NAME`: Database name

### Third-party Services
- `GOOGLE_MAPS_API_KEY`: Google Maps API key
- `FIREBASE_API_KEY`: Firebase API key
- `ANALYTICS_TRACKING_ID`: Analytics tracking ID

### Feature Flags
- `ENABLE_ANALYTICS`: Enable/disable analytics
- `ENABLE_FEATURE_X`: Enable/disable specific features

### Debug Settings
- `DEBUG_MODE`: Enable/disable debug mode
- `ENABLE_LOGGING`: Enable/disable logging
- `ENABLE_MOCK_DATA`: Enable/disable mock data
- `ENABLE_DEBUG_OVERLAY`: Enable/disable debug overlays

## Building for Different Environments

### Using Build Scripts
The project includes build scripts for each environment:

```bash
# Development build
./scripts/build_dev.sh

# Staging build
./scripts/build_staging.sh

# Production build
./scripts/build_production.sh
```

### Manual Build Commands

#### Development
```bash
cp .env.development .env
flutter build apk --debug --flavor development
```

#### Staging
```bash
cp .env.staging .env
flutter build apk --release --flavor staging
```

#### Production
```bash
cp .env.production .env
flutter build apk --release --flavor production --obfuscate --split-debug-info=build/debug-info
```

## Environment Detection

The app automatically detects the environment using:
1. Explicit environment parameter in `EnvConfig.init()`
2. `ENVIRONMENT` variable in the loaded .env file
3. Build mode detection (debug vs release)

## Usage in Code

### Basic Usage
```dart
import 'package:glidic_app/core/config/env_config.dart';

// Initialize (usually in main.dart)
await EnvConfig.init();

// Access configuration
String apiUrl = EnvConfig.baseUrl;
bool isDebug = EnvConfig.isDebugMode;
String apiKey = EnvConfig.firebaseApiKey;
```

### Environment-Specific Logic
```dart
if (EnvConfig.isDevelopment) {
  // Development-specific code
} else if (EnvConfig.isStaging) {
  // Staging-specific code
} else if (EnvConfig.isProduction) {
  // Production-specific code
}
```

### Custom Variables
```dart
// String values
String customValue = EnvConfig.getCustom('CUSTOM_KEY', defaultValue: 'default');

// Boolean values
bool customFlag = EnvConfig.getCustomBool('CUSTOM_FLAG', defaultValue: false);

// Integer values
int customNumber = EnvConfig.getCustomInt('CUSTOM_NUMBER', defaultValue: 0);
```

## Best Practices

### Security
1. **Never commit environment files** (except .env.example)
2. **Use strong, unique secrets** in production
3. **Rotate secrets regularly**
4. **Use different API keys** for each environment

### Development
1. **Keep .env.example updated** with all required variables
2. **Document new environment variables**
3. **Use meaningful default values**
4. **Validate required variables** on app startup

### Deployment
1. **Test each environment** thoroughly
2. **Verify configuration** before deployment
3. **Use build scripts** for consistency
4. **Monitor environment-specific metrics**

## Troubleshooting

### Common Issues

#### "Environment file not found"
- Ensure the environment file exists
- Check file permissions
- Verify the file is included in pubspec.yaml assets

#### "Missing required environment variables"
- Check that all required variables are set
- Verify variable names match exactly
- Ensure values are not empty

#### "Wrong environment loaded"
- Check the ENVIRONMENT variable value
- Verify the correct .env file is being used
- Clear app data and rebuild

### Debug Information
In development mode, you can access debug information:
```dart
// Get environment summary
Map<String, dynamic> summary = EnvConfig.getEnvironmentSummary();
print(summary);

// Get all environment variables
Map<String, String> allVars = EnvConfig.getAllEnvVars();
print(allVars);
```

## Support

For questions or issues with environment configuration:
1. Check this documentation
2. Review the .env.example file
3. Check the build scripts
4. Contact the development team
