# =============================================================================
# GRADLE BUILD PERFORMANCE OPTIMIZATIONS
# =============================================================================

# Increase memory allocation for better build performance
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC

# Enable parallel builds and daemon
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configureondemand=true

# Enable build cache for faster incremental builds
org.gradle.caching=true

# =============================================================================
# ANDROID BUILD OPTIMIZATIONS
# =============================================================================

# Enable AndroidX and Jetifier
android.useAndroidX=true
android.enableJetifier=true

# Enable R8 full mode for better optimization
android.enableR8.fullMode=true

# Enable non-transitive R classes for faster builds
android.nonTransitiveRClass=true

# Enable non-final resource IDs for faster builds
android.nonFinalResIds=true

# =============================================================================
# SECURITY AND RELEASE BUILD SETTINGS
# =============================================================================

# Enable build features for release optimization
# android.enableBuildCache=true  # Deprecated in AGP 7.0+, use Gradle build cache instead

# Disable unnecessary features in release builds
# android.enableSeparateAnnotationProcessing=true  # Deprecated in AGP 4.0+

# =============================================================================
# FLUTTER SPECIFIC SETTINGS
# =============================================================================

# Flutter build optimizations
flutter.compilationTraceEnabled=true
flutter.trackWidgetCreation=false
