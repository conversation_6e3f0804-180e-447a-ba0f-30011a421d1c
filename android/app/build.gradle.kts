import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

fun readVersionCode(): Int {
    val localProperties = Properties()
    val localPropertiesFile = rootProject.file("local.properties")
    if (localPropertiesFile.exists()) {
        localPropertiesFile.inputStream().use { stream -> localProperties.load(stream) }
    }
    return localProperties.getProperty("flutter.versionCode")?.toInt() ?: 1
}

fun readVersionName(): String {
    val localProperties = Properties()
    val localPropertiesFile = rootProject.file("local.properties")
    if (localPropertiesFile.exists()) {
        localPropertiesFile.inputStream().use { stream -> localProperties.load(stream) }
    }
    return localProperties.getProperty("flutter.versionName") ?: "1.0"
}

fun loadKeystoreProperties(): Properties? {
    val keystorePropertiesFile = rootProject.file("keystore.properties")
    return if (keystorePropertiesFile.exists()) {
        val keystoreProperties = Properties()
        keystoreProperties.load(FileInputStream(keystorePropertiesFile))
        keystoreProperties
    } else {
        null
    }
}

android {
    namespace = "jp.co.iotbank.glidici"
    compileSdk = 35
    // TODO: The NDK version should be checked and updated if necessary.
    ndkVersion = "27.0.********"

     compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        applicationId = "jp.co.iotbank.glidici"
        minSdk = 29
        targetSdk = 35
        versionCode = readVersionCode()
        versionName = readVersionName()
        resValue("string", "app_name", "Glidic")
        buildConfigField("String", "ENVIRONMENT", "\"production\"")
        buildConfigField("boolean", "DEBUG_MODE", "false")
    }

    signingConfigs {
        getByName("debug") {}
        create("release") {
            val keystoreProperties = loadKeystoreProperties()
            if (keystoreProperties != null) {
                storeFile = file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String
                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
            } else {
                // Temporary fallback to allow configuration phase to complete
                // The actual validation will happen during build execution
                println("WARNING: keystore.properties not found. Release builds will fail without proper keystore configuration.")
                storeFile = file("debug.keystore")
                storePassword = "android"
                keyAlias = "androiddebugkey"
                keyPassword = "android"
            }
        }
    }

    buildTypes {
        getByName("debug") {
            isMinifyEnabled = false
            isShrinkResources = false
            signingConfig = signingConfigs.getByName("debug")
            buildConfigField("boolean", "ENABLE_LOGGING", "true")
        }
        getByName("release") {
            isMinifyEnabled = true
            isShrinkResources = true
            // Signing config will be set per flavor (see productFlavors section)
            // Production uses "release" config, dev/staging use "debugRelease" config
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            buildConfigField("boolean", "ENABLE_LOGGING", "false")

            // Additional optimizations for release builds
            isDebuggable = false
            isJniDebuggable = false
            // isRenderscriptDebuggable = false  // Deprecated in AGP 9.0
            isPseudoLocalesEnabled = false

            // Enable ZIP alignment and compression
            isZipAlignEnabled = true
            isCrunchPngs = true
        }
    }

    flavorDimensions.add("environment")

    productFlavors {
        create("development") {
            dimension = "environment"
            applicationId = "jp.co.iotbank.glidici.dev"
            versionNameSuffix = "-dev"
            resValue("string", "app_name", "Glidic (Dev)")
            buildConfigField("String", "ENVIRONMENT", "\"development\"")
            buildConfigField("boolean", "DEBUG_MODE", "true")
        }
        create("staging") {
            dimension = "environment"
            applicationId = "jp.co.iotbank.glidici.staging"
            versionNameSuffix = "-staging"
            resValue("string", "app_name", "Glidic (Staging)")
            buildConfigField("String", "ENVIRONMENT", "\"staging\"")
            buildConfigField("boolean", "DEBUG_MODE", "false")
        }
        create("production") {
            dimension = "environment"
            applicationId = "jp.co.iotbank.glidici"
            resValue("string", "app_name", "Glidic")
            buildConfigField("String", "ENVIRONMENT", "\"production\"")
            buildConfigField("boolean", "DEBUG_MODE", "false")
        }
    }
}

flutter {
    source = "../.."
}

// App Bundle configuration for Google Play Store
android.bundle {
    language {
        // Enable language-based APK splits for smaller downloads
        enableSplit = true
    }
    density {
        // Enable density-based APK splits for different screen densities
        enableSplit = true
    }
    abi {
        // Enable ABI-based APK splits for different CPU architectures
        enableSplit = true
    }
}

// Validate keystore.properties for release builds during execution
gradle.taskGraph.whenReady {
    allTasks.forEach { task ->
        if (task.name.contains("Release") && (task.name.startsWith("assemble") || task.name.startsWith("bundle"))) {
            task.doFirst {
                val keystoreProperties = loadKeystoreProperties()
                if (keystoreProperties == null) {
                    throw GradleException(
                        "Release builds require keystore.properties file. " +
                        "Please create keystore.properties in the android/ directory with proper signing configuration. " +
                        "This applies to development, staging, and production environments as they will be distributed through app stores."
                    )
                }
            }
        }
    }
}
