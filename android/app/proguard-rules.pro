# Flutter Proguard Rules
# Add project specific ProGuard rules here.

# Keep Flutter engine classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-dontwarn io.flutter.**

# Keep Dart VM classes
-keep class com.google.dart.** { *; }
-dontwarn com.google.dart.**

# Keep application classes
-keep class jp.co.iotbank.glidici.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep annotations
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Keep line numbers for debugging
-keepattributes SourceFile,LineNumberTable

# Firebase specific rules (if using Firebase)
-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.**

# Gson specific rules (if using Gson)
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# OkHttp and Retrofit rules (if using)
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove print statements in release builds
-assumenosideeffects class java.io.PrintStream {
    public void println(%);
    public void print(%);
}

# Keep BuildConfig
-keep class **.BuildConfig { *; }

# Keep R class
-keep class **.R
-keep class **.R$* {
    <fields>;
}

# =============================================================================
# SECURITY AND OBFUSCATION ENHANCEMENTS
# =============================================================================

# Aggressive obfuscation settings
-overloadaggressively
-repackageclasses ''
-allowaccessmodification

# Remove unused code more aggressively
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5

# Obfuscate package names and class names
-flattenpackagehierarchy
-repackageclasses 'obfuscated'

# Remove debug information
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
    static void checkNotNullParameter(java.lang.Object, java.lang.String);
    static void checkExpressionValueIsNotNull(java.lang.Object, java.lang.String);
    static void checkNotNullExpressionValue(java.lang.Object, java.lang.String);
    static void checkReturnedValueIsNotNull(java.lang.Object, java.lang.String);
    static void checkFieldIsNotNull(java.lang.Object, java.lang.String);
    static void throwUninitializedPropertyAccessException(java.lang.String);
}

# =============================================================================
# GOOGLE PLAY STORE OPTIMIZATIONS
# =============================================================================

# Keep Google Play Services
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Keep Google Play Billing
-keep class com.android.billingclient.** { *; }
-dontwarn com.android.billingclient.**

# Keep Google Play Core
-keep class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**

# =============================================================================
# FLUTTER AND DART SPECIFIC OPTIMIZATIONS
# =============================================================================

# More aggressive Flutter optimizations
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }

# Keep Dart-related classes
-keep class com.google.dart.** { *; }

# Keep method channels
-keep class * extends io.flutter.plugin.common.MethodChannel$MethodCallHandler { *; }
-keep class * extends io.flutter.plugin.common.EventChannel$StreamHandler { *; }

# =============================================================================
# FLUTTER PLUGIN SPECIFIC RULES
# =============================================================================

# Device Calendar Plugin - Prevent obfuscation of calendar-related classes
# This is required even though the plugin documentation states rules are not needed from v4.3.2
# due to the aggressive obfuscation settings used in this app
-keep class com.builttoroam.devicecalendar.** { *; }
-keep class com.builttoroam.devicecalendar.models.** { *; }
-keep class com.builttoroam.devicecalendar.common.** { *; }
-dontwarn com.builttoroam.devicecalendar.**

# Keep Android Calendar Provider classes that the plugin uses
-keep class android.provider.CalendarContract** { *; }
-keep class android.provider.CalendarContract$** { *; }

# Keep permission-related classes for calendar access
-keep class android.Manifest$permission { *; }

# Keep timezone-related classes used by device_calendar plugin
-keep class org.threeten.bp.** { *; }
-dontwarn org.threeten.bp.**

# Keep reflection-based classes that might be used by the plugin
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# =============================================================================
# ADDITIONAL SECURITY MEASURES
# =============================================================================

# Obfuscate sensitive strings (API keys, URLs, etc.)
-adaptclassstrings
-adaptresourcefilenames
-adaptresourcefilecontents

# Remove stack traces in production
-assumenosideeffects class java.lang.Throwable {
    public void printStackTrace();
}
