# Configuration file for Flutter localization code generation
# This file defines how the localization files should be processed

# ARB files directory
arb-dir: lib/core/l10n

# Template ARB file (English as the source language)
template-arb-file: app_en.arb

# Output directory for generated localization classes
output-dir: lib/core/generated/l10n

# Output localization file name
output-localization-file: app_localizations.dart

# Output class name for the main localization class
output-class: AppLocalizations

# Preferred supported locales (in order of preference)
preferred-supported-locales: ["en", "ja"]

# Whether to use deferred loading for localization
use-deferred-loading: false

# Whether to generate synthetic packages
synthetic-package: false

# Whether to generate nullable getters
nullable-getter: false

# Whether to format generated files
format: true
