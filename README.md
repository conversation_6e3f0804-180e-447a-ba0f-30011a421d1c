# Glidic App

A modern Flutter application built with Clean Architecture, implementing feature-first folder structure and MVVM pattern.
Glidic App serves as a production-ready foundation for scalable Flutter applications with proper separation of concerns, dependency injection, state management, database layer, code-generated routing, and internationalization support.

**Supported Platforms**: This Flutter project supports **Android** and **iOS** platforms only.

## 🏗️ Project Overview

This project follows **Clean Architecture** principles with a **feature-first** approach and **MVVM pattern** in the presentation layer, ensuring:

- **Clean Architecture**: Clear separation between domain, data, and presentation layers with strict dependency rules
- **Feature-First Structure**: Each feature is self-contained with its own Clean Architecture layers
- **MVVM Pattern**: Each screen has its own View, ViewModel (Cubit), and State classes
- **Dependency Flow**: Features depend only on Core layer (features → core)
- **Core Infrastructure**: Centralized common functionality, utilities, and shared services
- **Database Layer**: Drift ORM with SQLite for local data persistence
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Testability**: Each layer can be tested independently with comprehensive mocking
- **Maintainability**: Easy to modify and extend without affecting other parts
- **Scalability**: New features can be added without restructuring existing code
- **Internationalization**: Built-in support for multiple languages with ARB files

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Flutter SDK**: `>=3.6.0` (tested with Flutter 3.27.1)
- **Dart SDK**: `>=3.6.0` (included with Flutter)
- **IDE**: Android Studio, VS Code, or IntelliJ IDEA with Flutter plugins
- **Git**: For version control
- **Platform-specific requirements**:
  - **Android**: Android Studio with Android SDK (API level 29+ / Android 10+)
  - **iOS**: Xcode 14.0+ (macOS only)

## 🛠️ IDE Configuration

### Android Studio Setup

1. **Install Android Studio** (latest stable version)
2. **Install Flutter Plugin**:
   - Go to `File > Settings > Plugins` or `Android Studio > Preferences > Plugins`
   - Search for "Flutter" and install the plugin
   - Restart Android Studio when prompted

3. **Configure Android SDK**:
   - Go to `File > Settings > Appearance & Behavior > System Settings > Android SDK`
   - Ensure Android SDK is installed (API level 29 or higher / Android 10+)
   - Install Android SDK Command-line Tools
   - Add SDK path to your system PATH

4. **Device Configuration**:
   - **Physical Device**: Enable Developer Options and USB Debugging (Android 10+ required)
   - **Emulator**: Create an AVD (Android Virtual Device) with API level 29+ (Android 10+)

5. **Configure Run/Debug in Android Studio**:

   To run the application with different environments, you need to configure Run/Debug configurations:

   **Step 1: Access configuration menu**
   ![Access Edit Configurations](docs/images/android-studio-edit-configurations.png)
   
   *Figure 1: Click on the dropdown next to the Run button and select "Edit Configurations..." to open the configuration window*

   **Step 2: Configure Development environment**
   ![Configure Development Environment](docs/images/android-studio-development-config.png)
   *Figure 2: Detailed configuration for Development environment with the following parameters:*
   - *Additional run args: `--flavor development --dart-define=ENVIRONMENT=development`*
   - *Build flavor: `development`*
   - *Ensure all fields are filled correctly as shown in the image*

   **Similarly, you can create configurations for Staging and Production**:
   - **Staging**: `--flavor staging --dart-define=ENVIRONMENT=staging`
   - **Production**: `--flavor production --dart-define=ENVIRONMENT=production`

### Running the application with different environments

After configuration is complete, you can easily switch between environments:

1. **Select configuration**: Use the dropdown next to the Run button to choose the environment (Development, Staging, Production)
2. **Run the application**: Press the Run or Debug button to launch the application with the selected environment
3. **Verify environment**: The application will automatically load the corresponding `.env` file and display the appropriate app name:
   - Development: "Glidic (Dev)"
   - Staging: "Glidic (Staging)"
   - Production: "Glidic"

6. **Verify Setup**:
   ```bash
   flutter doctor
   ```

### Visual Studio Code Setup

1. **Install VS Code Extensions**:
   - **Flutter**: Official Flutter extension
   - **Dart**: Official Dart language extension
   - **Flutter Widget Snippets**: Helpful code snippets
   - **Bracket Pair Colorizer**: Better bracket visualization

2. **Configure Launch Configurations**:
   The project includes pre-configured launch settings in `.vscode/launch.json`:
   - **Development**: Debug mode with development flavor
   - **Staging**: Profile mode with staging flavor
   - **Production**: Release mode with production flavor

3. **Recommended VS Code Settings**:
   ```json
   {
     "dart.flutterSdkPath": "/path/to/flutter",
     "dart.debugExternalPackageLibraries": false,
     "dart.debugSdkLibraries": false,
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.fixAll": true
     }
   }
   ```

4. **Verify Setup**:
   - Open Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`)
   - Run `Flutter: Doctor` to check installation

## 🚀 Project Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd glidic-app
```

### 2. Install Flutter Dependencies
```bash
flutter pub get
```

### 3. Environment Configuration Setup

#### Create Environment Files
The project uses `flutter_dotenv` for environment management. Create your environment files:

```bash
# Copy the example file to create your environment files
cp .env.example .env.development
cp .env.example .env.staging
cp .env.example .env.production

# Create default .env file (will be overwritten during builds)
cp .env.development .env
```

#### Configure Environment Variables
Edit each environment file with appropriate values:

**`.env.development`** (Development environment):
```bash
ENVIRONMENT=development
DEBUG_MODE=true
ENABLE_LOGGING=true
BASE_URL=https://dev-api.glidic.com
FIREBASE_API_KEY=your_dev_firebase_key
GOOGLE_MAPS_API_KEY=your_dev_maps_key
```

**`.env.staging`** (Staging environment):
```bash
ENVIRONMENT=staging
DEBUG_MODE=false
ENABLE_LOGGING=true
BASE_URL=https://staging-api.glidic.com
FIREBASE_API_KEY=your_staging_firebase_key
GOOGLE_MAPS_API_KEY=your_staging_maps_key
```

**`.env.production`** (Production environment):
```bash
ENVIRONMENT=production
DEBUG_MODE=false
ENABLE_LOGGING=false
BASE_URL=https://api.glidic.com
FIREBASE_API_KEY=your_prod_firebase_key
GOOGLE_MAPS_API_KEY=your_prod_maps_key
```

> ⚠️ **Security Note**: Never commit `.env*` files to version control. They are already included in `.gitignore`.

### 4. Platform-Specific Setup

#### Android Setup
1. **Minimum Android Version**: This app requires Android 10 (API level 29) or higher
   - **compileSdkVersion**: 35 (Android 15)
   - **targetSdkVersion**: 35 (Android 15)
   - **minSdkVersion**: 29 (Android 10)

2. **Build Flavors**: The project includes three Android flavors:
   - `development`: Debug builds with dev API endpoints
   - `staging`: Pre-production builds with staging API endpoints
   - `production`: Production builds with live API endpoints

3. **App Signing**: Configure signing keys in `android/app/build.gradle` for release builds

#### iOS Setup
1. **Xcode Configuration**: Open `ios/Runner.xcworkspace` in Xcode
2. **Schemes**: The project includes three iOS schemes:
   - `Development`: Debug builds with development configuration
   - `Staging`: Profile builds with staging configuration
   - `Production`: Release builds with production configuration

3. **Bundle Identifiers**:
   - Development: `jp.co.iotbank.glidici.dev`
   - Staging: `jp.co.iotbank.glidici.staging`
   - Production: `jp.co.iotbank.glidici`

4. **Display Names**:
   - Development: "Glidic (Dev)"
   - Staging: "Glidic (Staging)"
   - Production: "Glidic"

### 5. Code Generation
```bash
# Generate database code (Drift ORM)
dart run build_runner build

# Generate localization files
flutter gen-l10n

# Clean and regenerate all code (if needed)
dart run build_runner build --delete-conflicting-outputs
```

### 6. Verify Installation
```bash
flutter doctor
flutter doctor -v  # Verbose output for detailed information
```

## 🏗️ Project Architecture

### Core Modules

#### 🗄️ Database Layer
- **Drift ORM**: Type-safe SQLite database operations
- **Database**: `AppDatabase` with migration support
- **DAOs**: Data Access Objects for each table (e.g., `UsersDao`)
- **Tables**: Strongly-typed table definitions
- **Constants**: Database configuration and table names

#### 🔧 Core Infrastructure (`lib/core/`)

The Core layer provides foundational services and utilities used across all features:

**Common Components (`core/common/`)**
- **Configuration**: Environment configuration and app settings
- **Constants**: Organized constants by category (API, database, UI, storage, colors)
- **Errors**: Centralized error handling and failure types
- **Extensions**: Dart/Flutter extensions for enhanced functionality
- **Utils**: Utility functions and helper classes

**Data Infrastructure (`core/data/`)**
- **Database**: Drift ORM setup with SQLite database and DAOs
- **Network**: Dio HTTP client with interceptors (auth, error, logging)
- **Storage**: Secure storage and shared preferences services
- **Models**: Core data models and base classes
- **Repositories**: Base repository implementations

**Domain Foundation (`core/domain/`)**
- **Entities**: Core business entities used across features
- **Repositories**: Abstract repository interfaces
- **Use Cases**: Base use case classes and common business logic

**Presentation Infrastructure (`core/presentation/`)**
- **Router**: Auto-route configuration and navigation
- **Theme**: App theming and styling
- **Widgets**: Reusable UI components (buttons, inputs, layouts)
- **Controllers**: Base controllers and state management utilities

**Dependency Injection (`core/di/`)**
- **Service Locator**: GetIt configuration with modular registration
- **Initialization**: Centralized dependency setup and configuration

#### 🎯 Features (Clean Architecture)

Each feature follows Clean Architecture with complete isolation and dependency only on the Core layer:

**Authentication Feature (`features/auth/`)**
- **Domain**: User entities, auth repository interfaces, login/logout use cases
- **Data**: API data sources, local storage, repository implementations
- **Presentation**: Login screen with MVVM pattern (LoginScreen, LoginCubit, LoginState)
- **Controller**: Feature-specific controllers and state management
- **DI**: Feature-specific dependency injection configuration

**Home Feature (`features/home/<USER>
- **Domain**: Home-specific business logic and entities
- **Data**: Home data sources and repository implementations
- **Presentation**: Home dashboard with MVVM pattern
- **Controller**: Home-specific controllers
- **DI**: Home feature dependency injection

## 📁 Project Structure

```
glidic_app/
├── android/                        # Android platform-specific files
├── ios/                            # iOS platform-specific files
├── lib/                            # Main application source code
│   ├── core/                       # Core infrastructure (shared across all features)
│   │   ├── common/                 # Common utilities and configurations
│   │   │   ├── config/             # Environment and app configuration
│   │   │   ├── constants/          # Application constants (API, UI, database, etc.)
│   │   │   ├── errors/             # Error handling and failure types
│   │   │   ├── extensions/         # Dart/Flutter extensions
│   │   │   └── utils/              # Utility functions and helpers
│   │   ├── controller/             # Core controllers and state management
│   │   ├── data/                   # Core data layer infrastructure
│   │   │   ├── datasources/        # Database, network, and storage services
│   │   │   │   ├── database/       # Drift database configuration and DAOs
│   │   │   │   ├── network/        # Dio HTTP client and interceptors
│   │   │   │   └── storage/        # Secure storage and preferences
│   │   │   ├── models/             # Core data models and DTOs
│   │   │   └── repositories/       # Base repository implementations
│   │   ├── di/                     # Dependency injection configuration
│   │   │   └── service_locator.dart # GetIt service locator setup
│   │   ├── domain/                 # Core domain layer
│   │   │   ├── entities/           # Core business entities
│   │   │   ├── repositories/       # Abstract repository interfaces
│   │   │   └── usecases/           # Base use case classes
│   │   ├── generated/              # Generated code (localization, etc.)
│   │   │   └── l10n/               # Generated localization classes
│   │   ├── l10n/                   # Localization files (ARB format)
│   │   │   ├── app_en.arb          # English translations
│   │   │   └── app_ja.arb          # Japanese translations
│   │   └── presentation/           # Core presentation layer
│   │       ├── controller/         # Base controllers
│   │       ├── router/             # Navigation and routing configuration
│   │       ├── theme/              # App theming and styling
│   │       └── widgets/            # Reusable UI components
│   ├── features/                   # Feature modules (Clean Architecture)
│   │   ├── auth/                   # Authentication feature
│   │   │   ├── controller/         # Auth-specific controllers
│   │   │   ├── data/               # Data layer implementation
│   │   │   │   ├── datasources/    # Remote and local data sources
│   │   │   │   ├── models/         # Data models with JSON serialization
│   │   │   │   └── repositories/   # Repository implementations
│   │   │   ├── domain/             # Business logic and entities
│   │   │   │   ├── entities/       # Core business objects
│   │   │   │   ├── repositories/   # Repository interfaces
│   │   │   │   └── usecases/       # Application-specific business rules
│   │   │   ├── presentation/       # UI components (MVVM pattern)
│   │   │   │   ├── screens/        # Screen-specific MVVM implementations
│   │   │   │   │   └── login_screen/
│   │   │   │   │       ├── login_screen.dart      # View
│   │   │   │   │       ├── login_cubit.dart       # ViewModel
│   │   │   │   │       ├── login_state.dart       # State
│   │   │   │   │       └── widgets/               # Screen-specific widgets
│   │   │   │   └── widgets/        # Shared auth feature widgets
│   │   │   └── di/                 # Feature-specific dependency injection
│   │   └── home/                   # Home dashboard feature
│   │       ├── controller/         # Home-specific controllers
│   │       ├── data/               # Data layer implementation
│   │       ├── domain/             # Business logic and entities
│   │       ├── presentation/       # UI components (MVVM pattern)
│   │       └── di/                 # Dependency injection configuration
│   └── main.dart                   # Application entry point
├── docs/                           # Project documentation
│   ├── ARCHITECTURE.md             # Architecture documentation
│   ├── BUILD_SYSTEM.md             # Build system and environment setup
│   ├── DEVELOPMENT_GUIDE.md        # Development guidelines and best practices
│   ├── ENVIRONMENT_CONFIGURATION.md # Environment configuration guide
│   └── images/                     # Documentation images and screenshots
├── scripts/                        # Build and utility scripts
│   ├── build_dev.sh               # Development build script
│   ├── build_staging.sh           # Staging build script
│   ├── build_production.sh        # Production build script
│   └── setup_ios_schemes.sh       # iOS scheme setup script
├── test/                          # Test files
├── .env                           # Environment variables (template)
├── .env.development               # Development environment variables
├── .env.staging                   # Staging environment variables
├── .env.production                # Production environment variables
├── pubspec.yaml                   # Flutter dependencies and configuration
└── README.md                      # Project documentation
```

## ⚡ Build and Run Commands

### Environment-Specific Commands

The project supports three environments with different configurations:

#### Development Environment
```bash
# Using build scripts (recommended)
./scripts/build_dev.sh

# Manual commands
flutter run --flavor development --dart-define=ENVIRONMENT=development

# Platform-specific development builds
flutter run --flavor development --dart-define=ENVIRONMENT=development -d android
flutter run --flavor development --dart-define=ENVIRONMENT=development -d ios

# Build APK/IPA for development
flutter build apk --debug --flavor development --dart-define=ENVIRONMENT=development
flutter build ios --debug --flavor development --dart-define=ENVIRONMENT=development
```

#### Staging Environment
```bash
# Using build scripts (recommended)
./scripts/build_staging.sh

# Manual commands
flutter run --flavor staging --dart-define=ENVIRONMENT=staging

# Platform-specific staging builds
flutter run --flavor staging --dart-define=ENVIRONMENT=staging -d android
flutter run --flavor staging --dart-define=ENVIRONMENT=staging -d ios

# Build APK/IPA for staging
flutter build apk --release --flavor staging --dart-define=ENVIRONMENT=staging
flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging
```

#### Production Environment
```bash
# Using build scripts (recommended)
./scripts/build_production.sh

# Manual commands
flutter run --flavor production --dart-define=ENVIRONMENT=production --release

# Platform-specific production builds
flutter run --flavor production --dart-define=ENVIRONMENT=production --release -d android
flutter run --flavor production --dart-define=ENVIRONMENT=production --release -d ios

# Build APK/IPA for production (with obfuscation)
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info
flutter build ios --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info
```

### Quick Development Commands

#### Running in Different Modes
```bash
# Development mode (debug) - default environment
flutter run

# Development with specific device
flutter run -d android         # Android device/emulator
flutter run -d ios             # iOS device/simulator

# Hot reload and hot restart
# Press 'r' for hot reload
# Press 'R' for hot restart
# Press 'q' to quit
```

#### Build Variants
```bash
# Debug builds (faster, includes debug info)
flutter build apk --debug --flavor development
flutter build ios --debug --flavor development

# Profile builds (optimized, with some debug info)
flutter build apk --profile --flavor staging
flutter build ios --profile --flavor staging

# Release builds (fully optimized)
flutter build apk --release --flavor production
flutter build ios --release --flavor production
```

### Environment Management

#### Switch Between Environments
```bash
# Use the environment switching script
./scripts/switch_env.sh development
./scripts/switch_env.sh staging
./scripts/switch_env.sh production

# Or manually copy environment files
cp .env.development .env    # Switch to development
cp .env.staging .env        # Switch to staging
cp .env.production .env     # Switch to production
```

#### Validate Environment Configuration
```bash
# Validate current environment setup
./scripts/validate_env.sh

# Test environment detection
./scripts/test_runtime_environment_detection.sh
```

### Database Operations
```bash
# Generate database code after schema changes
dart run build_runner build

# Watch mode for continuous generation during development
dart run build_runner watch

# Clean generated files and regenerate
dart run build_runner build --delete-conflicting-outputs
```

### Testing Commands
```bash
# Run all tests
flutter test

# Run tests with coverage report
flutter test --coverage

# Run specific feature tests
flutter test test/features/auth/
flutter test test/features/home/

# Run tests for specific layer
flutter test test/core/
flutter test test/features/auth/domain/
flutter test test/features/auth/data/
flutter test test/features/auth/presentation/
```

### Code Generation
```bash
# Generate localization files
flutter gen-l10n

# Generate all code (database, models, etc.)
dart run build_runner build

# Generate only router code
dart run build_runner build --build-filter="lib/router/**.gr.dart"

# Generate JSON serialization code
dart run build_runner build --build-filter="lib/**.g.dart"
```

### Maintenance Commands
```bash
# Clean build artifacts
flutter clean

# Get latest dependencies
flutter pub get

# Upgrade dependencies (check for updates)
flutter pub outdated
flutter pub upgrade

# Analyze code quality
flutter analyze

# Format code
dart format lib/ test/

# Check for security vulnerabilities
flutter pub deps
```

## 🌍 Environment Configuration

### Overview
The project uses a sophisticated environment management system with `flutter_dotenv` for secure configuration management across development, staging, and production environments.

### Environment Detection
The app automatically detects the environment using the following priority:
1. **Build-time detection**: `--dart-define=ENVIRONMENT=<env>` parameter
2. **Environment file**: `ENVIRONMENT` variable in loaded `.env` file
3. **Fallback**: Development in debug mode, Production in release mode

### Environment Files Structure
```
├── .env.example          # Template file with all available variables
├── .env.development      # Development environment settings
├── .env.staging          # Staging environment settings
├── .env.production       # Production environment settings
└── .env                  # Active environment file (auto-generated)
```

### Configuration Categories

#### API Configuration
```bash
BASE_URL=https://api.example.com           # API base URL
API_VERSION=v1                             # API version
CONNECT_TIMEOUT_SECONDS=30                 # Connection timeout
RECEIVE_TIMEOUT_SECONDS=30                 # Receive timeout
SEND_TIMEOUT_SECONDS=30                    # Send timeout
```

#### Third-Party Services
```bash
FIREBASE_API_KEY=your_firebase_key         # Firebase configuration
GOOGLE_MAPS_API_KEY=your_maps_key          # Google Maps integration
ANALYTICS_TRACKING_ID=your_analytics_id    # Analytics tracking
```

#### Feature Flags
```bash
ENABLE_FEATURE_X=false                     # Feature toggles
ENABLE_ANALYTICS=true                      # Analytics enablement
ENABLE_MOCK_DATA=false                     # Mock data for testing
```

#### Debug Settings
```bash
DEBUG_MODE=true                            # Debug mode toggle
ENABLE_LOGGING=true                        # Logging enablement
ENABLE_DEBUG_OVERLAY=false                 # Debug overlay
ENABLE_PERFORMANCE_MONITORING=false        # Performance monitoring
```

### Environment-Specific Settings

#### Development Environment
- **Purpose**: Local development and testing
- **API**: Development server endpoints
- **Logging**: Enabled with verbose output
- **Debug**: Full debug information available
- **App Name**: "Glidic (Dev)"
- **Bundle ID**: `jp.co.iotbank.glidici.dev`

#### Staging Environment
- **Purpose**: Pre-production testing and QA
- **API**: Staging server endpoints
- **Logging**: Enabled with filtered output
- **Debug**: Limited debug information
- **App Name**: "Glidic (Staging)"
- **Bundle ID**: `jp.co.iotbank.glidici.staging`

#### Production Environment
- **Purpose**: Live production deployment
- **API**: Production server endpoints
- **Logging**: Disabled or minimal
- **Debug**: No debug information
- **App Name**: "Glidic"
- **Bundle ID**: `jp.co.iotbank.glidici`

### Usage in Code

#### Initialize Environment
```dart
import 'package:glidic_app/core/config/env_config.dart';

// Initialize in main.dart (done automatically in service locator)
await EnvConfig.init();

// Or initialize with specific environment
await EnvConfig.init(environment: Environment.staging);
```

#### Access Configuration Values
```dart
// API configuration
String apiUrl = EnvConfig.baseUrl;
String apiVersion = EnvConfig.apiVersion;
int timeout = EnvConfig.connectTimeoutSeconds;

// Environment detection
Environment currentEnv = EnvConfig.currentEnvironment;
String envName = EnvConfig.environment;
bool isDebug = EnvConfig.isDebugMode;

// Feature flags
bool analyticsEnabled = EnvConfig.enableAnalytics;
bool loggingEnabled = EnvConfig.enableLogging;

// Third-party keys
String firebaseKey = EnvConfig.firebaseApiKey;
String mapsKey = EnvConfig.googleMapsApiKey;
```

#### Environment-Specific Logic
```dart
// Conditional behavior based on environment
if (EnvConfig.currentEnvironment == Environment.development) {
  // Development-specific code
  print('Running in development mode');
} else if (EnvConfig.currentEnvironment == Environment.production) {
  // Production-specific code
  enableCrashReporting();
}

// Feature flag usage
if (EnvConfig.enableAnalytics) {
  analytics.track('user_action');
}
```

### Build System Integration

#### Android Build Flavors
The Android build system includes three flavors that automatically set environment variables:

```gradle
productFlavors {
    development {
        applicationId "jp.co.iotbank.glidici.dev"
        buildConfigField "String", "ENVIRONMENT", '"development"'
        resValue "string", "app_name", "Glidic Dev"
    }
    staging {
        applicationId "jp.co.iotbank.glidici.staging"
        buildConfigField "String", "ENVIRONMENT", '"staging"'
        resValue "string", "app_name", "Glidic Staging"
    }
    production {
        applicationId "jp.co.iotbank.glidici"
        buildConfigField "String", "ENVIRONMENT", '"production"'
        resValue "string", "app_name", "Glidic"
    }
}
```

#### iOS Schemes and Configurations
iOS uses `.xcconfig` files for environment-specific settings:

- **Development.xcconfig**: `DART_DEFINES = ENVIRONMENT=development`
- **Staging.xcconfig**: `DART_DEFINES = ENVIRONMENT=staging`
- **Production.xcconfig**: `DART_DEFINES = ENVIRONMENT=production`

### Security Best Practices

#### Environment File Management
```bash
# ✅ DO: Use environment-specific files
.env.development    # Development settings
.env.staging        # Staging settings
.env.production     # Production settings

# ❌ DON'T: Commit sensitive files to version control
.env               # Auto-generated, excluded from git
.env.*             # All environment files excluded from git
```

#### Sensitive Data Handling
- **API Keys**: Store in environment files, never hardcode
- **Database URLs**: Environment-specific endpoints
- **Third-party Tokens**: Separate keys for each environment
- **Debug Settings**: Disabled in production builds

#### Validation and Testing
```bash
# Validate environment configuration
./scripts/validate_env.sh

# Test environment switching
./scripts/switch_env.sh development
flutter run --dart-define=ENVIRONMENT=development

# Verify environment detection
./scripts/test_runtime_environment_detection.sh
```

### Troubleshooting

#### Common Issues
1. **Environment not detected**: Ensure `--dart-define=ENVIRONMENT=<env>` is passed
2. **Missing .env file**: Run `cp .env.example .env.development`
3. **Wrong API endpoints**: Verify `BASE_URL` in environment file
4. **Build flavor mismatch**: Check Android flavor and iOS scheme match

#### Debug Environment Loading
```dart
// Add to main.dart for debugging
print('Current Environment: ${EnvConfig.currentEnvironment}');
print('Base URL: ${EnvConfig.baseUrl}');
print('Debug Mode: ${EnvConfig.isDebugMode}');
```

For detailed environment setup instructions, see [docs/ENVIRONMENT_CONFIGURATION.md](docs/ENVIRONMENT_CONFIGURATION.md).

## 🚀 Key Features & Architecture

### ✅ Clean Architecture Implementation
- **Domain Layer**: Business logic, entities, and repository interfaces
- **Data Layer**: Repository implementations, data sources, and models with JSON serialization
- **Presentation Layer**: MVVM pattern with View, ViewModel (Cubit), and State classes

### ✅ Database Layer (Drift ORM)
- **Type-safe SQLite operations** with compile-time query validation
- **Migration support** for database schema changes
- **DAOs (Data Access Objects)** for organized database operations
- **Reactive queries** with Stream support for real-time updates
- **Transaction support** for complex database operations

### ✅ MVVM Pattern in Presentation Layer
- **View**: Screen widgets that display UI and handle user interactions
- **ViewModel**: Cubit classes that manage screen state and business logic
- **State**: Immutable state classes using Equatable for efficient rebuilds
- **Screen-specific widgets**: Organized in dedicated widgets folders per screen

### ✅ State Management
- **flutter_bloc** with Cubit pattern for predictable state management
- Automatic loading, success, and error state handling
- Immutable state classes with Equatable for performance optimization
- BlocBuilder and BlocListener for reactive UI updates

### ✅ Dependency Injection
- **GetIt** service locator pattern for dependency management
- Modular dependency registration per feature
- Easy testing with mock implementations
- Singleton and factory registrations as needed

### ✅ Navigation
- **auto_route** for declarative, code-generated routing
- Type-safe navigation with generated route classes
- Centralized route configuration with auto-generated router
- Deep linking support for mobile platforms
- Navigation guards and route validation
- Nested routing and route parameters

### ✅ Error Handling
- **Exception-based** error handling with custom AppFailure hierarchy
- Specific failure types for different error scenarios (NetworkFailure, AuthFailure, etc.)
- Consistent error propagation using try-catch-throw pattern
- User-friendly error messages with localization support

### ✅ Clean Architecture Dependency Rules
- **Strict Dependency Flow**: Features → Core (features depend only on core layer)
- **Core Layer Independence**: Core has zero dependencies on features
- **Feature Isolation**: Features are completely independent of each other
- **Shared Infrastructure**: Core provides all shared services, utilities, and components
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Interface Segregation**: Features depend on abstractions, not concrete implementations

## 📦 Key Dependencies

### Core Dependencies
```yaml
# State Management & Architecture
flutter_bloc: ^9.1.1              # BLoC/Cubit state management
equatable: ^2.0.7                 # Value equality for state classes

# Navigation
auto_route: ^9.2.2                # Code-generated routing

# Dependency Injection
get_it: ^8.0.3                    # Service locator pattern

# Database
drift: ^2.26.1                    # Type-safe SQLite ORM
sqlite3: ^2.4.6                   # SQLite database engine
sqlite3_flutter_libs: ^0.5.34     # SQLite libraries for Flutter

# HTTP & Networking
dio: ^5.8.0+1                     # HTTP client
retrofit: ^4.5.0                  # Type-safe API client
connectivity_plus: ^6.1.4         # Network connectivity checking

# Storage
flutter_secure_storage: ^9.2.4    # Secure storage for sensitive data
shared_preferences: ^2.5.3        # Simple key-value storage

# Error Handling
# Exception-based error handling with custom AppFailure hierarchy

# Internationalization
flutter_localizations: sdk        # Flutter localization support
intl: ^0.19.0                     # Internationalization utilities
```

### Development Dependencies
```yaml
# Code Generation
build_runner: ^2.4.15             # Code generation runner
drift_dev: ^2.26.0                # Drift code generation
json_serializable: ^6.8.0         # JSON serialization
retrofit_generator: ^9.3.0        # Retrofit code generation
auto_route_generator: ^9.0.0      # Auto route code generation

# Testing
flutter_test: sdk                 # Flutter testing framework
mocktail: ^1.0.4                  # Modern mocking library
bloc_test: ^10.0.0                # BLoC testing utilities
```

## 🧪 Testing Strategy

### Test Structure
```
test/
├── core/                          # Core functionality tests
│   ├── database/                  # Database layer tests
│   ├── network/                   # Network layer tests
│   └── di/                        # Dependency injection tests
├── features/                      # Feature-specific tests
│   ├── auth/
│   │   ├── data/                  # Data layer tests
│   │   ├── domain/                # Domain layer tests
│   │   └── presentation/          # Presentation layer tests
│   └── home/
│       ├── data/                  # Data layer tests
│       ├── domain/                # Domain layer tests
│       └── presentation/          # Presentation layer tests
└── helpers/                       # Test helpers and utilities

```

### Testing Best Practices
- **Unit Tests**: Test individual functions, methods, and classes in isolation
- **Widget Tests**: Test UI components and their interactions
- **Integration Tests**: Test complete user flows and feature interactions
- **Database Tests**: Test database operations with in-memory databases
- **Mocking**: Use mocktail for mocking dependencies
- **BLoC Testing**: Use bloc_test for testing Cubit state changes

## 🌍 Localization

The app supports internationalization with ARB files:

- **English** (`lib/l10n/app_en.arb`): Default language
- **Japanese** (`lib/l10n/app_ja.arb`): Additional language support

### Adding New Languages
1. Create new ARB file: `lib/l10n/app_[locale].arb`
2. Add translations following the existing structure
3. Run `flutter gen-l10n` to generate localization classes
4. Access translations in code: `context.l10n.yourTranslationKey`

## 🔧 Development Guidelines

### Adding New Features
1. **Create feature structure** following the established pattern
2. **Implement Clean Architecture layers** (Domain → Data → Presentation)
3. **Follow MVVM pattern** in presentation layer
4. **Check shared components** before creating new ones - reuse existing entities, models, widgets, and data sources
5. **Add to shared module** if the component will be used by multiple features
6. **Add comprehensive tests** for all layers
7. **Update dependency injection** configuration
8. **Add navigation routes** in app_router.dart and regenerate router code if needed

### Navigation with AutoRoute
1. **Define routes** in `lib/router/app_router.dart` using `@AutoRouteConfig()`
2. **Add new pages** with `@RoutePage()` annotation
3. **Generate router code**: `dart run build_runner build`
4. **Navigate programmatically**: 
   ```dart
   context.router.push(LoginRoute());
   context.router.pushAndClearStack(HomeRoute());
   ```
5. **Route parameters**: Use typed route parameters for type-safe navigation
6. **Route guards**: Implement custom route guards for authentication checks
7. **AutoRouteWrapper**: Use wrapper routes to provide BLoC/Cubit to child routes
   ```dart   
   // AuthWrapperRoute implementation
   @RoutePage()
   class AuthWrapper extends StatelessWidget implements AutoRouteWrapper {
     const AuthWrapper({super.key});
   
     @override
     Widget wrappedRoute(BuildContext context) {
      // Provide a Bloc/Cubit for a screen
       return BlocProvider(
         create: (context) => getIt<AuthCubit>(),
         child: this,
       );
     }
   }
   ```
8. **Nested Route**: Implement nested route

### Using Core Components
1. **Before creating new components**, check if similar functionality exists in `core/`
2. **Place common entities** in `core/domain/entities/` if used by multiple features
3. **Share data models** in `core/data/models/` for common API responses
4. **Reuse UI widgets** from `core/presentation/widgets/` for consistency
5. **Common data sources** should be placed in `core/data/datasources/`
6. **Utility functions** should be placed in `core/common/utils/`
7. **Constants** should be organized in `core/common/constants/`
8. **Follow the dependency rule**: Features can depend on Core, but Core cannot depend on Features

### Database Schema Changes
1. **Update table definitions** in `lib/core/database/tables/`
2. **Increment schema version** in `DatabaseConstants`
3. **Add migration logic** in `AppDatabase.migration`
4. **Run code generation**: `dart run build_runner build`
5. **Test migrations** thoroughly

### Code Quality Standards
- Follow **Clean Architecture** principles
- Implement **SOLID** principles
- Use **meaningful naming** conventions
- Add **comprehensive documentation**
- Maintain **>80% test coverage** for business logic
- Follow **Dart/Flutter** style guidelines

## 📚 Additional Documentation

For more detailed information, refer to the comprehensive documentation in the `docs/` directory:

- **[Architecture Guide](docs/ARCHITECTURE.md)**: Detailed Clean Architecture implementation
- **[Build System](docs/BUILD_SYSTEM.md)**: Complete build system documentation
- **[Development Guide](docs/DEVELOPMENT_GUIDE.md)**: Development workflows and best practices
- **[Environment Configuration](docs/ENVIRONMENT_CONFIGURATION.md)**: Comprehensive environment setup
- **[iOS Xcode Setup](docs/IOS_XCODE_SETUP.md)**: iOS-specific configuration guide

## 🚨 Troubleshooting

### Common Setup Issues

#### Flutter Doctor Issues
```bash
# Check Flutter installation (ensure Android and iOS toolchains are properly configured)
flutter doctor -v

# Fix common issues
flutter doctor --android-licenses    # Accept Android licenses
flutter config --android-sdk /path/to/android/sdk    # Set Android SDK path
```

#### Environment Configuration Issues
```bash
# Validate environment setup
./scripts/validate_env.sh

# Test environment switching
./scripts/switch_env.sh development
flutter run --dart-define=ENVIRONMENT=development
```

#### Build Issues
```bash
# Clean and rebuild
flutter clean
flutter pub get
dart run build_runner build --delete-conflicting-outputs

# iOS-specific issues
cd ios && pod install && cd ..
./scripts/fix_ios_schemes.sh
```

#### IDE Issues
- **Android Studio**: Restart IDE after installing Flutter plugin
- **VS Code**: Reload window after installing Flutter extension
- **Xcode**: Clean build folder and rebuild project

### Getting Help

1. **Check Documentation**: Review the comprehensive docs in `/docs` directory
2. **Run Diagnostics**: Use `flutter doctor -v` for detailed system information
3. **Validate Setup**: Run validation scripts in `/scripts` directory
4. **Environment Issues**: Check environment configuration with `./scripts/validate_env.sh`

## 🎯 Quick Start Checklist

- [ ] Install Flutter SDK (>=3.6.0) with Android and iOS development support
- [ ] Install IDE with Flutter plugins (Android Studio/VS Code)
- [ ] Set up Android SDK (API level 29+ / Android 10+) and/or Xcode 14.0+ for iOS development
- [ ] Clone repository and run `flutter pub get`
- [ ] Set up environment files (`.env.development`, `.env.staging`, `.env.production`)
- [ ] Run code generation: `dart run build_runner build` (generates database, router, and JSON serialization code)
- [ ] Generate localizations: `flutter gen-l10n`
- [ ] Verify setup: `flutter doctor`
- [ ] Run development build: `flutter run --flavor development --dart-define=ENVIRONMENT=development`
- [ ] Test environment switching: `./scripts/switch_env.sh staging`
- [ ] Run tests: `flutter test`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Flutter Team**: For the amazing framework and comprehensive documentation
- **Clean Architecture Community**: For architectural guidance and best practices
- **Drift Team**: For the excellent type-safe database ORM
- **BLoC Library Team**: For the robust state management solution
- **Open Source Contributors**: For the excellent packages that make this project possible
