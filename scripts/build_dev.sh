#!/bin/bash

# Build script for Development Environment
# This script builds the Flutter app for development environment
# Usage: ./build_dev.sh [debug|release]
# Default: debug

set -e  # Exit on any error

# Parse build type argument
BUILD_TYPE=${1:-debug}

if [[ "$BUILD_TYPE" != "debug" && "$BUILD_TYPE" != "release" ]]; then
    echo "❌ Error: Invalid build type '$BUILD_TYPE'. Use 'debug' or 'release'"
    exit 1
fi

echo "🚀 Building Glidic App for Development Environment ($BUILD_TYPE)..."

# Check if .env.development exists
if [ ! -f ".env.development" ]; then
    echo "❌ Error: .env.development file not found!"
    echo "Please create .env.development file based on .env.example"
    exit 1
fi

# Copy development environment file to .env for build
echo "📋 Using development environment configuration..."
cp .env.development .env

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build for different platforms
echo "🔨 Building for development ($BUILD_TYPE)..."

if [[ "$BUILD_TYPE" == "debug" ]]; then
    # Build for Android (Debug)
    echo "📱 Building Android APK (Debug)..."
    flutter build apk --debug --flavor development --dart-define=ENVIRONMENT=development

    # Build for iOS (Debug) - only on macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "🍎 Building iOS app (Debug)..."
        flutter build ios --debug --flavor development --dart-define=ENVIRONMENT=development --no-codesign
    else
        echo "⚠️  Skipping iOS build (not on macOS)"
    fi

    echo "✅ Development debug build completed successfully!"
    echo "📍 Environment: Development"
    echo "🔧 Debug Mode: Enabled"
    echo "📝 Logging: Enabled"
    echo "🔑 Signing: Debug keystore"
else
    # Check if keystore.properties exists for release builds
    if [ ! -f "android/keystore.properties" ]; then
        echo "❌ Error: android/keystore.properties file not found!"
        echo "Release builds require keystore.properties for secure signing."
        echo "Please create keystore.properties based on android/keystore.properties.example"
        exit 1
    fi

    # Build for Android (Release with production keystore)
    echo "📱 Building Android APK (Release with production keystore)..."
    flutter build apk --release --flavor development --dart-define=ENVIRONMENT=development

    echo "📱 Building Android App Bundle (Release with production keystore)..."
    flutter build appbundle --release --flavor development --dart-define=ENVIRONMENT=development

    # Build for iOS (Release) - only on macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "🍎 Building iOS app (Release)..."
        flutter build ios --release --flavor development --dart-define=ENVIRONMENT=development --no-codesign
    else
        echo "⚠️  Skipping iOS build (not on macOS)"
    fi

    echo "✅ Development release build completed successfully!"
    echo "📍 Environment: Development"
    echo "🔧 Debug Mode: Enabled (in app logic)"
    echo "📝 Logging: Enabled"
    echo "🔑 Signing: Production keystore"
    echo "✅ Note: Uses secure keystore - suitable for app store distribution"
fi
