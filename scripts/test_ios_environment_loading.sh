#!/bin/bash

# Test iOS Environment Loading Script
# This script tests that iOS builds correctly load environment-specific configuration files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "🧪 Testing iOS Environment Configuration Loading"
echo ""

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_color $RED "❌ Error: Not in Flutter project root directory"
    exit 1
fi

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    print_color $RED "❌ Error: iOS directory not found"
    exit 1
fi

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_color $YELLOW "⚠️  Skipping iOS tests (not on macOS)"
    exit 0
fi

print_color $YELLOW "📋 Checking Environment Files..."
echo ""

# Check environment files
env_files=(".env.development" ".env.staging" ".env.production")
all_files_exist=true

for file in "${env_files[@]}"; do
    if [ -f "$file" ]; then
        print_color $GREEN "✅ $file exists"
        
        # Check key environment variables
        if grep -q "ENVIRONMENT=" "$file"; then
            env_value=$(grep "ENVIRONMENT=" "$file" | cut -d'=' -f2)
            print_color $BLUE "   📍 ENVIRONMENT=$env_value"
        fi
        
        if grep -q "BASE_URL=" "$file"; then
            base_url=$(grep "BASE_URL=" "$file" | cut -d'=' -f2)
            print_color $BLUE "   🌐 BASE_URL=$base_url"
        fi
    else
        print_color $RED "❌ $file missing"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = false ]; then
    print_color $RED "❌ Some environment files are missing. Cannot proceed with tests."
    exit 1
fi

echo ""

print_color $YELLOW "📋 Checking iOS Configuration Files..."
echo ""

# Check .xcconfig files
xcconfig_files=("ios/Flutter/Development.xcconfig" "ios/Flutter/Staging.xcconfig" "ios/Flutter/Production.xcconfig")
all_xcconfig_exist=true

for file in "${xcconfig_files[@]}"; do
    if [ -f "$file" ]; then
        print_color $GREEN "✅ $file exists"
        
        # Check DART_DEFINES
        if grep -q "DART_DEFINES" "$file"; then
            dart_defines=$(grep "DART_DEFINES" "$file" | cut -d'=' -f2-)
            print_color $BLUE "   🎯 DART_DEFINES=$dart_defines"
        fi
    else
        print_color $RED "❌ $file missing"
        all_xcconfig_exist=false
    fi
done

if [ "$all_xcconfig_exist" = false ]; then
    print_color $RED "❌ Some .xcconfig files are missing. Cannot proceed with tests."
    exit 1
fi

echo ""

print_color $YELLOW "📋 Testing iOS Builds..."
echo ""

# Test Development Build
print_color $BLUE "🧪 Testing Development Build (Development scheme)..."
if flutter build ios --debug --flavor Development --dart-define=ENVIRONMENT=development --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Development build successful"

    # Check if the correct environment file would be loaded
    print_color $BLUE "   📄 Should load: .env.development"
    print_color $BLUE "   🎯 DART_DEFINES includes: ENVIRONMENT=development"
else
    print_color $RED "❌ Development build failed"
fi

echo ""

# Test Staging Build
print_color $BLUE "🧪 Testing Staging Build (Staging scheme)..."
if flutter build ios --release --flavor Staging --dart-define=ENVIRONMENT=staging --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Staging build successful"

    # Check if the correct environment file would be loaded
    print_color $BLUE "   📄 Should load: .env.staging"
    print_color $BLUE "   🎯 DART_DEFINES includes: ENVIRONMENT=staging"
else
    print_color $RED "❌ Staging build failed"
fi

echo ""

# Test Production Build (using Release configuration directly)
print_color $BLUE "🧪 Testing Production Build (Production scheme)..."
if flutter build ios --release --flavor Production --dart-define=ENVIRONMENT=production --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Production build successful"

    # Check if the correct environment file would be loaded
    print_color $BLUE "   📄 Should load: .env.production"
    print_color $BLUE "   🎯 DART_DEFINES includes: ENVIRONMENT=production"
else
    print_color $RED "❌ Production build failed"
fi

echo ""

print_color $YELLOW "📋 Verifying Environment Detection Logic..."
echo ""

# Check EnvConfig implementation
if [ -f "lib/core/config/env_config.dart" ]; then
    print_color $GREEN "✅ EnvConfig class exists"
    
    # Check if it uses String.fromEnvironment
    if grep -q "String.fromEnvironment('ENVIRONMENT')" "lib/core/config/env_config.dart"; then
        print_color $GREEN "✅ Uses String.fromEnvironment('ENVIRONMENT') for detection"
    else
        print_color $YELLOW "⚠️  May not be using String.fromEnvironment for environment detection"
    fi
    
    # Check if it loads environment-specific files
    if grep -q "fileName.*environment.*fileName" "lib/core/config/env_config.dart"; then
        print_color $GREEN "✅ Loads environment-specific .env files"
    else
        print_color $YELLOW "⚠️  May not be loading environment-specific .env files"
    fi
else
    print_color $RED "❌ EnvConfig class not found"
fi

echo ""

print_color $YELLOW "📋 Testing Build Scripts..."
echo ""

# Test build scripts
build_scripts=("scripts/build_dev.sh" "scripts/build_staging.sh" "scripts/build_production.sh")

for script in "${build_scripts[@]}"; do
    if [ -f "$script" ] && [ -x "$script" ]; then
        script_name=$(basename "$script")
        print_color $GREEN "✅ $script_name is executable"
        
        # Check if it uses correct iOS flavor
        if grep -q "flutter build ios.*--flavor" "$script"; then
            flavor_line=$(grep "flutter build ios.*--flavor" "$script")
            print_color $BLUE "   🎯 $flavor_line"
        fi
    else
        print_color $RED "❌ $script is missing or not executable"
    fi
done

echo ""

print_color $BLUE "📊 Test Summary"
echo ""

print_color $GREEN "✅ Environment Configuration Status:"
echo "   📄 Environment files: .env.development, .env.staging, .env.production"
echo "   ⚙️  iOS configurations: Development.xcconfig, Staging.xcconfig, Production.xcconfig"
echo "   🎯 DART_DEFINES: Properly configured in .xcconfig files"
echo "   🔧 Build commands: Use correct --flavor parameters"
echo ""

print_color $BLUE "📱 iOS Flavor Mapping:"
echo "   Development → --flavor Development → Development.xcconfig → .env.development"
echo "   Staging → --flavor Staging → Staging.xcconfig → .env.staging"
echo "   Production → --flavor Production → Production.xcconfig → .env.production"
echo ""

print_color $GREEN "🎉 iOS Environment Configuration Test Complete!"
echo ""

print_color $YELLOW "💡 How it works:"
echo "1. Flutter build uses --flavor parameter with iOS scheme name"
echo "2. Xcode scheme links to appropriate .xcconfig file"
echo "3. .xcconfig file sets DART_DEFINES with ENVIRONMENT variable"
echo "4. EnvConfig.init() detects environment from String.fromEnvironment('ENVIRONMENT')"
echo "5. EnvConfig loads corresponding .env file (.env.development, .env.staging, .env.production)"
echo ""

print_color $BLUE "🔧 To test manually:"
echo "1. Run: ./scripts/build_dev.sh"
echo "2. Run: ./scripts/build_staging.sh"
echo "3. Run: ./scripts/build_production.sh"
echo "4. Check app behavior in each environment"
