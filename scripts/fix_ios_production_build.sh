#!/bin/bash

# Fix iOS Production Build Script
# This script provides solutions for the iOS production build configuration issue

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "🔧 Fixing iOS Production Build Configuration"
echo ""

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_color $RED "❌ Error: Not in Flutter project root directory"
    exit 1
fi

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    print_color $RED "❌ Error: iOS directory not found"
    exit 1
fi

print_color $YELLOW "📋 Current Issue:"
echo "Flutter expects a build configuration named 'Release-Runner' for production builds"
echo "but the current Xcode project doesn't have this configuration."
echo ""

print_color $BLUE "🔍 Available Build Configurations:"
cd ios
if command -v xcodebuild &> /dev/null; then
    xcodebuild -showBuildSettings -workspace Runner.xcworkspace -scheme Runner | grep "BUILD_CONFIGURATION" | head -5 || true
else
    print_color $YELLOW "⚠️  xcodebuild not available"
fi
cd ..

echo ""

print_color $YELLOW "💡 Solutions Available:"
echo ""
echo "1. Create Release-Runner configuration in Xcode (Recommended)"
echo "2. Use alternative production build command"
echo "3. Update production build script to use Release configuration"
echo ""

read -p "Which solution would you prefer? (1/2/3): " choice

case $choice in
    1)
        print_color $BLUE "📖 Manual Xcode Setup Required"
        echo ""
        echo "Please follow these steps in Xcode:"
        echo ""
        echo "1. Open Xcode workspace:"
        echo "   open ios/Runner.xcworkspace"
        echo ""
        echo "2. Select the Runner PROJECT (blue icon, not the target)"
        echo ""
        echo "3. Go to Info tab"
        echo ""
        echo "4. Under 'Configurations', click '+' button"
        echo ""
        echo "5. Select 'Duplicate Release Configuration'"
        echo ""
        echo "6. Rename the new configuration to: Release-Runner"
        echo ""
        echo "7. For the Release-Runner configuration, set:"
        echo "   - Runner: Flutter/Production"
        echo "   - Pods: Flutter/Production"
        echo ""
        echo "8. Test the build:"
        echo "   flutter build ios --release --flavor Runner --dart-define=ENVIRONMENT=production --no-codesign"
        echo ""
        print_color $GREEN "✅ After completing these steps, production builds will work!"
        ;;
    2)
        print_color $BLUE "🔧 Using Alternative Production Build Command"
        echo ""
        echo "You can build production iOS apps using the Release configuration directly:"
        echo ""
        print_color $GREEN "Alternative command:"
        echo "flutter build ios --release --dart-define=ENVIRONMENT=production --no-codesign"
        echo ""
        print_color $YELLOW "⚠️  Note: This won't use the Production.xcconfig file automatically."
        echo "The app will still load .env.production due to DART_DEFINES, but won't get"
        echo "the iOS-specific settings from Production.xcconfig."
        ;;
    3)
        print_color $BLUE "🔧 Updating Production Build Script"
        echo ""
        
        # Update production build script
        if [ -f "scripts/build_production.sh" ]; then
            # Create backup
            cp scripts/build_production.sh scripts/build_production.sh.backup
            
            # Update the iOS build command
            sed -i.tmp 's/flutter build ios --release --flavor Runner/flutter build ios --release/g' scripts/build_production.sh
            rm scripts/build_production.sh.tmp
            
            print_color $GREEN "✅ Updated build_production.sh to use Release configuration"
            echo ""
            echo "The production build script now uses:"
            echo "flutter build ios --release --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info --no-codesign"
            echo ""
            print_color $YELLOW "⚠️  Note: This won't use Production.xcconfig settings, but will still load .env.production"
        else
            print_color $RED "❌ build_production.sh not found"
        fi
        ;;
    *)
        print_color $YELLOW "Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""

print_color $BLUE "🧪 Testing Production Build..."
echo ""

# Test the production build based on the chosen solution
case $choice in
    1)
        print_color $YELLOW "⏳ Please complete the Xcode setup first, then test with:"
        echo "flutter build ios --release --flavor Runner --dart-define=ENVIRONMENT=production --no-codesign"
        ;;
    2|3)
        print_color $YELLOW "Testing alternative production build..."
        if flutter build ios --release --dart-define=ENVIRONMENT=production --no-codesign &> /dev/null; then
            print_color $GREEN "✅ Alternative production build successful!"
        else
            print_color $RED "❌ Alternative production build failed"
            print_color $YELLOW "You may need to use Solution 1 (Xcode setup)"
        fi
        ;;
esac

echo ""

print_color $BLUE "📖 Additional Resources:"
echo "- Complete setup guide: docs/IOS_ENVIRONMENT_CONFIGURATION_COMPLETE.md"
echo "- Detailed Xcode setup: docs/IOS_XCODE_SETUP.md"
echo "- Test environment loading: ./scripts/test_ios_environment_loading.sh"
echo ""

print_color $GREEN "🎉 iOS Production Build Fix Complete!"
