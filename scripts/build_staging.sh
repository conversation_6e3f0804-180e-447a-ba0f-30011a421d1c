#!/bin/bash

# Build script for Staging Environment
# This script builds the Flutter app for staging environment
# Usage: ./build_staging.sh [debug|release]
# Default: release

set -e  # Exit on any error

# Parse build type argument
BUILD_TYPE=${1:-release}

if [[ "$BUILD_TYPE" != "debug" && "$BUILD_TYPE" != "release" ]]; then
    echo "❌ Error: Invalid build type '$BUILD_TYPE'. Use 'debug' or 'release'"
    exit 1
fi

echo "🚀 Building Glidic App for Staging Environment ($BUILD_TYPE)..."

# Check if .env.staging exists
if [ ! -f ".env.staging" ]; then
    echo "❌ Error: .env.staging file not found!"
    echo "Please create .env.staging file based on .env.example"
    exit 1
fi

# Copy staging environment file to .env for build
echo "📋 Using staging environment configuration..."
cp .env.staging .env

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build for different platforms
echo "🔨 Building for staging ($BUILD_TYPE)..."

if [[ "$BUILD_TYPE" == "debug" ]]; then
    # Build for Android (Debug)
    echo "📱 Building Android APK (Debug)..."
    flutter build apk --debug --flavor staging --dart-define=ENVIRONMENT=staging

    # Build for iOS (Debug) - only on macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "🍎 Building iOS app (Debug)..."
        flutter build ios --debug --flavor staging --dart-define=ENVIRONMENT=staging --no-codesign
    else
        echo "⚠️  Skipping iOS build (not on macOS)"
    fi

    echo "✅ Staging debug build completed successfully!"
    echo "📍 Environment: Staging"
    echo "🔧 Debug Mode: Disabled (in app logic)"
    echo "📝 Logging: Enabled"
    echo "📊 Analytics: Enabled"
    echo "🔑 Signing: Debug keystore"
else
    # Check if keystore.properties exists for release builds
    if [ ! -f "android/keystore.properties" ]; then
        echo "❌ Error: android/keystore.properties file not found!"
        echo "Release builds require keystore.properties for secure signing."
        echo "Please create keystore.properties based on android/keystore.properties.example"
        exit 1
    fi

    # Build for Android (Release with production keystore)
    echo "📱 Building Android APK (Release with production keystore)..."
    flutter build apk --release --flavor staging --dart-define=ENVIRONMENT=staging

    # Build Android App Bundle for Play Store
    echo "📱 Building Android App Bundle (Release with production keystore)..."
    flutter build appbundle --release --flavor staging --dart-define=ENVIRONMENT=staging

    # Build for iOS (Release) - only on macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "🍎 Building iOS app (Release)..."
        flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging --no-codesign
    else
        echo "⚠️  Skipping iOS build (not on macOS)"
    fi

    echo "✅ Staging release build completed successfully!"
    echo "📍 Environment: Staging"
    echo "🔧 Debug Mode: Disabled"
    echo "📝 Logging: Enabled"
    echo "📊 Analytics: Enabled"
    echo "🔑 Signing: Production keystore"
    echo "✅ Note: Uses secure keystore - suitable for app store distribution"
fi
