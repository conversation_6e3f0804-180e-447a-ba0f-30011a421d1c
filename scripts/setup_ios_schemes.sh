#!/bin/bash

# iOS Schemes Setup Script
# This script provides instructions for setting up iOS build schemes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "🍎 iOS Build Schemes Setup"
echo ""

print_color $YELLOW "📋 Manual Setup Required in Xcode"
echo ""
echo "Since Xcode project files are binary, you need to manually configure the schemes in Xcode."
echo "Follow these steps:"
echo ""

print_color $GREEN "1. Open Xcode project:"
echo "   open ios/Runner.xcworkspace"
echo ""

print_color $GREEN "2. Create Build Configurations:"
echo "   a. Select the project 'Runner' in the navigator"
echo "   b. Go to 'Info' tab"
echo "   c. Under 'Configurations', duplicate existing configurations:"
echo "      - Duplicate 'Debug' → rename to 'Development'"
echo "      - Duplicate 'Release' → rename to 'Staging'"
echo "      - Duplicate 'Release' → rename to 'Production'"
echo ""

print_color $GREEN "3. Assign Configuration Files:"
echo "   For each configuration, set the configuration file:"
echo "   - Development: Flutter/Development"
echo "   - Staging: Flutter/Staging"
echo "   - Production: Flutter/Production"
echo ""

print_color $GREEN "4. Create Schemes:"
echo "   a. Go to Product → Scheme → Manage Schemes"
echo "   b. Duplicate the 'Runner' scheme 3 times:"
echo "      - Runner Development"
echo "      - Runner Staging"
echo "      - Runner Production"
echo ""

print_color $GREEN "5. Configure Each Scheme:"
echo "   For each scheme, click 'Edit Scheme' and set:"
echo ""
echo "   📱 Runner Development:"
echo "   - Build Configuration: Development"
echo "   - Run Configuration: Development"
echo "   - Archive Configuration: Development"
echo ""
echo "   📱 Runner Staging:"
echo "   - Build Configuration: Staging"
echo "   - Run Configuration: Staging"
echo "   - Archive Configuration: Staging"
echo ""
echo "   📱 Runner Production:"
echo "   - Build Configuration: Production"
echo "   - Run Configuration: Production"
echo "   - Archive Configuration: Production"
echo ""

print_color $GREEN "6. Update Build Settings (if needed):"
echo "   a. Select the 'Runner' target"
echo "   b. Go to 'Build Settings' tab"
echo "   c. Verify that the following are set correctly for each configuration:"
echo "      - Product Bundle Identifier"
echo "      - Product Name"
echo "      - Display Name"
echo ""

print_color $GREEN "7. Code Signing:"
echo "   Update the DEVELOPMENT_TEAM_ID in the .xcconfig files:"
echo "   - ios/Flutter/Development.xcconfig"
echo "   - ios/Flutter/Staging.xcconfig"
echo "   - ios/Flutter/Production.xcconfig"
echo ""
echo "   Replace 'YOUR_DEVELOPMENT_TEAM_ID' with your actual team ID."
echo ""

print_color $BLUE "🔧 Automated Configuration Check"
echo ""

# Check if configuration files exist
configs=("Development" "Staging" "Production")
for config in "${configs[@]}"; do
    if [ -f "ios/Flutter/${config}.xcconfig" ]; then
        print_color $GREEN "✅ ${config}.xcconfig exists"
    else
        print_color $RED "❌ ${config}.xcconfig missing"
    fi
done

echo ""
print_color $BLUE "📝 Next Steps:"
echo "1. Complete the manual Xcode setup above"
echo "2. Update team IDs in .xcconfig files"
echo "3. Test building with different schemes"
echo "4. Use the updated build scripts to build for each environment"
echo ""

print_color $YELLOW "💡 Tips:"
echo "- You can switch schemes in Xcode using the scheme selector"
echo "- Each scheme will use different bundle IDs and app names"
echo "- Use 'flutter build ios --flavor development' for command line builds"
echo ""

print_color $GREEN "🎉 Configuration files are ready!"
print_color $YELLOW "⚠️  Manual Xcode setup is required to complete the process."
