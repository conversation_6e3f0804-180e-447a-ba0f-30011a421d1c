#!/bin/bash

# Environment Switcher Script
# This script helps developers switch between different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Available environments:"
    echo "  dev, development    - Switch to development environment"
    echo "  staging            - Switch to staging environment"
    echo "  prod, production   - Switch to production environment"
    echo "  current            - Show current environment"
    echo "  help               - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev             # Switch to development"
    echo "  $0 staging         # Switch to staging"
    echo "  $0 production      # Switch to production"
    echo "  $0 current         # Show current environment"
}

# Function to show current environment
show_current() {
    if [ -f ".env" ]; then
        current_env=$(grep "^ENVIRONMENT=" .env | cut -d'=' -f2)
        print_color $GREEN "Current environment: $current_env"
        
        # Show some key configuration
        if [ -n "$current_env" ]; then
            base_url=$(grep "^BASE_URL=" .env | cut -d'=' -f2)
            debug_mode=$(grep "^DEBUG_MODE=" .env | cut -d'=' -f2)
            print_color $BLUE "Base URL: $base_url"
            print_color $BLUE "Debug Mode: $debug_mode"
        fi
    else
        print_color $RED "No active environment found (.env file missing)"
        print_color $YELLOW "Run: $0 dev|staging|prod to set an environment"
    fi
}

# Function to switch environment
switch_environment() {
    local env_name=$1
    local env_file=""
    
    case $env_name in
        "dev"|"development")
            env_file=".env.development"
            env_name="development"
            ;;
        "staging")
            env_file=".env.staging"
            env_name="staging"
            ;;
        "prod"|"production")
            env_file=".env.production"
            env_name="production"
            ;;
        *)
            print_color $RED "Invalid environment: $env_name"
            show_usage
            exit 1
            ;;
    esac
    
    # Check if environment file exists
    if [ ! -f "$env_file" ]; then
        print_color $RED "Environment file not found: $env_file"
        print_color $YELLOW "Please create $env_file based on .env.example"
        exit 1
    fi
    
    # Backup current .env if it exists
    if [ -f ".env" ]; then
        cp .env .env.backup
        print_color $YELLOW "Backed up current .env to .env.backup"
    fi
    
    # Copy environment file to .env
    cp "$env_file" .env
    print_color $GREEN "✅ Switched to $env_name environment"
    
    # Show current configuration
    echo ""
    show_current
    
    # Show next steps
    echo ""
    print_color $BLUE "Next steps:"
    print_color $BLUE "1. Run 'flutter pub get' to refresh dependencies"
    print_color $BLUE "2. Restart your development server/IDE"
    print_color $BLUE "3. Run 'flutter run' to test the new environment"
}

# Main script logic
case "${1:-}" in
    "dev"|"development")
        switch_environment "development"
        ;;
    "staging")
        switch_environment "staging"
        ;;
    "prod"|"production")
        switch_environment "production"
        ;;
    "current")
        show_current
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    "")
        print_color $YELLOW "No environment specified."
        echo ""
        show_current
        echo ""
        show_usage
        ;;
    *)
        print_color $RED "Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
