#!/bin/bash

# Fix iOS Schemes Script
# This script fixes the iOS scheme naming to match Android flavors

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "🔧 Fixing iOS Scheme Names"
echo ""

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_color $RED "❌ Error: Not in Flutter project root directory"
    exit 1
fi

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    print_color $RED "❌ Error: iOS directory not found"
    exit 1
fi

print_color $YELLOW "📋 Current iOS Schemes:"
cd ios
if command -v xcodebuild &> /dev/null; then
    xcodebuild -list -workspace Runner.xcworkspace | grep -A 20 "Schemes:" | grep -E "(development|staging|production|Runner)" || true
else
    print_color $YELLOW "⚠️  xcodebuild not available"
fi
cd ..

echo ""

print_color $YELLOW "🔍 Testing Current Setup..."
echo ""

# Test if we can build with current scheme names
print_color $YELLOW "Testing development build with 'development' scheme..."
if flutter build ios --debug --flavor development --dart-define=ENVIRONMENT=development --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Development build works with 'development' scheme"
else
    print_color $RED "❌ Development build failed with 'development' scheme"
fi

print_color $YELLOW "Testing staging build with 'staging' scheme..."
if flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Staging build works with 'staging' scheme"
else
    print_color $RED "❌ Staging build failed with 'staging' scheme"
fi

print_color $YELLOW "Testing production build with 'production' scheme..."
if flutter build ios --release --flavor production --dart-define=ENVIRONMENT=production --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Production build works with 'production' scheme"
else
    print_color $RED "❌ Production build failed with 'production' scheme"
fi

echo ""

print_color $BLUE "📝 Recommendation:"
echo ""
echo "Option 1 (Preferred): Rename schemes in Xcode to match Android flavors"
echo "  - Provides consistent naming across platforms"
echo "  - Matches existing build scripts"
echo ""
echo "Option 2 (Quick fix): Update build scripts to use current scheme names"
echo "  - Faster to implement"
echo "  - Maintains current Xcode setup"
echo ""

print_color $YELLOW "Which option would you prefer?"
echo "1) Rename schemes in Xcode (recommended)"
echo "2) Update build scripts to use current scheme names"
echo ""

read -p "Enter your choice (1 or 2): " choice

case $choice in
    1)
        print_color $BLUE "📖 Please follow the manual steps above to rename schemes in Xcode"
        print_color $YELLOW "After renaming, run: ./scripts/verify_ios_setup.sh"
        ;;
    2)
        print_color $BLUE "🔧 Updating build scripts to use current scheme names..."
        
        # Update build scripts
        if [ -f "scripts/build_dev.sh" ]; then
            sed -i.bak 's/--flavor development/--scheme development/g' scripts/build_dev.sh
            print_color $GREEN "✅ Updated build_dev.sh"
        fi
        
        if [ -f "scripts/build_staging.sh" ]; then
            sed -i.bak 's/--flavor staging/--scheme staging/g' scripts/build_staging.sh
            print_color $GREEN "✅ Updated build_staging.sh"
        fi
        
        if [ -f "scripts/build_production.sh" ]; then
            sed -i.bak 's/--flavor production/--scheme production/g' scripts/build_production.sh
            print_color $GREEN "✅ Updated build_production.sh"
        fi
        
        print_color $GREEN "🎉 Build scripts updated to use current scheme names!"
        print_color $YELLOW "Test with: ./scripts/build_dev.sh"
        ;;
    *)
        print_color $YELLOW "Invalid choice. Please run the script again."
        ;;
esac

echo ""
print_color $BLUE "📖 For detailed iOS setup instructions, see: docs/IOS_XCODE_SETUP.md"
