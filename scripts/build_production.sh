#!/bin/bash

# Build script for Production Environment
# This script builds the Flutter app for production environment

set -e  # Exit on any error

echo "🚀 Building Glidic App for Production Environment..."

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo "❌ Error: .env.production file not found!"
    echo "Please create .env.production file based on .env.example"
    echo "⚠️  IMPORTANT: Ensure all production secrets are properly configured!"
    exit 1
fi

# Check if keystore.properties exists for production builds
if [ ! -f "android/keystore.properties" ]; then
    echo "❌ Error: android/keystore.properties file not found!"
    echo "Production builds require proper keystore configuration."
    echo ""
    echo "To fix this:"
    echo "1. Copy android/keystore.properties.example to android/keystore.properties"
    echo "2. Generate a production keystore using keytool"
    echo "3. Update keystore.properties with your keystore details"
    echo ""
    echo "See docs/ANDROID_RELEASE_BUILD_GUIDE.md for detailed instructions"
    exit 1
fi

# Validate production environment
echo "🔍 Validating production configuration..."
if grep -q "CHANGE_THIS" .env.production; then
    echo "❌ Error: Production environment contains placeholder values!"
    echo "Please update all 'CHANGE_THIS' values in .env.production"
    exit 1
fi

# Validate keystore configuration
echo "🔐 Validating keystore configuration..."
if ! grep -q "storeFile=" android/keystore.properties || ! grep -q "storePassword=" android/keystore.properties; then
    echo "❌ Error: Invalid keystore.properties configuration!"
    echo "Please ensure keystore.properties contains all required fields:"
    echo "- storeFile"
    echo "- storePassword"
    echo "- keyAlias"
    echo "- keyPassword"
    exit 1
fi

# Check if keystore file exists
KEYSTORE_FILE=$(grep "storeFile=" android/keystore.properties | cut -d'=' -f2)
if [ ! -f "android/app/$KEYSTORE_FILE" ] && [ ! -f "android/$KEYSTORE_FILE" ] && [ ! -f "$KEYSTORE_FILE" ]; then
    echo "❌ Error: Keystore file not found!"
    echo "Keystore file specified in keystore.properties: $KEYSTORE_FILE"
    echo "Please ensure the keystore file exists and the path is correct."
    exit 1
fi

# Copy production environment file to .env for build
echo "📋 Using production environment configuration..."
cp .env.production .env

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build for different platforms
echo "🔨 Building for production..."

# Build for Android (Release)
echo "📱 Building Android APK (Release)..."
flutter build apk --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info

# Build Android App Bundle for Play Store
echo "📱 Building Android App Bundle..."
flutter build appbundle --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info

# Build for iOS (Release) - only on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Building iOS app (Release)..."
    # Use Release configuration directly (works without additional Xcode setup)
    # Environment detection still works via DART_DEFINES -> loads .env.production
    flutter build ios --release --flavor production --dart-define=ENVIRONMENT=production --obfuscate --split-debug-info=build/debug-info --no-codesign
else
    echo "⚠️  Skipping iOS build (not on macOS)"
fi

echo "✅ Production build completed successfully!"
echo ""
echo "📋 Build Summary:"
echo "📍 Environment: Production"
echo "🔧 Debug Mode: Disabled"
echo "📝 Logging: Disabled"
echo "📊 Analytics: Enabled"
echo "🔒 Code Obfuscation: Enabled"
echo "🔐 Signed with Production Keystore: ✅"
echo ""
echo "📦 Output Files:"
echo "📱 APK: build/app/outputs/flutter-apk/app-production-release.apk"
echo "📦 App Bundle: build/app/outputs/bundle/productionRelease/app-production-release.aab"
echo ""
echo "🚀 Next Steps:"
echo "1. Test the release build on multiple devices"
echo "2. Upload app-production-release.aab to Google Play Console"
echo "3. Complete Play Store listing and submit for review"
echo ""
echo "⚠️  Remember to test thoroughly before deployment!"
echo "📖 See docs/ANDROID_RELEASE_BUILD_GUIDE.md for detailed submission guide"
