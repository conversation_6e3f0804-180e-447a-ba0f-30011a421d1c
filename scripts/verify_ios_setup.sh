#!/bin/bash

# iOS Setup Verification Script
# This script verifies that the iOS multi-environment setup is correctly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

print_color $BLUE "🔍 iOS Multi-Environment Setup Verification"
echo ""

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_color $RED "❌ Error: Not in Flutter project root directory"
    exit 1
fi

# Check if iOS directory exists
if [ ! -d "ios" ]; then
    print_color $RED "❌ Error: iOS directory not found"
    exit 1
fi

print_color $YELLOW "📋 Checking Configuration Files..."
echo ""

# Check .xcconfig files
configs=("Development" "Staging" "Production")
config_files_exist=true

for config in "${configs[@]}"; do
    config_file="ios/Flutter/${config}.xcconfig"
    if [ -f "$config_file" ]; then
        print_color $GREEN "✅ ${config}.xcconfig exists"
        
        # Check if Team ID is configured
        if grep -q "YOUR_DEVELOPMENT_TEAM_ID" "$config_file"; then
            print_color $YELLOW "⚠️  ${config}.xcconfig still contains placeholder Team ID"
        else
            print_color $GREEN "✅ ${config}.xcconfig has Team ID configured"
        fi
    else
        print_color $RED "❌ ${config}.xcconfig missing"
        config_files_exist=false
    fi
done

echo ""

# Check if Xcode workspace exists
if [ -d "ios/Runner.xcworkspace" ]; then
    print_color $GREEN "✅ Runner.xcworkspace exists"
else
    print_color $RED "❌ Runner.xcworkspace not found"
fi

echo ""

# Check for scheme files (these are created manually in Xcode)
print_color $YELLOW "📋 Checking Scheme Files (created manually in Xcode)..."
echo ""

scheme_dir="ios/Runner.xcodeproj/xcshareddata/xcschemes"
if [ -d "$scheme_dir" ]; then
    schemes=("Runner Development" "Runner Staging" "Runner Production")
    
    for scheme in "${schemes[@]}"; do
        scheme_file="${scheme_dir}/${scheme}.xcscheme"
        if [ -f "$scheme_file" ]; then
            print_color $GREEN "✅ ${scheme}.xcscheme exists"
        else
            print_color $YELLOW "⚠️  ${scheme}.xcscheme not found (needs manual Xcode setup)"
        fi
    done
else
    print_color $YELLOW "⚠️  Schemes directory not found (needs manual Xcode setup)"
fi

echo ""

# Check if xcodebuild is available
if command -v xcodebuild &> /dev/null; then
    print_color $YELLOW "📋 Checking Available Schemes..."
    echo ""
    
    cd ios
    if xcodebuild -list -workspace Runner.xcworkspace &> /dev/null; then
        print_color $GREEN "✅ Xcode workspace is valid"
        
        # List schemes
        echo "Available schemes:"
        xcodebuild -list -workspace Runner.xcworkspace | grep -A 10 "Schemes:" | tail -n +2 | while read -r line; do
            if [[ -n "$line" ]]; then
                echo "  - $line"
            fi
        done
    else
        print_color $YELLOW "⚠️  Cannot read Xcode workspace (may need manual setup)"
    fi
    cd ..
else
    print_color $YELLOW "⚠️  xcodebuild not available (Xcode not installed or not in PATH)"
fi

echo ""

# Test Flutter build commands
print_color $YELLOW "📋 Testing Flutter Build Commands..."
echo ""

# Test development build (should work even without custom schemes)
print_color $BLUE "Testing development build..."
if flutter build ios --debug --flavor development --dart-define=ENVIRONMENT=development --no-codesign &> /dev/null; then
    print_color $GREEN "✅ Development build command works"
else
    print_color $RED "❌ Development build command failed"
fi

echo ""

# Summary
print_color $BLUE "📊 Setup Summary"
echo ""

if [ "$config_files_exist" = true ]; then
    print_color $GREEN "✅ Configuration files are ready"
else
    print_color $RED "❌ Some configuration files are missing"
fi

# Check if manual setup is needed
manual_setup_needed=false

if [ ! -f "${scheme_dir}/Runner Development.xcscheme" ] || 
   [ ! -f "${scheme_dir}/Runner Staging.xcscheme" ] || 
   [ ! -f "${scheme_dir}/Runner Production.xcscheme" ]; then
    manual_setup_needed=true
fi

if [ "$manual_setup_needed" = true ]; then
    print_color $YELLOW "⚠️  Manual Xcode setup is required"
    echo ""
    print_color $BLUE "📋 Next Steps:"
    echo "1. Open Xcode: open ios/Runner.xcworkspace"
    echo "2. Follow the detailed guide: docs/IOS_XCODE_SETUP.md"
    echo "3. Create build configurations and schemes manually"
    echo "4. Update Team IDs in .xcconfig files"
    echo "5. Run this script again to verify"
else
    print_color $GREEN "🎉 iOS setup appears to be complete!"
    echo ""
    print_color $BLUE "📋 You can now:"
    echo "1. Build using: ./scripts/build_dev.sh"
    echo "2. Build using: ./scripts/build_staging.sh"
    echo "3. Build using: ./scripts/build_production.sh"
    echo "4. Use Xcode schemes for development"
fi

echo ""

# Check Team ID configuration
team_id_configured=true
for config in "${configs[@]}"; do
    config_file="ios/Flutter/${config}.xcconfig"
    if [ -f "$config_file" ] && grep -q "YOUR_DEVELOPMENT_TEAM_ID" "$config_file"; then
        team_id_configured=false
        break
    fi
done

if [ "$team_id_configured" = false ]; then
    print_color $YELLOW "⚠️  Remember to update Team IDs in .xcconfig files for code signing"
    echo ""
    echo "Replace 'YOUR_DEVELOPMENT_TEAM_ID' with your actual Apple Developer Team ID in:"
    for config in "${configs[@]}"; do
        echo "  - ios/Flutter/${config}.xcconfig"
    done
fi

echo ""
print_color $BLUE "📖 For detailed setup instructions, see: docs/IOS_XCODE_SETUP.md"
