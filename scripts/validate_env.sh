#!/bin/bash

# Environment Validation Script
# This script validates environment configuration files

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Required environment variables
REQUIRED_VARS=(
    "ENVIRONMENT"
    "BASE_URL"
    "API_VERSION"
)

# Sensitive variables that should not contain placeholder values
SENSITIVE_VARS=(
    "JWT_SECRET"
    "OAUTH_CLIENT_SECRET"
    "DATABASE_URL"
    "FIREBASE_API_KEY"
    "GOOGLE_MAPS_API_KEY"
)

# Function to validate a single environment file
validate_env_file() {
    local env_file=$1
    local env_name=$2
    local errors=0
    local warnings=0
    
    print_color $BLUE "🔍 Validating $env_name environment ($env_file)..."
    
    # Check if file exists
    if [ ! -f "$env_file" ]; then
        print_color $RED "❌ File not found: $env_file"
        return 1
    fi
    
    # Check required variables
    for var in "${REQUIRED_VARS[@]}"; do
        if ! grep -q "^${var}=" "$env_file"; then
            print_color $RED "❌ Missing required variable: $var"
            ((errors++))
        elif [ -z "$(grep "^${var}=" "$env_file" | cut -d'=' -f2)" ]; then
            print_color $RED "❌ Empty required variable: $var"
            ((errors++))
        else
            print_color $GREEN "✅ $var is set"
        fi
    done
    
    # Check for placeholder values in sensitive variables
    if [ "$env_name" = "production" ]; then
        for var in "${SENSITIVE_VARS[@]}"; do
            if grep -q "^${var}=" "$env_file"; then
                value=$(grep "^${var}=" "$env_file" | cut -d'=' -f2)
                if [[ "$value" == *"CHANGE_THIS"* ]] || [[ "$value" == *"your_"* ]] || [[ "$value" == *"_here"* ]]; then
                    print_color $RED "❌ Production variable contains placeholder: $var"
                    ((errors++))
                fi
            fi
        done
    fi
    
    # Check environment-specific settings
    case $env_name in
        "development")
            # Development should have debug mode enabled
            debug_mode=$(grep "^DEBUG_MODE=" "$env_file" | cut -d'=' -f2)
            if [ "$debug_mode" != "true" ]; then
                print_color $YELLOW "⚠️  Development environment should have DEBUG_MODE=true"
                ((warnings++))
            fi
            ;;
        "production")
            # Production should have debug mode disabled
            debug_mode=$(grep "^DEBUG_MODE=" "$env_file" | cut -d'=' -f2)
            if [ "$debug_mode" = "true" ]; then
                print_color $RED "❌ Production environment should have DEBUG_MODE=false"
                ((errors++))
            fi
            
            # Production should have logging disabled
            logging=$(grep "^ENABLE_LOGGING=" "$env_file" | cut -d'=' -f2)
            if [ "$logging" = "true" ]; then
                print_color $YELLOW "⚠️  Consider disabling logging in production (ENABLE_LOGGING=false)"
                ((warnings++))
            fi
            ;;
    esac
    
    # Summary
    if [ $errors -eq 0 ] && [ $warnings -eq 0 ]; then
        print_color $GREEN "✅ $env_name environment validation passed"
    elif [ $errors -eq 0 ]; then
        print_color $YELLOW "⚠️  $env_name environment validation passed with $warnings warning(s)"
    else
        print_color $RED "❌ $env_name environment validation failed with $errors error(s) and $warnings warning(s)"
    fi
    
    echo ""
    return $errors
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment]"
    echo ""
    echo "Available options:"
    echo "  dev, development    - Validate development environment"
    echo "  staging            - Validate staging environment"
    echo "  prod, production   - Validate production environment"
    echo "  all                - Validate all environments"
    echo "  current            - Validate current active environment"
    echo "  help               - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 all             # Validate all environments"
    echo "  $0 production      # Validate production environment"
    echo "  $0 current         # Validate current environment"
}

# Main script logic
case "${1:-all}" in
    "dev"|"development")
        validate_env_file ".env.development" "development"
        ;;
    "staging")
        validate_env_file ".env.staging" "staging"
        ;;
    "prod"|"production")
        validate_env_file ".env.production" "production"
        ;;
    "current")
        if [ -f ".env" ]; then
            current_env=$(grep "^ENVIRONMENT=" .env | cut -d'=' -f2)
            validate_env_file ".env" "current ($current_env)"
        else
            print_color $RED "❌ No active environment found (.env file missing)"
            exit 1
        fi
        ;;
    "all")
        print_color $BLUE "🔍 Validating all environment configurations..."
        echo ""
        
        total_errors=0
        
        # Validate development
        if validate_env_file ".env.development" "development"; then
            :
        else
            ((total_errors++))
        fi
        
        # Validate staging
        if validate_env_file ".env.staging" "staging"; then
            :
        else
            ((total_errors++))
        fi
        
        # Validate production
        if validate_env_file ".env.production" "production"; then
            :
        else
            ((total_errors++))
        fi
        
        # Overall summary
        if [ $total_errors -eq 0 ]; then
            print_color $GREEN "🎉 All environment validations passed!"
        else
            print_color $RED "❌ $total_errors environment(s) failed validation"
            exit 1
        fi
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_color $RED "Unknown option: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
