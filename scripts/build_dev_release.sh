#!/bin/bash

# Build script for Development Environment (Release Mode)
# This script builds release versions of the Flutter app for development environment
# Uses production keystore for signing (suitable for app store distribution)

set -e  # Exit on any error

echo "🚀 Building Glidic App for Development Environment (Release Mode)..."

# Check if keystore.properties exists
if [ ! -f "android/keystore.properties" ]; then
    echo "❌ Error: android/keystore.properties file not found!"
    echo "Release builds require keystore.properties for secure signing."
    echo "Please create keystore.properties based on android/keystore.properties.example"
    exit 1
fi

# Check if .env.development exists
if [ ! -f ".env.development" ]; then
    echo "❌ Error: .env.development file not found!"
    echo "Please create .env.development file based on .env.example"
    exit 1
fi

# Copy development environment file to .env for build
echo "📋 Using development environment configuration..."
cp .env.development .env

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build for different platforms
echo "🔨 Building release version for development environment..."

# Build for Android (Release with production keystore)
echo "📱 Building Android APK (Release with production keystore)..."
flutter build apk --release --flavor development --dart-define=ENVIRONMENT=development

echo "📱 Building Android App Bundle (Release with production keystore)..."
flutter build appbundle --release --flavor development --dart-define=ENVIRONMENT=development

# Build for iOS (Release) - only on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Building iOS app (Release)..."
    flutter build ios --release --flavor development --dart-define=ENVIRONMENT=development --no-codesign
else
    echo "⚠️  Skipping iOS build (not on macOS)"
fi

echo "✅ Development release build completed successfully!"
echo ""
echo "📋 Build Summary:"
echo "📍 Environment: Development"
echo "🏷️  Application ID: jp.co.iotbank.glidici.dev"
echo "📱 App Name: Glidic (Dev)"
echo "🔧 Debug Mode: Enabled (in app logic)"
echo "📝 Logging: Enabled"
echo "🔑 Signing: Production keystore"
echo "✅ Note: Uses secure keystore - suitable for app store distribution"
echo ""
echo "📁 Output files:"
echo "   APK: build/app/outputs/flutter-apk/app-development-release.apk"
echo "   Bundle: build/app/outputs/bundle/developmentRelease/app-development-release.aab"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "   iOS: build/ios/iphoneos/Runner.app"
fi
echo ""
echo "🚀 Flutter commands used:"
echo "   flutter build apk --release --flavor development --dart-define=ENVIRONMENT=development"
echo "   flutter build appbundle --release --flavor development --dart-define=ENVIRONMENT=development"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "   flutter build ios --release --flavor development --dart-define=ENVIRONMENT=development --no-codesign"
fi
