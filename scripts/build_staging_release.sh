#!/bin/bash

# Build script for Staging Environment (Release Mode)
# This script builds release versions of the Flutter app for staging environment
# Uses production keystore for signing (suitable for app store distribution)

set -e  # Exit on any error

echo "🚀 Building Glidic App for Staging Environment (Release Mode)..."

# Check if keystore.properties exists
if [ ! -f "android/keystore.properties" ]; then
    echo "❌ Error: android/keystore.properties file not found!"
    echo "Release builds require keystore.properties for secure signing."
    echo "Please create keystore.properties based on android/keystore.properties.example"
    exit 1
fi

# Check if .env.staging exists
if [ ! -f ".env.staging" ]; then
    echo "❌ Error: .env.staging file not found!"
    echo "Please create .env.staging file based on .env.example"
    exit 1
fi

# Copy staging environment file to .env for build
echo "📋 Using staging environment configuration..."
cp .env.staging .env

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Build for different platforms
echo "🔨 Building release version for staging environment..."

# Build for Android (Release with production keystore)
echo "📱 Building Android APK (Release with production keystore)..."
flutter build apk --release --flavor staging --dart-define=ENVIRONMENT=staging

echo "📱 Building Android App Bundle (Release with production keystore)..."
flutter build appbundle --release --flavor staging --dart-define=ENVIRONMENT=staging

# Build for iOS (Release) - only on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Building iOS app (Release)..."
    flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging --no-codesign
else
    echo "⚠️  Skipping iOS build (not on macOS)"
fi

echo "✅ Staging release build completed successfully!"
echo ""
echo "📋 Build Summary:"
echo "📍 Environment: Staging"
echo "🏷️  Application ID: jp.co.iotbank.glidici.staging"
echo "📱 App Name: Glidic (Staging)"
echo "🔧 Debug Mode: Disabled"
echo "📝 Logging: Enabled"
echo "📊 Analytics: Enabled"
echo "🔑 Signing: Production keystore"
echo "✅ Note: Uses secure keystore - suitable for app store distribution"
echo ""
echo "📁 Output files:"
echo "   APK: build/app/outputs/flutter-apk/app-staging-release.apk"
echo "   Bundle: build/app/outputs/bundle/stagingRelease/app-staging-release.aab"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "   iOS: build/ios/iphoneos/Runner.app"
fi
echo ""
echo "🚀 Flutter commands used:"
echo "   flutter build apk --release --flavor staging --dart-define=ENVIRONMENT=staging"
echo "   flutter build appbundle --release --flavor staging --dart-define=ENVIRONMENT=staging"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "   flutter build ios --release --flavor staging --dart-define=ENVIRONMENT=staging --no-codesign"
fi
