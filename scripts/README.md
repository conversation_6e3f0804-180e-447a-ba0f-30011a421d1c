# Build and Environment Scripts

This directory contains utility scripts for managing environments and building the Glidic Flutter application.

## Available Scripts

### Environment Management

#### `switch_env.sh`
Switch between different environments during development.

```bash
# Switch to development environment
./scripts/switch_env.sh dev

# Switch to staging environment
./scripts/switch_env.sh staging

# Switch to production environment
./scripts/switch_env.sh prod

# Show current environment
./scripts/switch_env.sh current
```

#### `validate_env.sh`
Validate environment configuration files.

```bash
# Validate all environments
./scripts/validate_env.sh all

# Validate specific environment
./scripts/validate_env.sh production

# Validate current active environment
./scripts/validate_env.sh current
```

### Build Scripts

#### `build_dev.sh`
Build the application for development environment.

```bash
# Debug build (default)
./scripts/build_dev.sh

# Release build (requires keystore.properties)
./scripts/build_dev.sh release
```

Features:
- Uses `.env.development` configuration
- Debug builds: Uses debug keystore, enables debug features
- Release builds: Uses production keystore, suitable for app store distribution
- Application ID: `jp.co.iotbank.glidici.dev`
- App Name: "Glidic (Dev)"

#### `build_staging.sh`
Build the application for staging environment.

```bash
# Debug build
./scripts/build_staging.sh debug

# Release build (default, requires keystore.properties)
./scripts/build_staging.sh
```

Features:
- Uses `.env.staging` configuration
- Debug builds: Uses debug keystore
- Release builds: Uses production keystore, suitable for app store distribution
- Application ID: `jp.co.iotbank.glidici.staging`
- App Name: "Glidic (Staging)"
- Enables analytics and monitoring

#### `build_production.sh`
Build the application for production environment.

```bash
./scripts/build_production.sh
```

Features:
- Uses `.env.production` configuration
- Validates production configuration
- Builds obfuscated release builds
- Uses production keystore for secure signing
- Application ID: `jp.co.iotbank.glidici`
- App Name: "Glidic"

#### `build_dev_release.sh`
Dedicated script for building development release versions.

```bash
./scripts/build_dev_release.sh
```

Features:
- Builds release APK and App Bundle for development environment
- Uses production keystore for secure signing
- Suitable for app store distribution
- Requires `android/keystore.properties`

#### `build_staging_release.sh`
Dedicated script for building staging release versions.

```bash
./scripts/build_staging_release.sh
```

Features:
- Builds release APK and App Bundle for staging environment
- Uses production keystore for secure signing
- Suitable for app store distribution
- Requires `android/keystore.properties`
- Generates debug info for crash reporting

## Prerequisites

### Required Tools
- Flutter SDK
- Bash shell (Linux/macOS/WSL)
- Git (for version control)

### Environment Files
Before using the scripts, ensure you have created the required environment files:

```bash
# Copy example file to create environment files
cp .env.example .env.development
cp .env.example .env.staging
cp .env.example .env.production
```

Then edit each file with appropriate values for each environment.

## Usage Examples

### Development Workflow
```bash
# 1. Switch to development environment
./scripts/switch_env.sh dev

# 2. Validate configuration
./scripts/validate_env.sh current

# 3. Build for development
./scripts/build_dev.sh

# 4. Run the app
flutter run
```

### Staging Deployment
```bash
# 1. Switch to staging environment
./scripts/switch_env.sh staging

# 2. Validate configuration
./scripts/validate_env.sh staging

# 3. Build for staging
./scripts/build_staging.sh

# 4. Deploy to staging environment
# (deployment commands depend on your CI/CD setup)
```

### Production Release
```bash
# 1. Validate all environments
./scripts/validate_env.sh all

# 2. Switch to production environment
./scripts/switch_env.sh prod

# 3. Build for production
./scripts/build_production.sh

# 4. Deploy to production
# (deployment commands depend on your CI/CD setup)
```

## Script Features

### Error Handling
- All scripts use `set -e` to exit on any error
- Comprehensive error messages and validation
- Colored output for better readability

### Security
- Production builds include validation for placeholder values
- Environment files are never committed to version control
- Obfuscation enabled for production builds

### Cross-Platform Support
- Scripts work on Linux, macOS, and WSL
- iOS builds are automatically skipped on non-macOS systems
- Proper path handling for different operating systems

## Troubleshooting

### Common Issues

#### "Permission denied" error
```bash
# Make scripts executable
chmod +x scripts/*.sh
```

#### "Environment file not found"
```bash
# Create environment files from example
cp .env.example .env.development
cp .env.example .env.staging
cp .env.example .env.production
```

#### "Flutter command not found"
```bash
# Ensure Flutter is in your PATH
export PATH="$PATH:/path/to/flutter/bin"
```

### Getting Help
```bash
# Show help for any script
./scripts/switch_env.sh help
./scripts/validate_env.sh help
```

## Integration with CI/CD

These scripts can be integrated into your CI/CD pipeline:

### GitHub Actions Example
```yaml
- name: Validate Environment
  run: ./scripts/validate_env.sh production

- name: Build Production
  run: ./scripts/build_production.sh
```

### GitLab CI Example
```yaml
build_production:
  script:
    - ./scripts/validate_env.sh production
    - ./scripts/build_production.sh
```

## Contributing

When adding new scripts:
1. Follow the existing naming convention
2. Include proper error handling
3. Add colored output for better UX
4. Update this README file
5. Make scripts executable (`chmod +x`)
6. Test on multiple platforms if possible
