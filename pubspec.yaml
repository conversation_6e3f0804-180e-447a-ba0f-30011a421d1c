name: glidic_app
description: "Glidic App - A modern Flutter application built with Clean Architecture."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.1
  get_it: ^8.0.3
  dio: ^5.8.0+1
  equatable: ^2.0.7
  connectivity_plus: ^6.1.4
  intl: ^0.20.2
  json_annotation: ^4.9.0
  retrofit: ^4.5.0
  logger: ^2.6.0
  drift: ^2.26.1
  flutter_secure_storage: ^9.2.4
  shared_preferences: ^2.5.3
  path: ^1.9.0
  path_provider: ^2.1.4
  sqlite3: ^2.4.6
  sqlite3_flutter_libs: ^0.5.34
  flutter_dotenv: ^5.2.1
  auto_route: ^10.1.0+1
  flutter_svg: ^2.2.0
  device_calendar: ^4.3.3
  permission_handler: ^12.0.1
  googleapis: ^14.0.0
  google_sign_in: ^7.1.1
  extension_google_sign_in_as_googleapis_auth: ^3.0.0

  # Audio recording and waveform dependencies
  audio_waveforms: ^1.1.0

  # Timer and duration utilities
  stop_watch_timer: ^3.2.0

  # Functional programming utilities
  dartz: ^0.10.1

  # UUID generation
  uuid: ^4.5.1

  # File sharing
  share_plus: ^10.1.2

  # Live Activities (iOS only)
  live_activities: ^2.4.1
  shared_preference_app_group: ^1.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  auto_route_generator: ^10.1.0


  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  mockito: ^5.4.5
  build_runner: ^2.4.15
  bloc_test: ^10.0.0
  json_serializable: ^6.8.0
  retrofit_generator: ^9.3.0
  # Drift database code generation
  drift_dev: ^2.26.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable code generation for localization
  generate: true

  # Assets for the application
  assets:
    # Environment configuration files
    - .env
    - .env.development
    - .env.staging
    - .env.production
    # Application assets
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
